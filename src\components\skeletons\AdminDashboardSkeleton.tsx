const AdminDashboardSkeleton = () => {
  return (
    <div className="md:mt-0 mt-[60px] max-w-[1240px] sm:px-[30px] px-[10px] mx-auto py-6">
      {/* Stats Overview Skeleton - 3x2 grid layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="bg-white p-6 rounded-lg border border-[#E5E7EB] shadow-sm">
            <div className="flex items-center justify-between">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-16"></div>
              </div>
              <div className="w-12 h-12 bg-gray-200 rounded-lg animate-pulse"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AdminDashboardSkeleton;