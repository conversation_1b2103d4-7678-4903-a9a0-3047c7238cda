import { useEffect, useState } from "react";
import { useTourTags } from '../../utils/useTourTags';
import TourTagChips from '../../components/common/TourTagChips';
import BackIcon from "../../assets/back-arrow.svg";
import StarIcon from "../../assets/star-icon.svg";
import Inclusion from "../../components/dashboard/planDetails/Inclusion";
import Overview from "../../components/dashboard/planDetails/Overview";
import Itinerary from "../../components/dashboard/planDetails/Itinerary";
import TermContact from "../../components/dashboard/planDetails/TermContact";
import Reviews from "../../components/dashboard/planDetails/Reviews";
import ContactUsModal from "../../components/dashboard/payments/ContactUsModal";
// import SignUpModal from "../../components/chat/SignUpModal";
// import SignInModal from "../../components/chat/SignInModal";
import { planOptions } from "../../utils/dummyData";
import ImageGrid from "../../components/dashboard/planDetails/ImageGrid";
import PriceCard from "../../components/dashboard/planDetails/PriceCard";
import { useParams, useNavigate } from "react-router-dom";
import { useGetPackagesByDestinationQuery } from "../../store/features/tours/toursApi";
import { useDispatch } from "react-redux";
import { setTourDetails } from "../../store/features/tours/toursSlice";
import locationSvg from "../../assets/location-icon.svg";
import { useGetReviewsQuery } from "../../store/features/reviews/reviews";
import { setReviews } from "../../store/features/tours/toursSlice";

const Loader = () => (
  <div className="flex flex-col items-center justify-center py-20">
    <div className="w-16 h-16 rounded-full border-4 border-[#E7ECF9] border-t-[#0D3FC6] animate-spin"></div>
    <div className="mt-6 text-gray-600 font-medium text-lg">Loading tour details...</div>
  </div>
);

const PlanDetails = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const { tour_id } = useParams();
  const { tags, loading: tagsLoading } = useTourTags(tour_id || '');
  const { data: reviews } = useGetReviewsQuery(tour_id);
  const { data: tourDetails, isLoading } = useGetPackagesByDestinationQuery(tour_id);
  // const { threadUid } = useParams();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [tourDetailsData, setTourDetailsData] = useState<any>(null);
  const dispatch = useDispatch();
  dispatch(setReviews(reviews));
  const [showContactModal, setShowContactModal] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    if (tourDetails) {
      setTourDetailsData(tourDetails.data);
      dispatch(setTourDetails(tourDetails.data));
    }
  }, [tourDetails, dispatch]);

  const handleGoBack = () => {
    // navigate(`/chat/${threadUid}`);
    navigate(-1)
  };

  return (
    <div className="flex">
      <div className="md:ms-[275px] h-screen overflow-y-auto flex-1">
        <div className="py-[15px] sm:py-[44px] max-w-[1075px] mx-auto sm:px-[20px] px-[10px]">
          <div className="text-end fixed sm:block top-0 pt-2.5 pr-2.5 z-20 right-0 w-full bg-white">
            <button
              onClick={handleGoBack}
              className="inline-flex cursor-pointer items-center bg-[#E7ECF9] rounded-[8px] text-[16px] leading-[24px] font-normal text-[#05073C] mb-[8px] gap-[5px] px-[18px] py-[10px]"
            >
              <img src={BackIcon} alt="Back" className="" /> Back
            </button>
          </div>

          {isLoading ? (
            <Loader />
          ) : tourDetailsData ? (
            <>
              <ImageGrid images={tourDetailsData?.tour_gallery_images} />
              <div>
                {/* Location and Package Info */}
                <div className="lg:px-[18px] px-[10px] mt-6">
                  <div className="flex items-center lg:mb-[24px] mb-[15px] justify-between">
                    <div className="flex items-center text-[15px] gap-1 leading-[20px] font-normal text-[#4B5563]">
                      <img src={locationSvg} alt="Location" className="w-[14px] h-[14px] " />
                      {tourDetailsData?.location?.address || "Location not available"}
                      {tourDetailsData?.featured && (
                        <div className="text-[14px] leading-[20px] font-normal bg-[#1249CC] text-white px-2 py-0.5 rounded-full ml-2">
                          <div className="flex items-center gap-1">
                            <span>Verified</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-start justify-between mb-[30px] flex-col lg:flex-row gap-3 lg:gap-0">
                    <div className="">
                      <h1 className="text-[20px] sm:text-[24px] lg:text-[34px] md:leading-[46px] leading-[24px] sm:leading-[30px] font-bold text-[#05073C] mb-[8px]">
                        {tourDetailsData?.tour_title || "Title not available"}
                      </h1>
                      {/* Tour Tags as Chips */}
                      <div className="mb-[8px]">
                        {tagsLoading ? <span className="text-xs text-gray-400">Loading tags...</span> : <TourTagChips tags={tags} />}
                      </div>
                      <div className="flex items-center gap-3 mt-2">
                        <div className="flex-1 flex items-center">
                          <div className="flex items-center text-[14px] leading-[20px] font-normal text-[#05073C]">
                            <img
                              src={StarIcon}
                              alt="Star"
                              className="w-[14px] h-[14px] mr-1 mb-0.5"
                            />
                            <span className="text-md">
                              {typeof reviews?.overall_rating?.average === "number"
                                ? reviews.overall_rating.average.toFixed(2)
                                : "Rating not available"} ({reviews?.total_reviews || "Rating not available"})
                            </span>
                          </div>
                          <div className="flex-1"></div>
                          {tour_id && (
                            <div className="text-[13px] text-[#1249CC] font-semibold ml-auto whitespace-nowrap pr-4">
                            SKU: TB-{tour_id}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <PriceCard
                      price={tourDetailsData?.adult_price}
                      duration={tourDetailsData?.duration}
                      onContactClick={() => setShowContactModal(true)}
                      tourTitle={tourDetailsData?.tour_title || "Title not available"}
                      tourId={tour_id}
                      booking_url={tourDetailsData?.booking_url}
                      groupSize={tourDetailsData?.group_size}
                    />
                  </div>

                  {/* Price and Booking Info */}
                  <div className="">
                    <div className="">
                      {/* Tabs */}
                      <div className="border-b border-gray-200">
                        <div className="flex justify-between sm:justify-start sm:gap-[10px] lg:gap-[30px] items-center flex-wrap sm:flex-nowrap">
                          {planOptions.map((option) => (
                            <button
                              key={option.id}
                              className={`text-[12px] sm:text-[14px] lg:text-[18px] px-[10px] sm:px-[16px] py-[6px] sm:py-[10px] text-[#05073C] leading-[24px] font-normal cursor-pointer ${activeTab === option.name.toLowerCase()
                                ? "border-b-3 border-[#0D3FC6]"
                                : ""
                                }`}
                              onClick={() => setActiveTab(option.name.toLowerCase())}
                            >
                              {option.name}
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Tour Overview Content */}
                      {activeTab === "overview" && (
                        <Overview />
                      )}

                      {/* Inclusion Content */}
                      {activeTab === "inclusion" && (
                        <Inclusion />
                      )}

                      {/* Itinerary Content */}
                      {activeTab === "itinerary" && (
                        <Itinerary />
                      )}

                      {/* Terms Content */}
                      {activeTab === "terms" && (
                        <TermContact terms={tourDetailsData?.terms_and_conditions_body} />
                      )}

                      {/* Reviews Content */}
                      {activeTab === "reviews" && (
                        <Reviews />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center py-16">
              <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#9CA3AF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
              <p className="mt-4 text-lg text-gray-700 font-medium">Tour details not found</p>
              <p className="text-gray-500">The tour you're looking for may not exist or has been removed</p>
              <button
                onClick={handleGoBack}
                className="mt-6 px-6 py-2 bg-[#0D3FC6] text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Go back
              </button>
            </div>
          )}
        </div>
      </div>
      {showContactModal && (
        <ContactUsModal onClose={() => setShowContactModal(false)} />
      )}
      {/* <SignUpModal /> */}
      {/* <SignInModal /> */}
    </div>
  );
};

export default PlanDetails;