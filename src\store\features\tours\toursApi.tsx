import Cookies from 'js-cookie';
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const toursApi = createApi({
     reducerPath: "toursApi",
     baseQuery: fetchBaseQuery({
          baseUrl: `${import.meta.env.VITE_API_URL}/database`,
          credentials: "include",
          prepareHeaders: (headers, { endpoint }) => {
               const token = Cookies.get('accessToken');

               if (token && endpoint !== 'loginUser' && endpoint !== 'registerUser') {
                    try {
                         const parts = token.split('.');
                         if (parts.length === 3) {
                              JSON.parse(atob(parts[1]));
                              headers.set('Authorization', `Bearer ${token}`);
                         } else {
                              Cookies.remove('accessToken');
                              window.location.reload();
                         }
                         // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    } catch (e: any) {
                         console.log(e, "::error");
                         Cookies.remove('accessToken');
                    }
               }

               return headers;
          },
     }),

     endpoints: (builder) => ({
          getCountries: builder.query({
               query: () => ({
                    url: "/countries/",
               }),
          }),

          getPackages: builder.mutation({
               query: (data) => ({
                    url: "/search-tours/",
                    method: "POST",
                    body: data,
               }),
          }),

          getPackagesByDestination: builder.query({
               query: (destinationId) => ({
                    url: `/tour-detail/${destinationId}`,
               }),
          }),

          pagination: builder.mutation({
               query: (data) => ({
                    url: "/paginated-tours/",
                    method: "POST",
                    body: data,
               }),
          }),
     }),
})

export const { useGetCountriesQuery, useGetPackagesMutation, useGetPackagesByDestinationQuery, usePaginationMutation } = toursApi;
