import AdminChat from "./AdminChat";
import { useEffect, useRef, useCallback } from "react";
import { useParams } from "react-router-dom";
import { useGetChatHistoryQuery } from "../../store/features/chat/chatApi";
import { useDispatch } from "react-redux";
import { setMessages, setTourDetails } from "../../store/features/chat/chatSlice";

const AdminChatHistory = () => {
     const { threadUid } = useParams();
     const { data: chatHistory, isLoading: historyLoading } = useGetChatHistoryQuery(threadUid!);
     const messagesEndRef = useRef<null | HTMLDivElement>(null);
     const dispatch = useDispatch();

     const scrollToBottom = useCallback(() => {
          if (messagesEndRef.current) {
               const scrollContainer = messagesEndRef.current.closest('.overflow-y-auto');
               if (scrollContainer) {
                    messagesEndRef.current.scrollIntoView({
                         behavior: 'smooth',
                         block: 'end'
                    });
               }
          }
     }, []);

     useEffect(() => {
          const reverseMessages = chatHistory?.data?.messages
               ? [...chatHistory.data.messages].reverse()
               : undefined;

          if (chatHistory?.data?.messages) {
               let foundTourDetails = null;
               // Parse messages
               const parsedMessages = reverseMessages?.map((msg: { context: string, message: string }) => {
                    if (msg.context !== "AI") {
                         return { content: msg.message, sender: "user" };
                    } else {
                         let content = "";
                         let tourDetails = null;
                         let responseType = "text";
                         const parsed = JSON.parse(msg.message);

                         try {
                              // Extract response type
                              responseType = parsed?.type || "text";

                              // Extract content based on different possible structures
                              if (parsed?.response?.message) {
                                   content = parsed.response.message;
                              } else if (parsed?.text_response?.message) {
                                   content = parsed.text_response.message;
                              } else if (parsed?.data?.text_response?.message) {
                                   content = parsed.data.text_response.message;
                              } else if (typeof parsed?.response === "string") {
                                   content = parsed.response;
                              }

                              // Handle tour details for various response types
                              if (responseType === "tour_packages" && parsed?.data) {
                                   tourDetails = parsed.data;
                              } else if (responseType === "popular" && parsed?.data) {
                                   tourDetails = parsed.data;
                              } else if (responseType === "json" && Array.isArray(parsed?.data)) {
                                   tourDetails = parsed.data;
                              } else {
                                   // Try to find tour details in various locations
                                   tourDetails = parsed?.data?.tour_details ||
                                        parsed?.tour_details ||
                                        (parsed?.data && Array.isArray(parsed.data) ? parsed.data : null);
                              }
                         } catch (e) {
                              console.log("Parse error:", e);
                              // If parsing fails completely, use the raw message
                              content = msg.message;
                         }

                         // Final fallback - if content is still empty, use the original message
                         if (!content || content.trim() === "") {
                              content = msg.message;
                         }

                         if (tourDetails && Array.isArray(tourDetails) && tourDetails.length > 0) {
                              foundTourDetails = tourDetails;
                         }

                         if (responseType === "popular" && parsed?.data) {
                              return {
                                   content,
                                   sender: "ai",
                                   responseType: responseType,
                                   tourDetails,
                                   popularDestinations: parsed.data
                              };
                         } else if (responseType === "tour_packages" && parsed?.data) {
                              return {
                                   content,
                                   sender: "ai",
                                   responseType: responseType,
                                   tourDetails,
                                   tourPackages: parsed.data,
                                   meta_details: parsed?.meta_details || {}
                              };
                         } else {
                              return {
                                   content,
                                   sender: "ai",
                                   responseType: responseType,
                                   extracontent: parsed?.response?.extra_context,
                                   tourDetails
                              };
                         }
                    }
               });

               dispatch(setMessages(parsedMessages || []));

               // If we found tour details, set them globally
               if (foundTourDetails) {
                    dispatch(setTourDetails(foundTourDetails));
               }
          }
     }, [chatHistory, dispatch]);

     useEffect(() => {
          scrollToBottom();
     }, [chatHistory, scrollToBottom]);

     return (
          <div>
               <div className="flex flex-col">
                    <div className="overflow-y-auto ">
                         <div className="h-[calc(100vh-100px)] px-[10px] ">
                              { historyLoading  ? (
                                   <div className="flex justify-center items-center h-full">
                                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                                   </div>
                              ) : (
                                   <div className="pointer-events-none select-none">
                                        <AdminChat messagesEndRef={messagesEndRef as React.RefObject<HTMLDivElement>} />
                                   </div>
                              )}
                         </div>
                    </div>
               </div>
          </div>
     );
};

export default AdminChatHistory;