import { Link } from "react-router-dom";
import { useGetUserQuery } from "../../store/features/auth/authApi";
import { useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import { useState } from "react";

const AdminSidebar = () => {
     const { data: userDetails } = useGetUserQuery({});
     const navigate = useNavigate();
     const [isSidebarOpen, setIsSidebarOpen] = useState(false);

     const isActive = (path: string) => {
          if (location.pathname.startsWith(path)) {
               return true;
          }

          if (path === "/" && (location.pathname.startsWith("/chat/") || location.pathname === "/chat")) {
               return true;
          }

          return false;
     };

     const handleLogout = () => {
          Cookies.remove('accessToken');
          navigate('/admin/login');
     };

     return (
          <>
               {/* Hamburger Menu for Mobile */}
               {!isSidebarOpen && <button
                    onClick={() => setIsSidebarOpen(true)}
                    className="md:hidden fixed top-2.5 left-4 z-50 p-2 rounded-lg bg-white"
               >
                    <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
               </button>}

               {/* Overlay for mobile */}
               {isSidebarOpen && (
                    <div
                         className="fixed inset-0 bg-black/50 bg-opacity-50 z-30 md:hidden"
                         onClick={() => setIsSidebarOpen(false)}
                    />
               )}

               {/* Sidebar */}
               <div className={`
                    fixed h-screen bg-white border-r border-gray-200 shadow-sm z-40
                    transition-transform duration-300 ease-in-out
                    md:translate-x-0 md:w-[280px]
                    w-[270px] 
                    ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}
               `}>
                    <div className="flex flex-col h-full">
                         {/* Header */}
                         <div className="border-b p-1 border-[#E5E7EB] px-[16px] flex items-center justify-between">
                              <div className="flex items-center">
                                   <img src="/chat-logo.svg" alt="Logo" className="w-[200px]" />
                              </div>
                              <button
                                   onClick={() => setIsSidebarOpen(false)}
                                   className="md:hidden p-2"
                              >
                                   <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                   </svg>
                              </button>
                         </div>

                         {/* Navigation */}
                         <nav className="flex-1 overflow-y-auto p-3 space-y-1">
                              <h2 className="text-[#05073C] px-4 font-semibold text-lg ">Admin Panel</h2>
                              <Link
                                   to="/admin/dashboard"
                                   className={`flex items-center px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 group ${isActive("/admin/dashboard")
                                        ? "bg-blue-50 text-blue-600"
                                        : "text-gray-600 hover:bg-gray-50"
                                        }`}
                                   onClick={() => setIsSidebarOpen(false)}
                              >
                                   <svg className="w-5 h-5 mr-3 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                                   </svg>
                                   Dashboard
                              </Link>

                              {userDetails?.role === "superuser" && (
                                   <>
                                        <Link
                                             to="/admin/users"
                                             className={`flex items-center px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 group ${isActive("/admin/users")
                                                  ? "bg-blue-50 text-blue-600"
                                                  : "text-gray-600 hover:bg-gray-50"
                                                  }`}
                                             onClick={() => setIsSidebarOpen(false)}
                                        >
                                             <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                             </svg>
                                             Users
                                        </Link>

                                        <Link
                                             to="/admin/analytics"
                                             className={`flex items-center px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 group ${isActive("/admin/analytics")
                                                  ? "bg-blue-50 text-blue-600"
                                                  : "text-gray-600 hover:bg-gray-50"
                                                  }`}
                                             onClick={() => setIsSidebarOpen(false)}
                                        >
                                             <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                             </svg>
                                             Analytics
                                        </Link>

                                        <Link
                                             to="/admin/packages"
                                             className={`flex items-center px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 group ${isActive("/admin/packages")
                                                  ? "bg-blue-50 text-blue-600"
                                                  : "text-gray-600 hover:bg-gray-50"
                                                  }`}
                                             onClick={() => setIsSidebarOpen(false)}
                                        >
                                             <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                                             </svg>
                                             Packages
                                        </Link>

                                        <Link
                                             to="/admin/error-logs"
                                             className={`flex items-center px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 group ${isActive("/admin/error-logs")
                                                  ? "bg-blue-50 text-blue-600"
                                                  : "text-gray-600 hover:bg-gray-50"
                                                  }`}
                                             onClick={() => setIsSidebarOpen(false)}
                                        >
                                             <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                             </svg>
                                             Error Logs
                                        </Link>
                                   </>
                              )}

                              <Link
                                   to="/admin/bookings"
                                   className={`flex items-center px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 group ${isActive("/admin/bookings")
                                        ? "bg-blue-50 text-blue-600"
                                        : "text-gray-600 hover:bg-gray-50"
                                        }`}
                                   onClick={() => setIsSidebarOpen(false)}
                              >
                                   <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                   </svg>
                                   Bookings
                              </Link>

                              <Link
                                   to="/admin/contact-details"
                                   className={`flex items-center px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 group ${isActive("/admin/contact-details")
                                        ? "bg-blue-50 text-blue-600"
                                        : "text-gray-600 hover:bg-gray-50"
                                        }`}
                                   onClick={() => setIsSidebarOpen(false)}
                              >
                                   <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                   </svg>
                                   Users Contacts
                              </Link>
                         </nav>

                         {/* Footer */}
                         <div className="p-4 border-t border-gray-200">
                              <div className="flex items-center justify-between px-2 py-0.5">
                                   <div className="flex items-center gap-3">
                                        <div className="w-9 h-9 rounded-full bg-[#F3F4F6] flex items-center justify-center">
                                             <span className="text-gray-600 text-xl capitalize">{userDetails?.email?.charAt(0)}</span>
                                        </div>
                                        <div>
                                             <p className="text-[15px] font-medium text-[#374151] truncate max-w-[100px]">{userDetails?.email}</p>
                                             <p className="text-[13px] text-[#6B7280]">{userDetails?.role}</p>
                                        </div>
                                   </div>
                                   <button
                                        type="button"
                                        onClick={handleLogout}
                                        className="flex items-center gap-1.5 text-red-500"
                                   >
                                        <svg
                                             className="w-[18px] h-[18px]"
                                             viewBox="0 0 24 24"
                                             fill="none"
                                             stroke="currentColor"
                                        >
                                             <path
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                  strokeWidth="1.5"
                                                  d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                                             />
                                        </svg>
                                        <span className="text-[14px]">Logout</span>
                                   </button>
                              </div>
                         </div>
                    </div>
               </div>
          </>
     );
};

export default AdminSidebar;