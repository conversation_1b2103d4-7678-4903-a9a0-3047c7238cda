import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import Cookies from 'js-cookie';

export const reviewsApi = createApi({
     reducerPath: "reviewsApi",
     baseQuery: fetchBaseQuery({
          baseUrl: `${import.meta.env.VITE_API_URL}/database/reviews/`,
          credentials: "include",
          prepareHeaders: (headers, { endpoint }) => {
               const token = Cookies.get('accessToken');

               if (token && endpoint !== 'loginUser' && endpoint !== 'registerUser') {
                    try {
                         const parts = token.split('.');
                         if (parts.length === 3) {
                              JSON.parse(atob(parts[1]));
                              headers.set('Authorization', `Bearer ${token}`);
                         }
                         else {
                              Cookies.remove('accessToken');
                              window.location.reload();
                         }

                         // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    } catch (e: any) {
                         console.log(e, "::error");
                         Cookies.remove('accessToken');
                    }
               }

               return headers;
          },
     }),

     tagTypes: ["reviews"],
     endpoints: (builder) => ({
          addReview: builder.mutation({
               query: (data) => ({
                    url: "/",
                    method: "POST",
                    body: data,
               }),
               invalidatesTags: ["reviews"],
          }),

          getReviews: builder.query({
               query: (tour_id) => ({
                    url: `/tour/${tour_id}/`,
                    method: "GET",
               }),
               providesTags: ["reviews"],
          }),
     }),
});

export const { useAddReviewMutation, useGetReviewsQuery } = reviewsApi;
