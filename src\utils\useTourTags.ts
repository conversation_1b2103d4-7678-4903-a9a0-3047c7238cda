import { useEffect, useState } from 'react';

export interface TourTag {
  id: number;
  tag: string;
}

export function useTourTags(tourId: string | number) {
  const [tags, setTags] = useState<TourTag[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => { 
    if (!tourId) return;
    setLoading(true);
    setError(null);
    fetch(`https://api.tripbooking.ai/database/promo-tags/${tourId}/`)
      .then(res => res.json())
      .then(data => {
        if (data.status) {
          setTags(data.data);
        } else {
          setError('Failed to fetch tags');
        }
      })
      .catch(() => setError('Failed to fetch tags'))
      .finally(() => setLoading(false));
  }, [tourId]);

  return { tags, loading, error };
}
