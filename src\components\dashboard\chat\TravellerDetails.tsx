import { useF<PERSON>, Controller, useFieldArray } from "react-hook-form";
import SelectIcon from "../../../assets/arrow-bottom.svg";
import AddTravellerIcon from "../../../assets/add-icon.svg";
import { useDispatch, useSelector } from "react-redux";
import { addMessage, threadUid as setThreadUid, setLoading } from "../../../store/features/chat/chatSlice";
import { useSendMessageMutation } from "../../../store/features/chat/chatApi";
import { RootState } from "../../../store/store";
import { useState } from "react";
import { countries } from "../../../utils/dummyData";

type Traveller = {
    first_name: string;
    last_name: string;
    gender: string;
    date_of_birth: string;
    nationality: string;
    passport_number: string;
    special_requirements?: string;
};

type TravellerFormData = {
    travellers: Traveller[];
};

const TravellerDetails = () => {
    const [isSubmitted, setIsSubmitted] = useState(false);
    const { register, handleSubmit, control, reset, formState: { errors } } = useForm<TravellerFormData>({
        defaultValues: {
            travellers: [{
                first_name: "",
                last_name: "",
                gender: "",
                date_of_birth: "",
                nationality: "",
                passport_number: "",
                special_requirements: ""
            }]
        }
    });

    const { fields, append, remove } = useFieldArray({
        control,
        name: "travellers"
    });

    const dispatch = useDispatch();
    const [sendMessage] = useSendMessageMutation();
    const threadUid = useSelector((state: RootState) => state.chat.threadUid);

    const onSubmit = async (data: TravellerFormData) => {
        const formattedTravellers = data.travellers.map(traveller => {
            const updatedTraveller = { ...traveller };
            if (updatedTraveller.date_of_birth) {
                const dateParts = updatedTraveller.date_of_birth.split('-');
                if (dateParts.length === 3) {
                    updatedTraveller.date_of_birth = `${dateParts[2]}/${dateParts[1]}/${dateParts[0]}`;
                }
            }
            return updatedTraveller;
        });
        const messageText = "Here are the details for each traveller";
        dispatch(addMessage({
            content: messageText,
            sender: "user"
        }));
        dispatch(setLoading(true));

        // Create the API payload in the exact format requested
        const messagePayload = {
            message: messageText,
            thread_uid: threadUid || "",
            context_data: {
                intent: "booking",
                action: "traveller_details",
                data: {
                    travellers: formattedTravellers
                }
            }
        };

        try {
            // Send the message to the API
            const response = await sendMessage(messagePayload).unwrap();

            // Get thread UID from the response
            const responseThreadUid = response?.data?.thread_uid || threadUid;

            // Update the thread UID in the Redux store
            if (responseThreadUid) {
                dispatch(setThreadUid(responseThreadUid));
            }

            // Set form as submitted to disable fields and hide buttons
            setIsSubmitted(true);

        } catch (error) {
            console.error("Error sending traveller details:", error);

            // Add error message to chat
            dispatch(addMessage({
                content: "Sorry, I couldn't process your information at the moment. Please try again later.",
                sender: "ai"
            }));
        } finally {
            // Set loading state back to false
            dispatch(setLoading(false));
        }
    };

    const handleSkip = async () => {
        const formattedTravellers: Traveller[] = [];
        const messageText = "Skip details for travellers";
        dispatch(addMessage({
            content: messageText,
            sender: "user"
        }));
        dispatch(setLoading(true));

        // Create the API payload in the exact format requested
        const messagePayload = {
            message: messageText,
            thread_uid: threadUid || "",
            context_data: {
                intent: "booking",
                action: "traveller_details",
                data: {
                    travellers: formattedTravellers
                }
            }
        };

        try {
            // Send the message to the API
            const response = await sendMessage(messagePayload).unwrap();

            // Get thread UID from the response
            const responseThreadUid = response?.data?.thread_uid || threadUid;

            // Update the thread UID in the Redux store
            if (responseThreadUid) {
                dispatch(setThreadUid(responseThreadUid));
            }

            // Set form as submitted to disable fields and hide buttons
            setIsSubmitted(true);

        } catch (error) {
            console.error("Error sending traveller details:", error);

            // Add error message to chat
            dispatch(addMessage({
                content: "Sorry, I couldn't process your information at the moment. Please try again later.",
                sender: "ai"
            }));
        } finally {
            // Set loading state back to false
            dispatch(setLoading(false));
        }
    };

    const handleClear = () => {
        reset({
            travellers: [{
                first_name: "",
                last_name: "",
                gender: "",
                date_of_birth: "",
                nationality: "",
                passport_number: "",
                special_requirements: ""
            }]
        });
    };

    const handleAddTraveller = () => {
        append({
            first_name: "",
            last_name: "",
            gender: "",
            date_of_birth: "",
            nationality: "",
            passport_number: "",
            special_requirements: ""
        });
    };

    // Add validation function for whitespace
    const validateNotOnlyWhitespace = (value: string) => {
        return value.trim().length > 0 || "This field cannot contain only spaces";
    };

    // Add validation function for passport number
    const validatePassportNumber = (value: string) => {
        // Common passport number format: 2 letters followed by 7 numbers
        const passportRegex = /^[A-Z0-9]{6,12}$/;
        if (!passportRegex.test(value)) {
            return "Invalid passport number format (6-12 alphanumeric characters)";
        }
        return true;
    };

    return (
        <div className="mt-[16px] ms-[45px] max-w-[620px] bg-[#F4F4F6] rounded-[24px] py-[20px] px-[18px] border border-[#E5E7EB]">
            <h3 className="mb-[15px] sm:mb-[23px] text-[16px] sm:text-[20px] leading-[20px] sm:leading-[24px] font-semibold text-[#05073C]">
                {isSubmitted ? "Traveller details submitted" : "Please Provide details for each traveller"}
            </h3>

            <form onSubmit={handleSubmit(onSubmit)}>
                {fields?.map((field, index) => (
                    <div key={field.id} className="mb-[30px] pb-[20px] border-b border-dashed border-gray-300">
                        <div className="flex justify-between items-center mb-[15px]">
                            <h4 className="text-[16px] font-semibold text-[#05073C]">Traveller {index + 1}</h4>
                            {!isSubmitted && index > 0 && (
                                <button
                                    type="button"
                                    onClick={() => remove(index)}
                                    className="text-red-500 text-sm font-medium"
                                >
                                    Remove
                                </button>
                            )}
                        </div>

                        {/* First row - First name, Last name */}
                        <div className="flex flex-col lg:flex-row lg:gap-[8px] items-center">
                            <div className="mb-[10px] lg:mb-[20px] w-full">
                                <label className="block text-[14px] font-semibold text-[#636C76] mb-1">First Name</label>
                                <input
                                    type="text"
                                    className={`rounded-[30px] border ${errors.travellers?.[index]?.first_name ? 'border-red-500' : 'border-[#D5D5D8]'} bg-[#FFFFFF] py-[13px] px-[18px] text-[14px] leading-[18px] text-[#05073C] w-full placeholder:text-[#989EAE] focus:outline-[#0D3FC6] ${isSubmitted ? 'opacity-80 cursor-not-allowed' : ''}`}
                                    placeholder="Enter first name"
                                    disabled={isSubmitted}
                                    {...register(`travellers.${index}.first_name` as const, {
                                        required: "First name is required",
                                        validate: {
                                            notWhitespace: validateNotOnlyWhitespace,
                                        },
                                        minLength: {
                                            value: 3,
                                            message: "First name must be at least 3 characters"
                                        },
                                        maxLength: {
                                            value: 50,
                                            message: "First name must not exceed 50 characters"
                                        },
                                        pattern: {
                                            value: /^[A-Za-z\s]+$/,
                                            message: "First name can only contain letters and spaces"
                                        }
                                    })}
                                />
                                {errors.travellers?.[index]?.first_name &&
                                    <span className="text-red-500 text-xs mt-1">
                                        {errors.travellers?.[index]?.first_name.message}
                                    </span>
                                }
                            </div>
                            <div className="mb-[10px] lg:mb-[20px] w-full">
                                <label className="block text-[14px] font-semibold text-[#636C76] mb-1">Last Name</label>
                                <input
                                    type="text"
                                    className={`rounded-[30px] border ${errors.travellers?.[index]?.last_name ? 'border-red-500' : 'border-[#D5D5D8]'} bg-[#FFFFFF] py-[13px] px-[18px] text-[14px] leading-[18px] text-[#05073C] w-full placeholder:text-[#989EAE] focus:outline-[#0D3FC6] ${isSubmitted ? 'opacity-80 cursor-not-allowed' : ''}`}
                                    placeholder="Enter last name"
                                    disabled={isSubmitted}
                                    {...register(`travellers.${index}.last_name` as const, {
                                        required: "Last name is required",
                                        validate: {
                                            notWhitespace: validateNotOnlyWhitespace,
                                        },
                                        minLength: {
                                            value: 3,
                                            message: "Last name must be at least 3 characters"
                                        },
                                        maxLength: {
                                            value: 50,
                                            message: "Last name must not exceed 50 characters"
                                        },
                                        pattern: {
                                            value: /^[A-Za-z\s]+$/,
                                            message: "Last name can only contain letters and spaces"
                                        }
                                    })}
                                />
                                {errors.travellers?.[index]?.last_name &&
                                    <span className="text-red-500 text-xs mt-1">
                                        {errors.travellers?.[index]?.last_name.message}
                                    </span>
                                }
                            </div>
                        </div>

                        {/* Second row - Gender, Date of birth */}
                        <div className="flex flex-col lg:flex-row lg:gap-[8px] items-center">
                            <div className="mb-[10px] lg:mb-[20px] w-full">
                                <label className="block text-[14px] font-semibold text-[#636C76] mb-1">Gender</label>
                                <div className="relative">
                                    <select
                                        className={`rounded-[30px] border ${errors.travellers?.[index]?.gender ? 'border-red-500' : 'border-[#D5D5D8]'} bg-[#FFFFFF] py-[11.8px] px-[18px] text-[14px] leading-[18px] text-[#05073C] w-full placeholder:text-[#989EAE] focus:outline-[#0D3FC6] appearance-none ${isSubmitted ? 'opacity-80 cursor-not-allowed' : ''}`}
                                        disabled={isSubmitted}
                                        {...register(`travellers.${index}.gender` as const, {
                                            required: "Please select a gender"
                                        })}
                                    >
                                        <option value="">Select Gender</option>
                                        <option value="Male">Male</option>
                                        <option value="Female">Female</option>
                                    </select>
                                    {!isSubmitted && <img src={SelectIcon} alt="Select" className="absolute right-[18px] top-1/2 transform -translate-y-1/2" />}
                                    {errors.travellers?.[index]?.gender &&
                                        <span className="text-red-500 text-xs mt-1">
                                            {errors.travellers?.[index]?.gender.message}
                                        </span>
                                    }
                                </div>
                            </div>
                            <div className="mb-[10px] lg:mb-[20px] w-full">
                                <label className="block text-[14px] font-semibold text-[#636C76] mb-1">Date of Birth (DD/MM/YYYY)</label>
                                <Controller
                                    control={control}
                                    name={`travellers.${index}.date_of_birth` as const}
                                    rules={{
                                        required: "Date of birth is required",
                                        validate: value => {
                                            if (!value) return "Date of birth is required";
                                            const selected = new Date(value);
                                            const today = new Date();
                                            const minDate = new Date();
                                            minDate.setFullYear(minDate.getFullYear() - 100); // Max age 100 years

                                            // Remove time part for accurate comparison
                                            selected.setHours(0, 0, 0, 0);
                                            today.setHours(0, 0, 0, 0);
                                            minDate.setHours(0, 0, 0, 0);

                                            if (selected > today) return "Date of birth cannot be in the future";
                                            if (selected < minDate) return "Invalid date of birth (age cannot exceed 100 years)";
                                            return true;
                                        }
                                    }}
                                    render={({ field }) => (
                                        <div className="relative">
                                            <input
                                                type="date"
                                                max={new Date().toISOString().split("T")[0]}
                                                className={`rounded-[30px] border ${errors.travellers?.[index]?.date_of_birth ? 'border-red-500' : 'border-[#D5D5D8]'} bg-[#FFFFFF] py-[13px] px-[18px] text-[14px] leading-[18px] text-[#05073C] w-full placeholder:text-[#989EAE] focus:outline-[#0D3FC6] ${isSubmitted ? 'opacity-80 cursor-not-allowed' : ''}`}
                                                onChange={(e) => field.onChange(e.target.value)}
                                                ref={field.ref}
                                                onBlur={field.onBlur}
                                                value={field.value || ''}
                                                onFocus={(e) => !isSubmitted && e.target.showPicker()}
                                                disabled={isSubmitted}
                                            />
                                        </div>
                                    )}
                                />
                                {errors.travellers?.[index]?.date_of_birth && (
                                    <span className="text-red-500 text-xs mt-1">
                                        {errors.travellers?.[index]?.date_of_birth.message}
                                    </span>
                                )}
                            </div>
                        </div>

                        {/* Third row - Nationality, Passport Number */}
                        <div className="flex flex-col lg:flex-row lg:gap-[8px] items-center">
                            <div className="mb-[10px] lg:mb-[20px] w-full">
                                <label className="block text-[14px] font-semibold text-[#636C76] mb-1">Nationality</label>
                                <div className="relative">
                                    <select
                                        className={`rounded-[30px] border ${errors.travellers?.[index]?.nationality ? 'border-red-500' : 'border-[#D5D5D8]'} bg-[#FFFFFF] py-[11.8px] px-[18px] text-[14px] leading-[18px] text-[#05073C] w-full placeholder:text-[#989EAE] focus:outline-[#0D3FC6] appearance-none ${isSubmitted ? 'opacity-80 cursor-not-allowed' : ''}`}
                                        disabled={isSubmitted}
                                        {...register(`travellers.${index}.nationality` as const, {
                                            required: "Nationality is required"
                                        })}
                                    >
                                        <option value="">Select Nationality</option>
                                        {countries.map((country) => (
                                            <option key={country} value={country}>
                                                {country}
                                            </option>
                                        ))}
                                    </select>
                                    {!isSubmitted && <img src={SelectIcon} alt="Select" className="absolute right-[18px] top-1/2 transform -translate-y-1/2" />}
                                    {errors.travellers?.[index]?.nationality &&
                                        <span className="text-red-500 text-xs mt-1">
                                            {errors.travellers?.[index]?.nationality.message}
                                        </span>
                                    }
                                </div>
                            </div>
                            <div className="mb-[10px] lg:mb-[20px] w-full">
                                <label className="block text-[14px] font-semibold text-[#636C76] mb-1">Passport Number</label>
                                <input
                                    type="text"
                                    className={`rounded-[30px] border ${errors.travellers?.[index]?.passport_number ? 'border-red-500' : 'border-[#D5D5D8]'} bg-[#FFFFFF] py-[13px] px-[18px] text-[14px] leading-[18px] text-[#05073C] w-full placeholder:text-[#989EAE] focus:outline-[#0D3FC6] ${isSubmitted ? 'opacity-80 cursor-not-allowed' : ''}`}
                                    placeholder="Enter passport number"
                                    disabled={isSubmitted}
                                    {...register(`travellers.${index}.passport_number` as const, {
                                        required: "Passport number is required",
                                        validate: validatePassportNumber,
                                        pattern: {
                                            value: /^[A-Z0-9]{6,12}$/,
                                            message: "Invalid passport number format"
                                        }
                                    })}
                                />
                                {errors.travellers?.[index]?.passport_number &&
                                    <span className="text-red-500 text-xs mt-1">
                                        {errors.travellers?.[index]?.passport_number.message}
                                    </span>
                                }
                            </div>
                        </div>

                        {/* Special Requirements */}
                        <div className="mb-[10px] lg:mb-[20px] w-full">
                            <label className="block text-[14px] font-semibold text-[#636C76] mb-1">Special Requirement <span className="font-normal">(Optional)</span></label>
                            <textarea
                                className={`h-[65px] resize-none rounded-[20px] border ${errors.travellers?.[index]?.special_requirements ? 'border-red-500' : 'border-[#D5D5D8]'} bg-[#FFFFFF] py-[13px] px-[18px] text-[14px] leading-[18px] text-[#05073C] w-full placeholder:text-[#989EAE] focus:outline-[#0D3FC6] ${isSubmitted ? 'opacity-80 cursor-not-allowed' : ''}`}
                                placeholder="Enter special requirements"
                                disabled={isSubmitted}
                                {...register(`travellers.${index}.special_requirements` as const, {
                                    maxLength: {
                                        value: 500,
                                        message: "Special requirements must not exceed 500 characters"
                                    }
                                })}
                            />
                            {errors.travellers?.[index]?.special_requirements &&
                                <span className="text-red-500 text-xs mt-1">
                                    {errors.travellers?.[index]?.special_requirements.message}
                                </span>
                            }
                        </div>
                    </div>
                ))}

                {/* Add Traveller button - only shown if not submitted */}
                {!isSubmitted && (
                    <div className="mb-[26px]">
                        <button
                            type="button"
                            className="flex items-center gap-[8px] border border-[#D5D5D8] rounded-[30px] py-[9px] px-[18px] text-[14px] font-semibold text-[#05073C] bg-white"
                            onClick={handleAddTraveller}
                        >
                            <span><img src={AddTravellerIcon} alt="Add Traveller" /></span> Add Traveller
                        </button>
                    </div>
                )}

                {/* Actions - only shown if not submitted */}
                {!isSubmitted && (
                    <div className="flex flex-wrap sm:flex-nowrap gap-[11px] items-center mb-[10px]">
                        <button
                            type="button"
                            className="w-full sm:w-auto bg-[#E7ECF9] text-[#0D3FC6] py-[18px] px-[48px] rounded-[8px] cursor-pointer text-[18px] leading-[16px] font-semibold"
                            onClick={handleClear}
                        >
                            Clear
                        </button>
                        <button
                            type="submit"
                            className="w-full sm:w-auto bg-[#0D3FC6] text-white py-[18px] px-[48px] rounded-[8px] cursor-pointer text-[18px] leading-[16px] font-semibold"
                        >
                            Save
                        </button>
                        <button
                            type="button"
                            className="w-full sm:w-auto bg-[#E7ECF9] text-[#0D3FC6] py-[18px] px-[48px] rounded-[8px] cursor-pointer text-[18px] leading-[16px] font-semibold"
                            onClick={handleSkip}
                        >
                            Skip
                        </button>
                    </div>
                )}

                {/* Success message shown when form is submitted */}
                {isSubmitted && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-green-700 text-center">
                        <svg className="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <p className="font-medium">Traveller details submitted successfully</p>
                    </div>
                )}
            </form>
        </div>
    );
};

export default TravellerDetails;
