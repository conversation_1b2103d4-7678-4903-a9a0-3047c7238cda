// import ModifyIcon from "../../../assets/modify-icon.svg";
// import PrintIcon from "../../../assets/print-icon.svg";
import { useNavigate } from "react-router-dom";
import tourDetailsCards from "../../../assets/plan-img1.png";
import { useDirectBankPaymentMutation } from "../../../store/features/bookings/bookingsApi";
import { toast } from "react-hot-toast";
import { BookingPDFDownload } from "./BookingPDF";

const formatDate = (dateString: string) => {
     if (dateString?.includes("T")) {
          const date = new Date(dateString);
          return date.toLocaleDateString('en-US', {
               year: 'numeric',
               month: 'short',
               day: 'numeric'
          });
     }
     return dateString;
};

const renderStatusBadge = (status: string) => {
     let colorClass = "";
     let icon = "";
     switch (status) {
          case "Confirmed":
               colorClass = "bg-green-100 text-green-800";
               icon = "fa-check-circle";
               break;
          case "Pending Payment":
               colorClass = "bg-yellow-100 text-yellow-800";
               icon = "fa-clock";
               break;
          case "Completed":
               colorClass = "bg-blue-100 text-blue-800";
               icon = "fa-check-double";
               break;
          case "Cancelled":
               colorClass = "bg-red-100 text-red-800";
               icon = "fa-times-circle";
               break;
          default:
               colorClass = "bg-gray-100 text-gray-800";
               icon = "fa-info-circle";
     }

     return (
          <span
               className={`inline-flex items-center px-2.5 py-[4px] rounded-full text-xs font-medium ${colorClass}`}
          >
               <i className={`fa ${icon} mr-1`}></i>
               {status}
          </span>
     );
};

const BookingCards = ({ booking, setSelectedBooking, setShowDetailModal }: {
     booking:
     {
          id: number;
          destination: string;
          hotel: string;
          dates: string;
          booking_status: string;
          guests: string;
          booking_amount: string;
          status: string;
          image: string;
          type: string;
          order: {
               order_id: string;
          };
          booking_reference: string;
          booking_date: string;
          duration: {
               start_date: string;
               end_date: string;
          };
          traveller_numbers: {
               total: number;
          };
          tour: {
               tour_title: string;
               tour_gallery_images: string[];
          };
          contact_details: {
               first_name: string;
               last_name: string;
               address: string;
          };
     }
     // eslint-disable-next-line @typescript-eslint/no-explicit-any
     setSelectedBooking: (booking: any) => void;
     setShowDetailModal: (show: boolean) => void;
}) => {
     const navigate = useNavigate();
     const [directBankPayment, { isLoading }] = useDirectBankPaymentMutation();

     const handlePayNow = (orderId: string) => {
          navigate(`/payment-gateway/${orderId}`);
     }

     const handleCancel = async (orderId: string) => {
          const payload = {
               order_id: orderId,
               status: "cancelled",
          }
          const response = await directBankPayment(payload);
          if (response?.data?.success) {
               toast.success(response?.data?.msg || "Booking cancelled successfully");
          } else {
               toast.error(response?.data?.msg || "Booking cancellation failed");
          }
     }

     return (
          <div
               key={booking.id}
               className="bg-white rounded-lg overflow-hidden border border-[#E5E7EB] shadow-[0px_1px_2px_0px_#0000000D] mb-[24px]"
          >
               <div className="lg:flex">
                    <div className="lg:w-1/3 h-48 md:h-auto overflow-hidden">
                         <img
                              src={booking?.tour?.tour_gallery_images[0] || tourDetailsCards}
                              alt={booking.destination}
                              className="w-full h-full object-cover object-top"
                         />
                    </div>
                    <div className="p-[16px] sm:p-[24px] lg:w-2/3">
                         <div className="flex justify-between items-start">
                              <h1 className="text-xl font-semibold text-[#05073C]">
                                   {booking?.tour?.tour_title}
                              </h1>
                              <div className="sm:flex justify-between items-start">
                                   <div>
                                        <h3 className="text-xl font-semibold text-[#05073C]">
                                             {booking.destination}
                                        </h3>
                                        <p className="text-[#4B5563] text-base">
                                             {booking.hotel}
                                        </p>
                                   </div>
                                   {renderStatusBadge(booking.booking_status)}
                              </div>
                         </div>

                         <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div>
                                   <p className="text-xs text-[#6B7280]">Booking Number</p>
                                   <p className="text-sm font-medium">
                                        {booking?.booking_reference}
                                   </p>
                              </div>
                              <div>
                                   <p className="text-xs text-[#6B7280]">Booking Date</p>
                                   <p className="text-sm font-medium">{formatDate(booking.booking_date)}</p>
                              </div>
                              <div>
                                   <p className="text-xs text-[#6B7280]">Total Price</p>
                                   <p className="text-sm font-medium">৳ {booking.booking_amount}</p>
                              </div>
                              <div>
                                   <p className="text-xs text-[#6B7280]">Dates</p>
                                   <p className="text-sm font-medium">{formatDate(booking?.duration?.start_date)} - {formatDate(booking?.duration?.end_date)}</p>
                              </div>
                              <div>
                                   <p className="text-xs text-[#6B7280]">Guests</p>
                                   <p className="text-sm font-medium">{booking?.traveller_numbers?.total}</p>
                              </div>
                              <div>
                                   <p className="text-xs text-[#6B7280]">Booking Status</p>
                                   <p className="text-sm font-medium first-letter:capitalize">{booking?.booking_status}</p>
                              </div>
                         </div>
                         <div className="mt-6 flex flex-wrap gap-2">
                              <button
                                   className="px-[25px] py-2 bg-[#0D3FC6] text-white rounded-[4px] text-sm cursor-pointer"
                                   onClick={() => {
                                        const bookingData = booking;
                                        setSelectedBooking(bookingData);
                                        setShowDetailModal(true);
                                   }}
                              >
                                   <i className="fa fa-eye mr-2"></i>
                                   View Details
                              </button>

                              {
                                   booking?.booking_status === "payment_pending" && (
                                        <button
                                             type="button"
                                             className="px-[25px] py-2 bg-[#0D3FC6] text-white rounded-[4px] text-sm cursor-pointer"
                                             onClick={() => handlePayNow(booking?.order?.order_id)}
                                        >
                                             <i className="fa fa-credit-card mr-2"></i>
                                             Pay Now
                                        </button>
                                   )
                              }
                              {booking?.booking_status !== "cancelled" && <button
                                   className="px-4 py-2 bg-white border border-[#E5E7EB] text-[#636A7E] rounded-sm text-sm flex items-center gap-[6px] cursor-pointer"
                                   type="button"
                                   disabled={isLoading}
                                   onClick={() => handleCancel(booking?.order?.order_id)}
                              >
                                   {isLoading ? "Cancelling..." : "Cancel"}
                              </button>}

                              {/* React-PDF Download Button */}
                              {booking?.booking_status === "completed" && <BookingPDFDownload booking={booking} />}

                              {booking.status === "Pending Payment" && (
                                   <button className="px-4 py-2 bg-[#00c50d] text-white rounded-[4px] text-sm cursor-pointer">
                                        <i className="fa fa-credit-card mr-2"></i>
                                        Pay Now
                                   </button>
                              )}
                         </div>
                    </div>
               </div>
          </div>
     )
}

export default BookingCards;
