import PlanImg from "../../../assets/plan-img1.png"
const DestinationList = ({ destination }: { destination: { name: string, id: string, image_url: string } }) => {
     return (
          <div className="bg-white rounded-xl shadow-md w-40 sm:w-72 flex flex-col overflow-hidden">
               <img
                    src={destination?.image_url ? `${import.meta.env.VITE_API_URL}${destination?.image_url}` : PlanImg}
                    alt={destination.name}
                    className="w-full h-32 sm:h-40 object-cover"
               />
               <div className="py-2 px-3">
                    <h3 className="text-[#05073C] text-base sm:text-lg font-semibold">{destination.name}</h3>
               </div>
          </div>
     )
}

export default DestinationList;
