import Sidebar from "./components/dashboard/sidebar/Sidebar";
import { useGetUserQuery } from "./store/features/auth/authApi";
import { useDispatch } from "react-redux";
import { setUserDetails } from "./store/features/auth/authSlice";
import { useEffect } from "react";
import { useParams } from "react-router-dom";
const Layout = ({ children }: { children: React.ReactNode }) => {

     const { threadUid } = useParams();
     const isAuthRoute = window.location.pathname === '/login' ||
          window.location.pathname === '/register' ||
          window.location.pathname === '/forgot-password' ||
          window.location.pathname === '/confirm-password' ||
          window.location.pathname === '/otp-verification';

     const { data: user } = useGetUserQuery({});
     const dispatch = useDispatch();

     useEffect(() => {
          if (user) {
               dispatch(setUserDetails(user));
          }
     }, [user, dispatch]);

     const isChatRoute = window.location.pathname === '/' || window.location.pathname === `/chat/${threadUid}`;

     return (
          <div className={`flex h-screen ${isChatRoute ? 'overflow-hidden' : 'overflow-y-auto'}`}>
               {!isAuthRoute && <Sidebar />}
               <main className="flex-1">
                    {children}
               </main>
          </div>
     );
};

export default Layout;