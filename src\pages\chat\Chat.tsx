import ChatInput from "../../components/dashboard/chat/ChatInput";
import ChatHeading from "../../components/dashboard/chat/ChatHeading";
import { useSelector } from "react-redux";
import { RootState } from "../../store/store";
import StaticChat from "../../components/dashboard/chat/StaticChat";
import { useEffect, useRef, useState, useCallback } from "react";
import NewChat from "../../components/dashboard/history/NewChat";
import { useSendMessageMutation } from "../../store/features/chat/chatApi";

const Chat = () => {
    const messages = useSelector((state: RootState) => state.chat.messages);
    const historyLoading = useSelector((state: RootState) => state.chat.historyLoading);
    const threadUid = useSelector((state: RootState) => state.chat.threadUid);
    const messagesEndRef = useRef<null | HTMLDivElement>(null);
    const [sendMessage, { isLoading: isSendingMessage }] = useSendMessageMutation();

    // Add a state to track if we should show the heading
    const [showHeading, setShowHeading] = useState(false);

    // Handle viewport height for mobile browsers
    useEffect(() => {
        const setVh = () => {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        };

        setVh();
        window.addEventListener('resize', setVh);
        window.addEventListener('orientationchange', setVh);

        return () => {
            window.removeEventListener('resize', setVh);
            window.removeEventListener('orientationchange', setVh);
        };
    }, []);

    const scrollToBottom = useCallback(() => {
        if (messagesEndRef.current) {
            const scrollContainer = messagesEndRef.current.closest('.overflow-y-auto');
            if (scrollContainer) {
                // Simple scroll to bottom without smooth animation for Mac
                messagesEndRef.current.scrollIntoView({
                    behavior: 'smooth',
                    block: 'end'
                });
            }
        }
    }, []);

    useEffect(() => {
        scrollToBottom();
    }, [messages, scrollToBottom]);

    useEffect(() => {
        if (!threadUid && messages.length === 0 && !historyLoading) {
            const timer = setTimeout(() => {
                setShowHeading(true);
            }, 300);
            return () => clearTimeout(timer);
        } else {
            setShowHeading(false);
        }
    }, [threadUid, messages.length, historyLoading]);

    return (
        <div className="flex">
            {/* Main Content */}
            <div className={`md:hidden fixed -top-2 left-0 right-0 z-10 transition-all duration-300 bg-white/80 backdrop-blur-md h-[70px]`}></div>
            <div className="block md:hidden fixed left-[50%] translate-x-[-50%] top-[10px] z-[20]">
                <img src="/mini-logo.svg" alt="Logo" className="w-[40px]" />
            </div>
            <div className="flex-1 md:ms-[275px] w-[calc(100vw-600px)] flex flex-col h-[calc(var(--vh, 1vh) * 100)]">
                <div
                    className="overflow-y-auto pt-[80px] flex-1"
                    style={{
                        height: 'calc(var(--vh, 1vh) * 100 - 120px)'
                    }}
                >
                    {showHeading && <ChatHeading />}

                    <div className="max-w-[1075px] mx-auto px-[10px] ms:px-[20px]">
                        {historyLoading && threadUid ? (
                            <>
                            </>
                        ) : (
                            <StaticChat messagesEndRef={messagesEndRef} sendMessage={sendMessage} isSendingMessage={isSendingMessage} />
                        )}
                    </div>
                </div>

                <div className="fixed bottom-0 left-0 right-0 md:left-[140px] w-full bg-white ">
                    <div className="pt-2 pb-1 max-w-[1075px] mx-auto px-[10px] md:px-[20px]">
                        <ChatInput sendMessage={sendMessage} isSendingMessage={isSendingMessage} />
                    </div>
                </div>
            </div>
            <div className="flex flex-col justify-center items-center gap-[4px] fixed top-3 sm:right-10 right-4 z-[98] h-[34px] w-[34px]">
                <NewChat />
            </div>
        </div>
    );
};

export default Chat;