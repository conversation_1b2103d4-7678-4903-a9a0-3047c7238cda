import { useState, useEffect } from "react";
import { useTourTags } from '../../../utils/useTourTags';
import TourTagChips from '../../common/TourTagChips';
import tourDetailsCards from "../../../assets/plan-img1.png";
import { useNavigate, useParams } from "react-router-dom";
import LocationIcon from "../../../assets/location-icon.svg";
import TimeIcon from "../../../assets/time-icon.svg"
import Cookies from "js-cookie";
import SignInModal from "./SignInModal";
import SignUpModal from "./SignUpModal";
import { useAppSelector } from "../../../store/hooks";
import VerifyPhoneNumber from "../../../pages/auth/VerifyPhoneNumber";

interface TourItem {
  id: string;
  image: string;
  tour_title: string;
  tour_description: string;
  location: { address: string };
  duration: string;
  adult_price: string;
  tour_id: string;
  tour_gallery_images: string[];
  featured: boolean;
  group_size: string;
  tags?: string[];
  tour_types?: string;
}

interface TourData {
  featured: TourItem[];
  cheapest: TourItem[];
}

const TourCard = ({ tour, category, index, handleViewDetails }: { tour: TourItem, category: string, index: number, handleViewDetails: (tour_id: string) => void }) => {
  const uniqueKey = `${category}-${tour.tour_id || ''}-${tour.id || ''}-${index}`;
  const { tags, loading: tagsLoading } = useTourTags(tour.tour_id);
  return (
    <div
      key={uniqueKey}
      className="bg-white relative rounded-[12px] overflow-hidden border border-[#E5E7EB] shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1)] flex flex-col"
    >
      <div className="h-48 overflow-hidden">
        <img
          src={tour?.tour_gallery_images?.[0] || tourDetailsCards}
          alt={tour.tour_title}
          className="w-full h-full object-cover object-top"
        />
      </div>
      <div className="p-4 flex flex-col gap-[8px] flex-grow">
        <h3 className="text-lg font-semibold text-[#05073C] line-clamp-1" dangerouslySetInnerHTML={{ __html: tour.tour_title }}></h3>
        <p className="text-[#4B5563] text-[14px] leading-[20px] line-clamp-4" dangerouslySetInnerHTML={{ __html: tour.tour_description.replace(/&nbsp;/g, ' ') }}></p>
        {/* Tour tags chips */}
        <div className="flex flex-wrap gap-2 mt-1 mb-1">
          {tagsLoading ? (
            <span className="text-xs text-gray-400">Loading tags...</span>
          ) : (
            <TourTagChips tags={tags} />
          )}
        </div>
        <div className="flex items-center justify-between">
          <div className="flex flex-col gap-[5px]">
            <div className="flex items-center gap-1.5">
              <img src={LocationIcon} alt="location" />
              <span className="text-[14px] text-[#4B5563] truncate max-w-[150px]">{tour.location.address}</span>
            </div>
            <div className="flex items-center gap-1.5">
              <img src={TimeIcon} alt="time" />
              <span className="text-[14px] text-[#4B5563]">{tour.duration} days</span>
            </div>
          </div>
          <div className="flex flex-col gap-1">
            {tour?.group_size && <div className="flex items-center gap-1">
              <img src="/group.svg" className="w-4 h-4" />
              <span className="text-[14px] text-[#4B5563]">{tour?.group_size}</span>
            </div>}
            <div className="flex items-end gap-1 ">
              <span className="block text-[10px] text-[#4B5563]">Starts from:</span>
              <span className="block text-[16px] text-[#1249CC] font-bold">৳ {Number(tour.adult_price).toLocaleString()}</span>
            </div>
          </div>
        </div>
        <div className="mt-auto pt-2">
          <button
            className="cursor-pointer bg-[#E7ECF9] text-[#0D3FC6] hover:bg-[#0D3FC6] hover:text-white transition-all duration-300 rounded-[4px] text-[14px] font-semibold py-[8px] px-[10px] w-full"
            onClick={() => handleViewDetails(tour.tour_id)}
          >
            View Details
          </button>
        </div>
      </div>
      {tour?.featured &&
        <div className="absolute top-2 left-2">
          <div className="text-[14px] px-2 rounded-full py-0.5 text-white bg-[#1249CC]">
            <div className="flex items-center gap-1">
              <span>Verified</span>
            </div>
          </div>
        </div>
      }
    </div>
  );
};

const TourDetailsCards = () => {
  const [activeTab, setActiveTab] = useState("cheapest");
  const [showSignInModal, setShowSignInModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [priceSort, setPriceSort] = useState("default");
  const [showSignUpModal, setShowSignUpModal] = useState(false);
  const [durationFilter, setDurationFilter] = useState("all");
  const [filteredTours, setFilteredTours] = useState<{ featured: TourItem[], cheapest: TourItem[] }>({ featured: [], cheapest: [] });
  const [tourData, setTourData] = useState<TourData>({ featured: [], cheapest: [] });
  const isAuthenticated = !!Cookies.get('accessToken');
  const [showVerifyPhoneOtpModal, setShowVerifyPhoneOtpModal] = useState(false);
  const { userDetails } = useAppSelector((state) => state.auth);
  const navigate = useNavigate();
  const { threadUid } = useParams();

  useEffect(() => {
    try {
      const paginateDetails = localStorage.getItem("paginateDetails");
      if (paginateDetails) {
        const parsedPaginateDetails = JSON.parse(paginateDetails);
        setTourData(parsedPaginateDetails?.tours || { featured: [], cheapest: [] });
      }
    } catch (error) {
      console.error("Error parsing localStorage data:", error);
      setTourData({ featured: [], cheapest: [] });
    }
  }, []);

  useEffect(() => {
    const applyFilters = (tours: TourItem[]) => {
      if (!tours || !Array.isArray(tours)) return [];

      // First apply search filter
      let filtered = [...tours];
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        filtered = filtered.filter(tour =>
          tour.tour_title?.toLowerCase().includes(searchLower) ||
          tour.tour_description?.toLowerCase().includes(searchLower) ||
          tour.location?.address?.toLowerCase().includes(searchLower)
        );
      }

      // Apply duration filter
      if (durationFilter !== "all") {
        filtered = filtered.filter(tour => {
          const duration = parseInt(tour.duration || "0");
          switch (durationFilter) {
            case "short": return duration <= 3;
            case "medium": return duration > 3 && duration <= 7;
            case "long": return duration > 7;
            default: return true;
          }
        });
      }

      // Apply price sorting
      if (priceSort !== "default") {
        filtered = [...filtered].sort((a, b) => {
          const priceA = parseFloat(a.adult_price || "0");
          const priceB = parseFloat(b.adult_price || "0");
          return priceSort === "low-high" ? priceA - priceB : priceB - priceA;
        });
      }

      return filtered;
    };

    setFilteredTours({
      featured: applyFilters(tourData.featured || []),
      cheapest: applyFilters(tourData.cheapest || [])
    });
  }, [tourData, searchTerm, priceSort, durationFilter]);

  const handleViewDetails = (tour_id: string) => {
    if (isAuthenticated) {
      if (userDetails?.otp_verify === true) {
        navigate(`/plan-details/${threadUid}/${tour_id}`);
      } else {
        setShowVerifyPhoneOtpModal(true);
      }
    } else {
      setShowSignInModal(true);
      return;
    }
  };

  const handleBack = () => {
    navigate(`/chat/${threadUid}`);
  };



  const handleSignUp = () => {
    setShowSignUpModal(true);
    setShowSignInModal(false);
  }

  const handleSignIn = () => {
    setShowSignInModal(true);
    setShowSignUpModal(false);
  }

  return (
    <div className="max-w-[1240px] mx-auto mb-10">
      <div className="mb-6 p-4 bg-white rounded-lg border border-[#E5E7EB] shadow-sm mt-5">
        <div className="flex flex-col sm:flex-row gap-4 items-center">
          <div className="w-full sm:flex-1">
            <div className="relative">
              <input
                type="text"
                placeholder="Search packages..."
                className="w-full px-4 py-2 pl-10 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0D3FC6] focus:border-transparent"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>

          <div className="w-full sm:w-auto">
            <select
              className="w-full px-4 py-2 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0D3FC6]"
              value={priceSort}
              onChange={(e) => setPriceSort(e.target.value)}
            >
              <option value="default">Price: Default</option>
              <option value="low-high">Price: Low to High</option>
              <option value="high-low">Price: High to Low</option>
            </select>
          </div>

          <div className="w-full sm:w-auto">
            <select
              className="w-full px-4 py-2 border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0D3FC6]"
              value={durationFilter}
              onChange={(e) => setDurationFilter(e.target.value)}
            >
              <option value="all">All Durations</option>
              <option value="short">Short (1-3 days)</option>
              <option value="medium">Medium (4-7 days)</option>
              <option value="long">Long (8+ days)</option>
            </select>
          </div>
        </div>

        <div className="mt-4 text-sm text-gray-500">
          Showing {activeTab === "cheapest" ? filteredTours.cheapest?.length || 0 : filteredTours.featured?.length || 0} results
          {searchTerm && <span> for "{searchTerm}"</span>}
        </div>
      </div>

      <div className="flex justify-between items-center mt-4 mb-[20px]">
        <div className="flex gap-[15px]">
          <button
            className={`text-[14px] px-[16px] py-[6px] leading-[22px] font-normal cursor-pointer rounded-[4px] ${activeTab === "cheapest"
              ? "bg-[#0D3FC6] text-white"
              : "bg-white border border-[#0D3FC6] text-[#0D3FC6]"
              }`}
            onClick={() => setActiveTab("cheapest")}
          >
            Cheapest
          </button>
          <button
            className={`text-[14px] px-[16px] py-[6px] leading-[22px] font-normal cursor-pointer rounded-[4px] ${activeTab === "featured"
              ? "bg-[#0D3FC6] text-white"
              : "bg-white border border-[#0D3FC6] text-[#0D3FC6]"
              }`}
            onClick={() => setActiveTab("featured")}
          >
            Verified
          </button>
        </div>

        <button
          onClick={handleBack}
          className="hidden md:flex  cursor-pointer items-center gap-2 py-2 px-4 bg-[#EEF1FB] rounded-lg text-[#05073C] font-medium"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 12H5M5 12L12 19M5 12L12 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
          Back
        </button>
      </div>

      {activeTab === "cheapest" && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTours.cheapest && filteredTours.cheapest.length > 0 ? (
            filteredTours.cheapest.map((tour, index) => (
              <TourCard key={`cheapest-${tour.tour_id || ''}-${tour.id || ''}-${index}`} tour={tour} category="cheapest" index={index} handleViewDetails={handleViewDetails} />
            ))
          ) : (
            <div className="col-span-full text-center py-8 text-[#4B5563]">
              No packages found matching your criteria
            </div>
          )}
        </div>
      )}

      {activeTab === "featured" && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTours.featured && filteredTours.featured.length > 0 ? (
            filteredTours.featured.map((tour, index) => (
              <TourCard key={`featured-${tour.tour_id || ''}-${tour.id || ''}-${index}`} tour={tour} category="featured" index={index} handleViewDetails={handleViewDetails} />
            ))
          ) : (
            <div className="col-span-full text-center py-8 text-[#4B5563]">
              No packages found matching your criteria
            </div>
          )}
        </div>
      )}

      {showSignInModal && <SignInModal onClose={() => setShowSignInModal(false)} onSignUpClick={handleSignUp} />}
      {showSignUpModal && <SignUpModal onClose={() => setShowSignUpModal(false)} onSignInClick={handleSignIn} />}
      {showVerifyPhoneOtpModal && <VerifyPhoneNumber isModal={true} onModalClose={() => setShowVerifyPhoneOtpModal(false)} onModalSuccess={() => {
        setShowVerifyPhoneOtpModal(false);
        window.location.reload();
      }} />}
    </div>
  );
};

export default TourDetailsCards;
