import { configureStore } from '@reduxjs/toolkit';
import authReducer from './features/auth/authSlice';
import { authApi } from './features/auth/authApi';
import sidebarReducer from './features/sidebar/sidebarSlice';
import chatReducer from './features/chat/chatSlice';
import { chatApi } from './features/chat/chatApi';
import { logger } from './middleware/logger';
import { contactUsApi } from './features/contact-us/contactUsApi';
import toursReducer from './features/tours/toursSlice';
import { toursApi } from './features/tours/toursApi';
import { bookingsApi } from './features/bookings/bookingsApi';
import { authErrorMiddleware } from '../utils/authUtils';
import { reviewsApi } from './features/reviews/reviews';
import { adminApi } from './features/admin/adminApi';
import { tourClickApi } from './features/external-tour-click/tourClickApi';
export const store = configureStore({
     reducer: {
          auth: authReducer,
          sidebar: sidebarReducer,
          tours: toursReducer,
          chat: chatReducer,
          [authApi.reducerPath]: authApi.reducer,
          [contactUsApi.reducerPath]: contactUsApi.reducer,
          [toursApi.reducerPath]: toursApi.reducer,
          [chatApi.reducerPath]: chatApi.reducer,
          [tourClickApi.reducerPath]: tourClickApi.reducer,
          [bookingsApi.reducerPath]: bookingsApi.reducer,
          [reviewsApi.reducerPath]: reviewsApi.reducer,
          [adminApi.reducerPath]: adminApi.reducer,
     },

     middleware: (getDefaultMiddleware) =>
          getDefaultMiddleware().concat(
               authApi.middleware,
               contactUsApi.middleware,
               toursApi.middleware,
               chatApi.middleware,
               bookingsApi.middleware,
               tourClickApi.middleware,
               reviewsApi.middleware,
               authErrorMiddleware,
               adminApi.middleware,
               logger,
          ),
     devTools: process.env.NODE_ENV !== 'production',
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch; 