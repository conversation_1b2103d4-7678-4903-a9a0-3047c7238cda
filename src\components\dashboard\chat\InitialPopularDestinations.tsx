import { useGetCountriesQuery } from "../../../store/features/tours/toursApi";
import DestinationList from "./DestinationList";
import { useDispatch, useSelector } from "react-redux";
import { addMessage, setLoading, threadUid as setThreadUid } from "../../../store/features/chat/chatSlice";
import { RootState } from "../../../store/store";
import { useState } from "react";
import SignInModal from "./SignInModal";
import SignUpModal from "./SignUpModal";

const PopularDestinations = ({ sendMessage, isSendingMessage }: { sendMessage: any, isSendingMessage: boolean }) => {
     const { data: countries, isLoading: isCountriesLoading } = useGetCountriesQuery({});
     const dispatch = useDispatch();
     const threadUid = useSelector((state: RootState) => state.chat.threadUid);
     const [showSignUpModal, setShowSignUpModal] = useState(false);
     const [showSignInModal, setShowSignInModal] = useState(false);

     const handleDestinationClick = async (destination: string) => {
          if (isSendingMessage) {
               return;
          }

          const message = `Please show me packages for ${destination}`;
          dispatch(addMessage({
               content: message,
               sender: "user"
          }));

          dispatch(setLoading(true));

          const messagePayload: {
               message: string;
               thread_uid?: string;
          } = {
               message: message
          };

          if (threadUid) {
               messagePayload.thread_uid = threadUid;
          }

          try {
               const response = await sendMessage(messagePayload).unwrap();
               const responseThreadUid = response?.data?.thread_uid;
               if (responseThreadUid) {
                    dispatch(setThreadUid(responseThreadUid));
                    window.history.pushState(
                         { threadUid: responseThreadUid },
                         '',
                         `/chat/${responseThreadUid}`
                    );
               }

               let aiResponseMessage = "";
               let responseType = "text";

               if (response.data?.ai_response?.type) {
                    responseType = response.data.ai_response.type;
               }

               if (response.data?.ai_response?.data?.text_response?.message) {
                    aiResponseMessage = response.data.ai_response.data.text_response.message;
               }
               else if (response.data?.ai_response?.text_response?.message) {
                    aiResponseMessage = response.data.ai_response.text_response.message;
               }
               else if (response.data?.ai_response?.response?.message) {
                    aiResponseMessage = response.data.ai_response.response.message;
               }

               if (aiResponseMessage) {
                    if (responseType === "json") {
                         dispatch(addMessage({
                              content: aiResponseMessage,
                              sender: "ai",
                              responseType: responseType,
                              tourDetails: response.data?.ai_response?.data || []
                         }));
                    } else if (responseType === "popular_destinations") {
                         dispatch(addMessage({
                              content: aiResponseMessage,
                              sender: "ai",
                              responseType: responseType,
                              popularDestinations: response?.data?.ai_response?.data
                         }));
                    } else if (responseType === "tour_packages") {
                         dispatch(addMessage({
                              content: aiResponseMessage,
                              sender: "ai",
                              responseType: responseType,
                              tourPackages: response?.data?.ai_response?.data,
                              meta_details: response?.data?.ai_response?.meta_details
                         }));
                    }
                    else {
                         dispatch(addMessage({
                              content: aiResponseMessage,
                              sender: "ai",
                              responseType: responseType
                         }));
                    }
               } else {
                    dispatch(addMessage({
                         content: "I've received your message. How can I help you with your travel plans?",
                         sender: "ai"
                    }));
               }

          } catch (error) {
               console.error("Error sending message:", error);

               dispatch(addMessage({
                    content: "Sorry, I couldn't process your request at the moment. Please try again later.",
                    sender: "ai"
               }));
          } finally {
               dispatch(setLoading(false));
          }
     }

     return (
          <div className={`${isSendingMessage ? "opacity-50" : ""}`}>
               <h2 className="text-[20px] leading-[24px] font-semibold text-[#05073C]">Popular Destinations</h2>
               <div className="w-[100%] overflow-x-auto scrollbar-hide" >
                    {isCountriesLoading ? <div className="w-full h-full flex items-center justify-center">
                         <p className="text-gray-500">Loading...</p>
                    </div> : <div className="flex gap-[16px] py-4">
                         {countries?.length > 0 ? countries?.map((destination: { id: string, name: string, image_url: string }, index: number) => (
                              <div
                                   key={`destination-${destination.id}-${index}`}
                                   onClick={() => handleDestinationClick(destination.name)}
                                   className="cursor-pointer"
                              >
                                   <DestinationList destination={destination} />
                              </div>
                         )) : <div className="w-full h-full flex items-center justify-center">
                              <p className="text-gray-500">No destinations found</p>
                         </div>}
                    </div>}
               </div>
               {showSignUpModal && <SignUpModal onClose={() => setShowSignUpModal(false)} onSignInClick={() => {
                    setShowSignUpModal(false);
                    setShowSignInModal(true);
               }} />}
               {showSignInModal && <SignInModal onClose={() => setShowSignInModal(false)} onSignUpClick={() => {
                    setShowSignInModal(false);
                    setShowSignUpModal(true);
               }} />}
          </div>
     )
}

export default PopularDestinations;
