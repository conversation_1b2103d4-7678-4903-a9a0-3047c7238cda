import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { threadUid as setThreadUid, setMessages, addMessage } from "../../../store/features/chat/chatSlice";
import EditIcon from "../../../assets/edit-icon.svg";

const NewChat = () => {
     const dispatch = useDispatch();
     const navigate = useNavigate();

     const handleNewChat = () => {
          dispatch(setThreadUid(null));
          dispatch(setMessages([]));
          dispatch(addMessage({
               content: "Hi there! I'm <PERSON>, your travel assistant. I'm here to help you find the best travel packages. To get started, where would you like to go? And how long are you planning to stay?",
               sender: "ai"
          }));
          navigate('/');
     };

     return (
          <>
               <div className="cursor-pointer" onClick={handleNewChat}>
                    <img src={EditIcon} alt="Edit" className="w-7 h-7" />
               </div>
          </>
     );
};

export default NewChat;