const AdminPackagesSkeleton = () => {
     return (
          <div className="w-full min-h-screen bg-gray-50">
               <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6 pb-8">
                    <div className="mb-6">
                         <div className="hidden sm:block">
                              <div className="border-b border-gray-200">
                                   <nav className="-mb-px flex space-x-8">
                                        {[1, 2].map((tab) => (
                                             <div
                                                  key={tab}
                                                  className="animate-pulse flex items-center pb-4"
                                             >
                                                  <div className="h-4 w-24 bg-gray-200 rounded"></div>
                                                  <div className="ml-2 h-5 w-8 bg-gray-200 rounded-full"></div>
                                             </div>
                                        ))}
                                   </nav>
                              </div>
                         </div>
                    </div>

                    {/* Table Container */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                         <div className="overflow-x-auto">
                              <table className="min-w-full divide-y divide-gray-200">
                                   <thead className="bg-gray-50">
                                        <tr>
                                             <th className="px-6 py-3 text-left">
                                                  <div className="h-3 w-24 bg-gray-200 rounded animate-pulse"></div>
                                             </th>
                                             <th className="px-6 py-3 text-left hidden sm:table-cell">
                                                  <div className="h-3 w-20 bg-gray-200 rounded animate-pulse"></div>
                                             </th>
                                             <th className="px-6 py-3 text-left">
                                                  <div className="h-3 w-16 bg-gray-200 rounded animate-pulse"></div>
                                             </th>
                                        </tr>
                                   </thead>
                                   <tbody className="bg-white divide-y divide-gray-200">
                                        {[...Array(10)].map((_, index) => (
                                             <tr key={index} className="animate-pulse">
                                                  <td className="px-6 py-4">
                                                       <div className="space-y-2">
                                                            <div className="h-4 w-48 bg-gray-200 rounded"></div>
                                                            <div className="h-3 w-32 bg-gray-200 rounded"></div>
                                                       </div>
                                                  </td>
                                                  <td className="px-6 py-4 hidden sm:table-cell">
                                                       <div className="h-4 w-32 bg-gray-200 rounded"></div>
                                                  </td>
                                                  <td className="px-6 py-4">
                                                       <div className="h-4 w-16 bg-gray-200 rounded"></div>
                                                  </td>
                                             </tr>
                                        ))}
                                   </tbody>
                              </table>
                         </div>

                         {/* Pagination Skeleton */}
                         <div className="px-6 py-3 flex items-center justify-between border-t border-gray-200">
                              <div className="flex items-center gap-2 animate-pulse">
                                   <div className="h-8 w-8 bg-gray-200 rounded"></div>
                                   <div className="h-8 w-8 bg-gray-200 rounded"></div>
                                   <div className="h-8 w-8 bg-gray-200 rounded"></div>
                              </div>
                         </div>
                    </div>
               </div>
          </div>
     );
};

export default AdminPackagesSkeleton;