const AdminChatThreadsSkeleton = () => {
     return (
          <div className="md:mt-0 mt-[60px] max-w-[1240px] sm:px-[30px] px-[10px] mx-auto py-6">
               {/* Table Skeleton */}
               <div className="bg-white rounded-lg border border-[#E5E7EB] shadow-sm overflow-hidden">
                    <div className="overflow-x-auto">
                         <table className="min-w-full divide-y divide-[#E5E7EB]">
                              <thead className="bg-gray-50">
                                   <tr>
                                        <th className="px-6 py-3 text-left">
                                             <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
                                        </th>
                                        <th className="px-6 py-3 text-left">
                                             <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                                        </th>
                                        <th className="px-6 py-3 text-left">
                                             <div className="h-4 bg-gray-200 rounded w-28 animate-pulse"></div>
                                        </th>
                                   </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-[#E5E7EB]">
                                   {[...Array(5)].map((_, index) => (
                                        <tr key={index} className="hover:bg-gray-50">
                                             <td className="px-6 py-4 whitespace-nowrap">
                                                  <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                                             </td>
                                             <td className="px-6 py-4 whitespace-nowrap">
                                                  <div className="h-4 bg-gray-200 rounded w-40 animate-pulse"></div>
                                             </td>
                                             <td className="px-6 py-4 whitespace-nowrap">
                                                  <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                                             </td>
                                        </tr>
                                   ))}
                              </tbody>
                         </table>
                    </div>
               </div>
          </div>
     );
};

export default AdminChatThreadsSkeleton;