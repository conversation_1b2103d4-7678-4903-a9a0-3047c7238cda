import { toast } from "react-hot-toast";
import airplane from "../../assets/plane-icon.svg";
import video from "../../assets/videos/forgot-pass-video.mp4";
import { useVerifyOtpMutation } from "../../store/features/auth/authApi";
import { useForm, Controller } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { useEffect, useRef, useState } from 'react';
import Cookies from 'js-cookie';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import LeftSide from "../../components/auth/LeftSide";

interface OtpInputs {
  otp: string[];
}

const OtpVerification = () => {
  const [verifyOtp] = useVerifyOtpMutation();
  const [isLoading, setIsLoading] = useState(true);
  const [isVerifying, setIsVerifying] = useState(false);
  const navigate = useNavigate();
  const forgetEmail = useSelector((state: RootState) => state.auth.forgotPasswordEmail);
  const { handleSubmit, control, setValue, reset } = useForm<OtpInputs>({
    defaultValues: {
      otp: ['', '', '', '', '', ''],
    },
  });
  const firstInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (firstInputRef.current) {
      firstInputRef.current.focus();
    }
  }, []);

  const onSubmit = async (data: OtpInputs) => {
    const otpValue = data.otp.join('');
    setIsVerifying(true);
    try {
      const response = await verifyOtp({ otp: otpValue, email: forgetEmail || "" });
      if (response.error) {
        // @ts-expect-error error
        toast.error(response?.error?.data?.error || "An unexpected error occurred");
        reset();
      } else {
        toast.success(response?.data?.msg || "OTP verified successfully");
        Cookies.set("secure_token", response.data.secure_token);
        navigate("/confirm-password");
      }
    } catch (error) {
      console.log("Error: ", error);
    } finally {
      setIsVerifying(false);
      reset();
    }
  };

  useEffect(() => {
    if (!forgetEmail) {
      navigate("/forgot-password");
    }
    setIsLoading(false);
  }, [forgetEmail, navigate]);

  return (
    <>
      {isLoading && <div className="flex justify-center items-center h-screen">
        <div className="w-10 h-10 border-t-transparent border-b-transparent border-r-transparent border-l-transparent border-t-2 border-b-2 border-r-2 border-l-2 border-blue-500 rounded-full animate-spin"></div>
      </div>}
      {!isLoading &&
        <div className="flex md:h-screen h-auto w-full">
          {/* Left side with video background */}
          <LeftSide video={video} title="Your next adventure is calling!" description="Log in and uncover the best travel deals in seconds." />

          {/* Right side with login form */}
          <div className="h-screen overflow-y-auto w-full md:w-1/2 bg-white flex flex-col justify-center items-center px-[20px] pt-[80px] py-[30px] relative">
            <div className="w-full max-w-[490px] py-[30px] mx-auto">
              <div className="absolute right-0 sm:w-[224px] w-[150px] mt-[-50px] ms-auto">
                <img src={airplane} alt="Airplane" className="w-full h-full" />
              </div>
              <div className="text-center mb-[35px]">
                <h1 className="lg:text-[55px] sm:text-[40px] text-[30px] font-bold sm:leading-[48px] md:leading-[40px] leading-[30px] text-[#0D3FC6] mb-[12px]">
                  Verification
                </h1>
                <p className="text-[#00000080] text-[16px]">
                  Enter the 6 digits OTP sent to your Email
                </p>
              </div>
              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="flex sm:gap-[10px] gap-[2px] justify-between items-center mb-[24px]">
                  {Array.from({ length: 6 }).map((_, index) => (
                    <Controller
                      key={index}
                      name={`otp.${index}`}
                      control={control}
                      render={({ field }) => (
                        <input
                          {...field}
                          ref={index === 0 ? firstInputRef : null}
                          type="text"
                          maxLength={1}
                          className="sm:w-[54px] w-[40px] sm:h-[54px] h-[40px] text-[14px] font-semibold text-[#000000] leading-[18px] border border-[#DDDFE4] focus:outline-none rounded-[8px] text-center"
                          onChange={(e) => {
                            const value = e.target.value;
                            if (/^\d*$/.test(value)) {
                              field.onChange(value);
                              if (value && index < 5) {
                                const nextSibling = document.querySelector<HTMLInputElement>(`input[name="otp.${index + 1}"]`);
                                if (nextSibling) {
                                  nextSibling.focus();
                                }
                              }
                            }
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Backspace' && !field.value && index > 0) {
                              const prevSibling = document.querySelector<HTMLInputElement>(`input[name="otp.${index - 1}"]`);
                              if (prevSibling) {
                                prevSibling.focus();
                              }
                            }
                          }}
                          onPaste={(e) => {
                            const paste = e.clipboardData.getData('text').replace(/\D/g, '');
                            if (paste.length === 6) {
                              for (let i = 0; i < 6; i++) {
                                setValue(`otp.${i}` as const, paste[i] || '');
                              }
                              e.preventDefault();
                              // Focus last input
                              const lastInput = document.querySelector<HTMLInputElement>(`input[name="otp.5"]`);
                              if (lastInput) lastInput.focus();
                            }
                          }}
                        />
                      )}
                    />
                  ))}
                </div>
                <button
                  className="w-full bg-gradient-to-r from-[#0D3FC6] to-[#3793FF] text-white py-[16px] rounded-[8px] font-medium hover:bg-blue-700 transition-colors cursor-pointer !rounded-button whitespace-nowrap text-[14px] leading-[18px] uppercase flex items-center justify-center"
                  type="submit"
                  disabled={isVerifying}
                >
                  {isVerifying ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Verifying...
                    </>
                  ) : (
                    'Verify'
                  )}
                </button>
              </form>

              <div className="flex items-center my-[25px] w-full max-w-[290px] mx-auto">
                <div className="flex-1 border-t border-[#1C1C1C33]"></div>
                <span className="px-4 text-[#05073C] text-[14px] leading-[18px]">
                  OR
                </span>
                <div className="flex-1 border-t border-[#1C1C1C33]"></div>
              </div>
              <div className="text-center text-[#05073C] font-normal mt-[30px] text-[14px] leading-[18px]">
                Didn't receive the code? {" "}
                <a href="/" className="text-[#0D3FC6] font-semibold">
                  Resend
                </a>
              </div>
            </div>
          </div>
        </div>}
    </>
  );
};

export default OtpVerification;
