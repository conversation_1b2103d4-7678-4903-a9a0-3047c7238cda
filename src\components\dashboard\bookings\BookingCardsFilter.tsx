import React from 'react';
import { useState } from 'react';

interface FilterProps {
     onFilterApply: (filters: FilterValues) => void;
     onClose: () => void;
     initialFilters: FilterValues;
}

export interface FilterValues {
     dateRange: {
          from: string;
          to: string;
     };
     priceRange: {
          min: string;
          max: string;
     };
     status: string;
}

const BookingCardsFilter: React.FC<FilterProps> = ({ onFilterApply, onClose, initialFilters }) => {
     const [filters, setFilters] = useState<FilterValues>(initialFilters);

     // Get today's date in YYYY-MM-DD format
     const today = new Date().toISOString().split('T')[0];

     const handleDateChange = (field: 'from' | 'to', value: string) => {
          let newFromDate = filters.dateRange.from;
          let newToDate = filters.dateRange.to;

          if (field === 'from') {
               newFromDate = value;
               // If 'to' date is earlier than new 'from' date, reset it
               if (newToDate && newToDate < value) {
                    newToDate = value;
               }
          } else {
               newToDate = value;
               // If 'from' date is later than new 'to' date, reset it
               if (newFromDate && newFromDate > value) {
                    newFromDate = value;
               }
          }

          setFilters({
               ...filters,
               dateRange: { from: newFromDate, to: newToDate }
          });
     };

     const handleSubmit = (e: React.FormEvent) => {
          e.preventDefault();
          onFilterApply(filters);
          onClose();
     };

     const handleReset = () => {
          const emptyFilters = {
               dateRange: { from: '', to: '' },
               priceRange: { min: '', max: '' },
               status: ''
          };
          setFilters(emptyFilters);
          onFilterApply(emptyFilters);
          onClose();
     };

     return (
          <div className="fixed inset-0 bg-black/70 bg-opacity-50 z-[999] flex items-center justify-center">
               <div className="bg-white rounded-lg p-6 w-full max-w-md">
                    <div className="flex justify-between items-center mb-4">
                         <h2 className="text-xl font-semibold">Filter Bookings</h2>
                         <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
                              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                              </svg>
                         </button>
                    </div>

                    <form onSubmit={handleSubmit}>
                         <div className="mb-4">
                              <label className="block text-sm font-medium text-gray-700 mb-2">Order Date Range</label>
                              <div className="grid grid-cols-2 gap-2">
                                   <div>
                                        <label className="text-xs text-gray-500 mb-1 block">From</label>
                                        <input
                                             type="date"
                                             className="border border-gray-300 rounded-md px-3 py-2 w-full"
                                             value={filters.dateRange.from}
                                             max={today}
                                             onChange={(e) => handleDateChange('from', e.target.value)}
                                        />
                                   </div>
                                   <div>
                                        <label className="text-xs text-gray-500 mb-1 block">To</label>
                                        <input
                                             type="date"
                                             className="border border-gray-300 rounded-md px-3 py-2 w-full"
                                             value={filters.dateRange.to}
                                             min={filters.dateRange.from || undefined}
                                             max={today}
                                             onChange={(e) => handleDateChange('to', e.target.value)}
                                        />
                                   </div>
                              </div>
                              <p className="text-xs text-gray-500 mt-1">Filter orders by creation date</p>
                         </div>

                         <div className="mb-6">
                              <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
                              <div className="grid grid-cols-2 gap-2">
                                   <input
                                        type="number"
                                        placeholder="Min"
                                        className="border border-gray-300 rounded-md px-3 py-2"
                                        value={filters.priceRange.min}
                                        onChange={(e) => setFilters({
                                             ...filters,
                                             priceRange: { ...filters.priceRange, min: e.target.value }
                                        })}
                                   />
                                   <input
                                        type="number"
                                        placeholder="Max"
                                        className="border border-gray-300 rounded-md px-3 py-2"
                                        value={filters.priceRange.max}
                                        onChange={(e) => setFilters({
                                             ...filters,
                                             priceRange: { ...filters.priceRange, max: e.target.value }
                                        })}
                                   />
                              </div>
                         </div>

                         <div className="flex justify-end gap-2">
                              <button
                                   type="button"
                                   onClick={handleReset}
                                   className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                              >
                                   Reset
                              </button>
                              <button
                                   type="submit"
                                   className="px-4 py-2 text-sm font-medium text-white bg-[#0D3FC6] rounded-md hover:bg-blue-700"
                              >
                                   Apply Filters
                              </button>
                         </div>
                    </form>
               </div>
          </div>
     );
};

export default BookingCardsFilter;