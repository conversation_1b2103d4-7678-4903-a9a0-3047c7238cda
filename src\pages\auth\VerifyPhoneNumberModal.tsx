import AuthButton from "../../components/auth/AuthButton";
import { usePhoneNumberOtpMutation } from "../../store/features/auth/authApi";
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { useDispatch } from 'react-redux';
import { setForgotPasswordEmail } from '../../store/features/auth/authSlice';
import Cookies from 'js-cookie';

interface phoneInputs {
     phone: string;
}

interface VerifyPhoneNumberModalProps {
     isOpen: boolean;
     onClose: () => void;
     onSuccess: () => void;
}

const VerifyPhoneNumberModal = ({ isOpen, onClose, onSuccess }: VerifyPhoneNumberModalProps) => {
     const [phoneNumberOtp, { isLoading }] = usePhoneNumberOtpMutation();
     const dispatch = useDispatch();

     const {
          register,
          handleSubmit,
          formState: { errors }
     } = useForm<phoneInputs>();

     const onSubmit = async (data: phoneInputs) => {
          try {
               const response = await phoneNumberOtp(data);
               const verification_id = response?.data?.verification_id;
               Cookies.set("verification_id", verification_id);
               Cookies.set("phone_number", data.phone);
               if (response.error) {
                    // @ts-expect-error error
                    toast.error(response.error.data?.msg || "An unexpected error occurred");
                    return;
               } else {
                    toast.success(response?.data?.msg || "OTP sent to your phone number");
                    dispatch(setForgotPasswordEmail(data.phone));
                    onSuccess();
               }
          } catch (error) {
               console.log("Error: ", error);
          }
     };

     if (!isOpen) return null;

     return (
          <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-[999]">
               <div className="bg-white rounded-lg p-8 max-w-[490px] w-full relative">
                    <button
                         onClick={onClose}
                         className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
                    >
                         ✕
                    </button>

                    <div className="text-center mb-[35px]">
                         <h1 className="text-[30px] font-bold leading-[30px] text-[#0D3FC6] mb-[12px]">
                              Verify Phone Number
                         </h1>
                         <p className="text-[#00000080] text-[16px]">
                              Please verify your phone number to continue
                         </p>
                    </div>

                    <form onSubmit={handleSubmit(onSubmit)}>
                         <div className="mb-[24px] relative w-full">
                              <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">
                                   Phone Number
                              </label>
                              <div className="relative">
                                   <img
                                        src="/phone.svg"
                                        alt="user"
                                        className="w-[30px] h-[20px] absolute sm:left-[30px] left-[15px] top-1/2 transform -translate-y-1/2"
                                   />
                                   <input
                                        {...register("phone", {
                                             required: "Phone number is required",
                                             pattern: {
                                                  value: /^(\+?88)?01[3-9]\d{8}$/,
                                                  message: "Enter a valid Bangladeshi phone number (e.g. +8801XXXXXXXXX or 01XXXXXXXXX)"
                                             }
                                        })}
                                        type="text"
                                        className="w-full py-[17px] px-[18px] sm:ps-[70px] ps-[50px] text-[14px] font-semibold text-[#000000] leading-[18px] border border-[#0D9BC6] focus:outline-none placeholder:text-[#00000080] rounded-[8px]"
                                        placeholder="Enter your phone number"
                                   />
                              </div>
                              {errors?.phone && (
                                   <span className="text-red-500 text-sm mt-1">{errors.phone.message}</span>
                              )}
                         </div>
                         <AuthButton isLoading={isLoading}>Send OTP</AuthButton>
                    </form>
               </div>
          </div>
     );
};

export default VerifyPhoneNumberModal;
