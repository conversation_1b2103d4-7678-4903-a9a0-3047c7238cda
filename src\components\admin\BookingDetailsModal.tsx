import { useState, useEffect } from 'react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { BookingDetailsModalProps } from './BookingsTable'

const BookingDetailsModal = ({ isOpen, onClose, booking }: { isOpen: boolean, onClose: () => void, booking: BookingDetailsModalProps | null }) => {
     const [activeTab, setActiveTab] = useState('tour')

     // Handle ESC key press
     useEffect(() => {
          const handleEsc = (e: KeyboardEvent) => {
               if (e.key === 'Escape') onClose()
          }
          window.addEventListener('keydown', handleEsc)
          return () => window.removeEventListener('keydown', handleEsc)
     }, [onClose])

     // Prevent body scroll when modal is open
     useEffect(() => {
          if (isOpen) {
               document.body.style.overflow = 'hidden'
          } else {
               document.body.style.overflow = 'unset'
          }
          return () => {
               document.body.style.overflow = 'unset'
          }
     }, [isOpen])

     if (!isOpen) return null

     const renderTabContent = () => {
          switch (activeTab) {
               case 'tour':
                    return (
                         <div className="space-y-6">
                              <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
                                   <div className="px-6 py-4 border-b border-gray-200">
                                        <h4 className="text-lg font-semibold text-gray-900">Tour Information</h4>
                                   </div>
                                   <div className="p-6">
                                        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                             <div>
                                                  <p className="text-sm font-medium text-gray-500">Tour Name</p>
                                                  <p className="mt-1 text-sm text-gray-900">{booking?.tour?.tour_title}</p>
                                             </div>
                                             <div>
                                                  <p className="text-sm font-medium text-gray-500">Location</p>
                                                  <p className="mt-1 text-sm text-gray-900">{booking?.tour?.location?.address}</p>
                                             </div>
                                             <div>
                                                  <p className="text-sm font-medium text-gray-500">Duration</p>
                                                  <p className="mt-1 text-sm text-gray-900">
                                                       {new Date(booking?.duration?.start_date ?? '').toLocaleDateString()} - {new Date(booking?.duration?.end_date ?? '').toLocaleDateString()}
                                                  </p>
                                             </div>
                                             <div>
                                                  <p className="text-sm font-medium text-gray-500">Amount</p>
                                                  <p className="mt-1 text-sm text-gray-900">৳ {Number(booking?.booking_amount).toLocaleString()}</p>
                                             </div>
                                             {booking?.tour?.description && (
                                                  <div className="col-span-2">
                                                       <p className="text-sm font-medium text-gray-500">Description</p>
                                                       <p className="mt-1 text-sm text-gray-900">{booking?.tour?.description}</p>
                                                  </div>
                                             )}
                                        </div>
                                   </div>
                              </div>
                         </div>
                    )
               case 'traveler':
                    return (
                         <div className="space-y-6">
                              <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
                                   <div className="px-6 py-4 border-b border-gray-200">
                                        <h4 className="text-lg font-semibold text-gray-900">Traveler Summary</h4>
                                   </div>
                                   <div className="p-6">
                                        <div className="grid grid-cols-1 gap-6 sm:grid-cols-3">
                                             <div>
                                                  <p className="text-sm font-medium text-gray-500">Adults</p>
                                                  <p className="mt-1 text-sm text-gray-900">{booking?.traveller_numbers?.adult}</p>
                                             </div>
                                             <div>
                                                  <p className="text-sm font-medium text-gray-500">Children</p>
                                                  <p className="mt-1 text-sm text-gray-900">{booking?.traveller_numbers?.child}</p>
                                             </div>
                                             <div>
                                                  <p className="text-sm font-medium text-gray-500">Infants</p>
                                                  <p className="mt-1 text-sm text-gray-900">{booking?.traveller_numbers?.infant}</p>
                                             </div>
                                        </div>
                                   </div>
                              </div>

                              {(booking?.traveller_details?.length ?? 0) > 0 && (
                                   <div className="space-y-4">
                                        {booking?.traveller_details?.map((traveler, index) => (
                                             <div key={traveler.id} className="bg-white rounded-xl border border-gray-200 overflow-hidden">
                                                  <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                                       <h4 className="text-lg font-semibold text-gray-900">
                                                            Traveler {index + 1}
                                                       </h4>

                                                  </div>
                                                  <div className="p-6">
                                                       <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                                                            <div>
                                                                 <p className="text-sm font-medium text-gray-500">Full Name</p>
                                                                 <p className="mt-1 text-sm text-gray-900">
                                                                      {traveler.first_name} {traveler.last_name}
                                                                 </p>
                                                            </div>

                                                            <div>
                                                                 <p className="text-sm font-medium text-gray-500">Date of Birth</p>
                                                                 <p className="mt-1 text-sm text-gray-900">
                                                                      {new Date(traveler.date_of_birth).toLocaleDateString('en-US', {
                                                                           year: 'numeric',
                                                                           month: 'long',
                                                                           day: 'numeric'
                                                                      })}
                                                                 </p>
                                                            </div>

                                                            <div>
                                                                 <p className="text-sm font-medium text-gray-500">Nationality</p>
                                                                 <p className="mt-1 text-sm text-gray-900">{traveler.nationality}</p>
                                                            </div>

                                                            <div>
                                                                 <p className="text-sm font-medium text-gray-500">Passport Number</p>
                                                                 <p className="mt-1 text-sm text-gray-900">{traveler.passport_number}</p>
                                                            </div>
                                                            <div>
                                                                 <p className="text-sm font-medium text-gray-500">Gender</p>
                                                                 <div className={`px-3 py-0.5 w-fit rounded-full text-sm font-medium
                                                            ${traveler.gender === 'Female'
                                                                           ? 'bg-pink-50 text-pink-700 border border-pink-200'
                                                                           : 'bg-blue-50 text-blue-700 border border-blue-200'}`}>
                                                                      {traveler.gender}
                                                                 </div>
                                                            </div>


                                                            {traveler.special_requirements && (
                                                                 <div className="col-span-full bg-gray-50 rounded-lg p-4">
                                                                      <p className="text-sm font-medium text-gray-500">Special Requirements</p>
                                                                      <p className="mt-1 text-sm text-gray-900">{traveler.special_requirements}</p>
                                                                 </div>
                                                            )}
                                                       </div>
                                                  </div>
                                             </div>
                                        ))}
                                   </div>
                              )}
                         </div >
                    )
               case 'contact':
                    return (
                         <div className="space-y-6">
                              <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
                                   <div className="px-6 py-4 border-b border-gray-200">
                                        <h4 className="text-lg font-semibold text-gray-900">Contact Information</h4>
                                   </div>
                                   <div className="p-6">
                                        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                             <div>
                                                  <p className="text-sm font-medium text-gray-500">Full Name</p>
                                                  <p className="mt-1 text-sm text-gray-900">
                                                       {booking?.contact_details?.first_name} {booking?.contact_details?.last_name}
                                                  </p>
                                             </div>
                                             <div>
                                                  <p className="text-sm font-medium text-gray-500">Email</p>
                                                  <p className="mt-1 text-sm text-gray-900">{booking?.contact_details?.email}</p>
                                             </div>
                                             <div>
                                                  <p className="text-sm font-medium text-gray-500">Phone</p>
                                                  <p className="mt-1 text-sm text-gray-900">{booking?.contact_details?.phone}</p>
                                             </div>
                                             <div>
                                                  <p className="text-sm font-medium text-gray-500">Address</p>
                                                  <p className="mt-1 text-sm text-gray-900">{booking?.contact_details?.address}</p>
                                             </div>
                                        </div>
                                   </div>
                              </div>
                         </div>
                    )
               case 'order':
                    // Console log to debug payment method data
                    console.log('Booking order data:', booking?.order);
                    console.log('Payment method:', booking?.order?.payment_method);
                    console.log('Order object keys:', booking?.order ? Object.keys(booking.order) : 'No order object');
                    console.log('Full booking object:', booking);
                    console.log('Booking object keys:', booking ? Object.keys(booking) : 'No booking object');

                    return (
                         <div className="space-y-6">
                              <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
                                   <div className="px-6 py-4 border-b border-gray-200">
                                        <h4 className="text-lg font-semibold text-gray-900">Order Information</h4>
                                   </div>
                                   <div className="p-6">
                                        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                                             <div>
                                                  <p className="text-sm font-medium text-gray-500">Order ID</p>
                                                  <p className="mt-1 text-sm text-gray-900">#{booking?.order?.order_id}</p>
                                             </div>
                                             <div>
                                                  <p className="text-sm font-medium text-gray-500">Order Date</p>
                                                  <p className="mt-1 text-sm text-gray-900">
                                                       {new Date(booking?.order?.order_date ?? '').toLocaleDateString()}
                                                  </p>
                                             </div>
                                             <div>
                                                  <p className="text-sm font-medium text-gray-500">Amount</p>
                                                  <p className="mt-1 text-sm text-gray-900">
                                                       {booking?.order?.order_currency} {Number(booking?.order?.order_amount).toLocaleString()}
                                                  </p>
                                             </div>
                                             <div>
                                                  <p className="text-sm font-medium text-gray-500">Payment Status</p>
                                                  <p className="mt-1 text-sm text-gray-900">
                                                       {booking?.order?.payment_status?.toLowerCase() === 'completed' || booking?.booking_status?.toLowerCase() === 'completed'
                                                         ? 'Completed'
                                                         : (booking?.order?.payment_status || 'Pending')}
                                                  </p>
                                             </div>
                                             {booking?.order?.payment_method && (
                                                  <div>
                                                       <p className="text-sm font-medium text-gray-500">Payment Method</p>
                                                       <p className="mt-1 text-sm text-gray-900">{booking?.order?.payment_method}</p>
                                                  </div>
                                             )}
                                             {booking?.order?.unique_payment_id && (
                                                  <div className="col-span-2">
                                                       <p className="text-sm font-medium text-gray-500">Payment ID</p>
                                                       <p className="mt-1 text-sm text-gray-900">{booking?.order?.unique_payment_id}</p>
                                                  </div>
                                             )}
                                             {booking?.order?.status_reason && (
                                                  <div className="col-span-2">
                                                       <p className="text-sm font-medium text-gray-500">Status Reason</p>
                                                       <p className="mt-1 text-sm text-gray-900">{booking?.order?.status_reason}</p>
                                                  </div>
                                             )}
                                            
                                        </div>
                                   </div>
                              </div>
                         </div>
                    )
          }
     }

     const tabs = [
          {
               name: 'Tour Details', id: 'tour', icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                    </svg>
               )
          },
          {
               name: 'Traveler Info', id: 'traveler', icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
               )
          },

          {
               name: 'Contact', id: 'contact', icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
               )
          },
          {
               name: 'Order Details', id: 'order', icon: (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
               )
          }
     ]

     return (
          <div className="fixed inset-0 z-[999] overflow-y-auto">
               {/* Backdrop */}
               <div
                    className="fixed inset-0 bg-black/70 transition-opacity"
                    onClick={onClose}
               />

               {/* Modal */}
               <div className="flex min-h-full items-center justify-center p-4">
                    <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all w-full max-w-4xl animate-modal-enter">
                         {/* Close Button */}
                         <button
                              type="button"
                              className="absolute right-4 top-4 z-10 rounded-md bg-white text-gray-400 hover:text-gray-500"
                              onClick={onClose}
                         >
                              <span className="sr-only">Close</span>
                              <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                         </button>

                         <div className="flex h-[600px]">
                              {/* Left Sidebar */}
                              <div className="w-64 bg-gray-50 border-r border-gray-200">
                                   <div className="px-4 py-5 border-b border-gray-200">
                                        <h3 className="text-lg font-semibold leading-6 text-gray-900">
                                             Booking #{booking?.order?.order_id}
                                        </h3>
                                   </div>
                                   <div className="flex flex-col py-2">
                                        {tabs.map((tab) => (
                                             <button
                                                  key={tab.id}
                                                  onClick={() => setActiveTab(tab.id)}
                                                  className={`flex items-center gap-3 px-4 py-3 text-sm font-medium text-left
                                                       focus:outline-none transition-colors duration-200 cursor-pointer
                                                       ${activeTab === tab.id
                                                            ? 'bg-white text-blue-600 border-r-2 border-blue-600'
                                                            : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                                                       }`}
                                             >
                                                  {tab.icon}
                                                  {tab.name}
                                             </button>
                                        ))}
                                   </div>
                              </div>

                              {/* Main Content Area */}
                              <div className="flex-1 overflow-y-auto">
                                   <div className="p-6">
                                        {renderTabContent()}
                                   </div>
                              </div>
                         </div>
                    </div>
               </div>
          </div>
     )
}

export default BookingDetailsModal