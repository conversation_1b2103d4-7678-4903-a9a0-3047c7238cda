import { useState, useEffect, useRef } from "react";
import AiChatIcon from "../../../assets/ai-chat-icon.svg";
import PaymentsIcon from "../../../assets/payment-icon.svg";
import BookingsIcon from "../../../assets/bookings-icon.svg";
import ContactUsIcon from "../../../assets/contact-us-icon.svg";
import TermsIcon from "../../../assets/terms-icon.svg";
import PrivacyPolicyIcon from "../../../assets/privacy-policy-icon.svg";
import ArrowDownIcon from "../../../assets/arrow-bottom.svg";
import { Link, useLocation, useNavigate } from "react-router-dom";
import SidebarDropdown from "./SidebarDropdown";
import Cookies from 'js-cookie';
import { setMessages, threadUid as setThreadUid, addMessage } from "../../../store/features/chat/chatSlice";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store/store";
import History from "../history/History";

const Sidebar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const profileRef = useRef<HTMLDivElement>(null);
  const location = useLocation();
  const isAuthenticated = !!Cookies.get('accessToken');
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { userDetails } = useSelector((state: RootState) => state.auth);
  const isLoading = useSelector((state: RootState) => state.chat.isLoading);

  useEffect(() => {
    const setVh = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    setVh();
    window.addEventListener('resize', setVh);
    window.addEventListener('orientationchange', setVh);

    return () => {
      window.removeEventListener('resize', setVh);
      window.removeEventListener('orientationchange', setVh);
    };
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (profileRef.current && !profileRef.current.contains(event.target as Node)) {
        setShowProfile(false);
      }
    };

    if (showProfile) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showProfile]);

  const handleLogoClick = () => {
    if (isLoading) return; // Disable logo click while message is being sent

    dispatch(setThreadUid(null));
    dispatch(setMessages([]));
    dispatch(addMessage({
      content: "Hi there! I'm Zoe, your travel assistant. I'm here to help you find the best travel packages. To get started, where would you like to go? And how long are you planning to stay?",
      sender: "ai"
    }));
    navigate("/");
    setIsOpen(false); // Close sidebar on logo click

    // Update the URL without causing a navigation
    window.history.pushState(
      {},
      '',
      '/'
    );
  }

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  const isActive = (path: string) => {
    // Check if the path is exactly matched
    if (location.pathname === path) {
      return true;
    }

    // Special case for AI Chat - should be active for all chat-related paths
    if (path === "/" && (location.pathname.startsWith("/chat/") || location.pathname === "/chat")) {
      return true;
    }

    return false;
  };

  const handleNavClick = () => {
    setIsOpen(false); // Close the sidebar
  };

  const handleAiChatClick = () => {
    if (isLoading) return; // Disable logo click while message is being sent
    setIsOpen(false);
    dispatch(setThreadUid(null));
    dispatch(setMessages([]));
    dispatch(addMessage({
      content: "Hi there! I'm Zoe, your travel assistant. I'm here to help you find the best travel packages. To get started, where would you like to go? And how long are you planning to stay?",
      sender: "ai"
    }));
    navigate("/");
    setIsOpen(false); // Close sidebar on logo click

    // Update the URL without causing a navigation
    window.history.pushState(
      {},
      '',
      '/'
    );
  }

  return (
    <div className="w-[100%] max-w-[270px] sm:max-w-[350px] md:max-w-auto md:w-[275px] absolute">

      <button
        className="md:hidden flex flex-col justify-center items-center gap-[4px] fixed top-4 left-4 z-[98] bg-[#e7ecf9] h-[34px] w-[34px] rounded-[8px]"
        onClick={toggleSidebar}
      >
        <span className="block w-[18px] h-[2px] bg-[#0D3FC6]"></span>
        <span className="block w-[18px] h-[2px] bg-[#0D3FC6]"></span>
        <span className="block w-[18px] h-[2px] bg-[#0D3FC6]"></span>
      </button>

      {isOpen && (
        <div
          className="fixed top-0 left-0 w-full h-full bg-black opacity-50 z-[999]"
          onClick={toggleSidebar}
        ></div>
      )}

      <div
        className={`fixed z-[999] top-0 left-0 h-full w-[100%] max-w-[270px] sm:max-w-[350px] md:max-w-auto md:w-[275px] bg-white overflow-[unset] transition-transform transform  ${isOpen ? "translate-x-0" : "-translate-x-full"
          } md:translate-x-0 border-r border-gray-200`}
      >
        <div className="py-[8.2px] px-[16px] flex items-center justify-between border-b border-[#E5E7EB] gap-[10px]">
          <div className="flex items-center cursor-pointer" onClick={handleLogoClick}>
            <img src="/chat-logo.svg" alt="Logo" className="w-[180px] " />
          </div>
        </div>

        <div
          className="px-[16px] overflow-y-auto"
          style={{
            height: 'calc(var(--vh, 1vh) * 100 - 140px)'
          }}
        >
          <nav className="pt-[8px]">
            <Link
              to="/"
              className={`flex items-center px-[10px] py-[8px] rounded-[8px] text-[16px] leading-[24px] font-semibold mb-[8px] gap-[12px] ${isActive("/")
                ? "bg-[#E7ECF9] text-[#2563EB]"
                : "text-[#636C76] hover:bg-[#E7ECF9]"
                }`}
              onClick={handleAiChatClick}
            >
              <img src={AiChatIcon} alt="AI Chat" className="w-4.5 h-4.5 " />
              <span>AI Chat</span>
            </Link>

            {isAuthenticated && (
              <>
                <Link
                  to="/payments"
                  className={`flex items-center px-[10px] py-[8px] rounded-[8px] text-[16px] leading-[24px] font-semibold mb-[8px] gap-[12px] ${isActive("/payments")
                    ? "bg-[#E7ECF9] text-[#2563EB]"
                    : "text-[#636C76] hover:bg-[#E7ECF9]"
                    }`}
                  onClick={handleNavClick}
                >
                  <img src={PaymentsIcon} alt="Payments" className="" />
                  <span>Payments</span>
                </Link>
                <Link
                  to="/bookings"
                  className={`flex items-center px-[10px] py-[8px] rounded-[8px] text-[16px] leading-[24px] font-semibold mb-[8px] gap-[12px] ${isActive("/bookings")
                    ? "bg-[#E7ECF9] text-[#2563EB]"
                    : "text-[#636C76] hover:bg-[#E7ECF9]"
                    }`}
                  onClick={handleNavClick}
                >
                  <img src={BookingsIcon} alt="Bookings" className="" />
                  <span>Bookings</span>
                </Link>
              </>
            )}

            <Link
              to="/contact"
              className={`flex items-center px-[10px] py-[8px] rounded-[8px] text-[16px] leading-[24px] font-semibold mb-[8px] gap-[12px] ${isActive("/contact")
                ? "bg-[#E7ECF9] text-[#2563EB]"
                : "text-[#636C76] hover:bg-[#E7ECF9]"
                }`}
              onClick={handleNavClick}
            >
              <img src={ContactUsIcon} alt="Contact Us" className="" />
              <span>Contact Us</span>
            </Link>
          </nav>

          <div className="pt-[8px] border-t border-[#E5E7EB]">
            <h3 className="text-[18px] font-semibold text-[#05073C] mb-2 ps-[3px]">
              Legal
            </h3>
            <Link
              to="/terms"
              className={`flex items-center px-[10px] py-[8px] rounded-[8px] text-[16px] leading-[24px] font-semibold mb-[8px] gap-[12px] ${isActive("/terms")
                ? "bg-[#E7ECF9] text-[#2563EB]"
                : "text-[#636C76] hover:bg-[#E7ECF9]"
                }`}
              onClick={handleNavClick}
            >
              <img src={TermsIcon} alt="Terms & Conditions" className="" />
              <span>Terms & Conditions</span>
            </Link>
            <Link
              to="/privacy-policy"
              className={`flex items-center px-[10px] py-[8px] rounded-[8px] text-[16px] leading-[24px] font-semibold mb-[8px] gap-[12px] ${isActive("/privacy-policy")
                ? "bg-[#E7ECF9] text-[#2563EB]"
                : "text-[#636C76] hover:bg-[#E7ECF9]"
                }`}
              onClick={handleNavClick}
            >
              <img src={PrivacyPolicyIcon} alt="Privacy Policy" className="" />
              <span>Privacy Policy</span>
            </Link>
            {isAuthenticated && <div className="pt-[8px] border-t border-[#E5E7EB]">
              <h3 className="text-[18px] font-semibold text-[#05073C] pb-[10px] ps-[3px]">
                History
              </h3>
              <History setIsOpen={setIsOpen} />
            </div>}
          </div>
        </div>

        {isAuthenticated ? (
          <div
            className="flex items-center p-[16px] bg-white border-t border-[#E5E7EB] relative cursor-pointer"
            onClick={() => setShowProfile(!showProfile)}
            ref={profileRef}
          >
            <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center mr-3">
              <span className="text-gray-600 capitalize">{userDetails?.username?.charAt(0)}</span>
            </div>
            <div>
              <p className="text-[16px] leading-[24px] text-[#05073C] font-semibold truncate max-w-[150px]">
                {userDetails?.username}
              </p>
              <p className="text-[14px] leading-[20px] text-[#6B7280] truncate max-w-[150px]">
                {userDetails?.email}
              </p>
            </div>
            <div>
              <img
                src={ArrowDownIcon}
                alt="Arrow Down"
                className="absolute right-[10px] top-[50%] translate-y-[-50%] rotate-180"
              />
            </div>
            {showProfile &&
              <SidebarDropdown
                onClose={() => {
                  setShowProfile(false);
                  setIsOpen(false); // This will also close the sidebar on mobile
                }}
              />
            }
          </div>
        ) : (
          <div className="p-[16px] border-t border-[#E5E7EB]">
            <Link
              to="/login"
              className="block w-full py-2 px-4 bg-[#2563EB] text-white text-center font-semibold rounded-md"
              onClick={handleNavClick}
            >
              Sign In
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
