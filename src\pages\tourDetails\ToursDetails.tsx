import { useParams, useNavigate } from "react-router-dom";
import TourDetailsCards from "../../components/dashboard/chat/TourDetailsCards";


const ToursDetails = () => {
     const navigate = useNavigate();
     const { threadUid } = useParams();
     const handleBack = () => {
          navigate(`/chat/${threadUid}`);
     };
     return (
          <div className="flex">
               <div className="md:ms-[275px] h-screen overflow-y-auto flex-1">

                    <div className="flex justify-between items-center fixed md:static w-full bg-white py-[15px] sm:py-[14px] sm:px-[30px] pl-[70px] pr-[15px] border-b z-10 border-[#E5E7EB] shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)]">
                         <h1 className="text-[20px] sm:text-[24px] leading-[32px] font-bold text-[#05073C]">
                              All Packages
                         </h1>
                         <button
                              onClick={handleBack}
                              className="flex md:hidden cursor-pointer items-center gap-2 py-2 px-4 bg-[#EEF1FB] rounded-lg text-[#05073C] font-medium"
                         >
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                   <path d="M19 12H5M5 12L12 19M5 12L12 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                              </svg>
                              Back
                         </button>
                    </div>

                    <div className="max-w-[1240px] md:mt-0 mt-[80px] sm:px-[30px] px-[10px] mx-auto">
                         {/* Tabs */}
                         <TourDetailsCards />
                    </div>
               </div>
          </div>
     );
};

export default ToursDetails;