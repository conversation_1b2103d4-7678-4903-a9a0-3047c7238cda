import userIcon from '../../../assets/user-icon.svg';
import LockIcon from '../../../assets/lock-icon.svg';
import { useForm } from "react-hook-form";
import { useLoginUserMutation } from "../../../store/features/auth/authApi";
import { toast } from "react-hot-toast";
import Cookies from "js-cookie";
import { useState, useEffect } from "react";
import { useGetUserQuery } from "../../../store/features/auth/authApi";
import { useDispatch } from "react-redux";
import { setUserDetails } from "../../../store/features/auth/authSlice";
import SocialLogin from '../../auth/SocialLogin';
import { threadUid } from "../../../store/features/chat/chatSlice";

interface SignInModalProps {
  onClose?: () => void;
  onSignUpClick?: () => void;
}

const SignInModal: React.FC<SignInModalProps> = ({ onClose, onSignUpClick }) => {
  const [loginUser, { isLoading }] = useLoginUserMutation();
  const { register, handleSubmit, formState: { errors } } = useForm();
  const { data: user } = useGetUserQuery({}, {
    skip: !Cookies.get('accessToken')
  });
  const dispatch = useDispatch();
  const [showPassword, setShowPassword] = useState(false);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onSubmit = async (data: any) => {
    try {
      const userData = {
        username_or_email: data.email,
        password: data.password,
      };
      const response = await loginUser(userData);
      if (response.error) {
        // @ts-expect-error error
        toast.error(response.error.data?.msg || "An unexpected error occurred");
        return;
      } else {
        if (response.data && response.data.access) {
          Cookies.set('accessToken', response.data.access, { expires: 7 });

          // Instead of refetching, fetch directly
          try {
            const userResponse = await fetch(`${import.meta.env.VITE_API_URL}/auth/profile/`, {
              headers: {
                'Authorization': `Bearer ${response.data.access}`
              }
            });
            const userData = await userResponse.json();
            dispatch(setUserDetails(userData));
          } catch (error) {
            console.error("Failed to fetch user details", error);
          }

          toast.success(response?.data?.msg || "Login successful");
          if (onClose) onClose();

          // Restore threadUid if present
          const savedThreadUid = localStorage.getItem('threadUid');
          if (savedThreadUid) {
            dispatch(threadUid(savedThreadUid));
            localStorage.removeItem('threadUid');
          }
        }
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.log(error);
      toast.error("Invalid email or password");
    }
  };

  useEffect(() => {
    if (user) {
      dispatch(setUserDetails(user));
    }
  }, [user, dispatch]);

  return (
    <div className="w-full h-full fixed inset-0 z-[9999]">
      <div className="bg-[#00000080] h-full w-full absolute top-0 left-0"></div>
      <div className="px-[10px] sm:px-[20px] fixed top-1/2 left-1/2 z-[100] -translate-x-1/2 -translate-y-1/2 max-w-[745px] w-full">
        <div className="bg-white rounded-[20px] ">
          <div className="flex justify-between items-center px-[15px] py-[10px] sm:px-[25px] sm:py-[15px] border-b border-[#E7ECF9]">
            <h3 className="text-[20px] sm:text-[28px] font-semibold text-[#05073C]">Sign In</h3>
            <button
              onClick={onClose}
              className="cursor-pointer w-[30px] sm:w-[35px] h-[30px] sm:h-[35px] bg-[#E7ECF9] rounded-full flex items-center justify-center"
            >
              <img src="/modalClose-icon.svg" alt="Close" />
            </button>
          </div>
          <div className="px-[15px] py-[10px] sm:px-[25px] sm:py-[20px] pb-[60px] max-h-[calc(100vh-100px)] overflow-y-auto">
            <p className="text-[14px] sm:text-[16px] font-semibod text-[#6C6C6C] mb-[20px] sm:mb-[35px]">Join now for free or log in to unlock exclusive offers and rewards!</p>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="mb-[20px] sm:mb-[35px] relative w-full">
                <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">Username or Email</label>
                <div className="relative">
                  <img src={userIcon} alt="user" className='w-[30px] h-[18px] absolute sm:left-[20px] left-[10px] top-1/2 transform -translate-y-1/2' />
                  <input
                    type="text"
                    {...register("email", { required: "Email or username is required" })}
                    className="w-full py-[17px] px-[18px] sm:ps-[60px] ps-[40px] text-[14px] font-semibold text-[#000000] leading-[18px] border border-[#0D9BC6] focus:outline-none placeholder:text-[#00000080] rounded-[8px]"
                    placeholder="Enter username or email"
                  />
                </div>
                {/* @ts-expect-error error */}
                {errors.email && <span className="text-red-500 text-sm">{errors.email.message}</span>}
              </div>
              <div className="mb-[20px] sm:mb-[35px] relative w-full">
                <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">Password</label>
                <div className="relative">
                  <img src={LockIcon} alt="user" className='w-[30px] h-[25px] absolute sm:left-[20px] left-[10px] top-1/2 transform -translate-y-1/2' />
                  <input
                    type={showPassword ? "text" : "password"}
                    {...register("password", { required: "Password is required" })}
                    className="w-full py-[17px] px-[18px] sm:ps-[60px] ps-[45px] text-[14px] font-semibold text-[#000000] leading-[18px] border border-[#0D9BC6] focus:outline-none placeholder:text-[#00000080] rounded-[8px]"
                    placeholder="Enter your password"
                  />
                  <img
                    src={showPassword ? "/eye-open.svg" : "/eye-close.svg"}
                    alt="Toggle"
                    className="cursor-pointer absolute right-5 top-0 bottom-0 my-auto w-6 h-6"
                    onClick={() => setShowPassword(!showPassword)}
                  />
                </div>
                {/* @ts-expect-error error */}
                {errors.password && <span className="text-red-500 text-sm">{errors.password.message}</span>}
              </div>
              <div className="flex justify-end my-[13px] sm:my-[23px]">
                <a href="/forgot-password" className="text-[14px] text-[#00000080] hover:text-[#0D3FC6]">
                  Forgot your password?
                </a>
              </div>
              <div className="text-center mb-[12px]">
                <button
                  type="submit"
                  className="bg-gradient-to-r from-[#0D3FC6] to-[#3793FF] text-white py-[16px] px-[45px] rounded-[8px] font-medium hover:bg-blue-700 transition-colors cursor-pointer !rounded-button whitespace-nowrap text-[14px] leading-[18px] uppercase"
                  disabled={isLoading}
                >
                  {isLoading ? "Signing in..." : "Sign In"}
                </button>
              </div>
            </form>
            <SocialLogin />

            <div className="text-center text-[#05073C] font-normal mt-[30px] text-[14px] leading-[18px]">
              Don't have an account?  {" "}
              <a
                href="#"
                className="text-[#0D3FC6] font-semibold"
                onClick={e => {
                  e.preventDefault();
                  onSignUpClick?.();
                }}
              >
                Sign Up
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignInModal;
