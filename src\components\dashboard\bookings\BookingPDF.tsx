import React from 'react';
import { Document, Page, Text, View, StyleSheet, PDFDownloadLink, Image, Font, Link, Svg, Path } from '@react-pdf/renderer';
import NotoBengali from '../../../fonts/NotoSansBengali-Regular.ttf';
// Register fonts (optional - you can use default fonts)
Font.register({
     family: 'NotoBengali',
     src: NotoBengali,
});

interface BookingData {
     id: number;
     destination: string;
     hotel: string;
     booking_status: string;
     booking_amount: string;
     order: {
          order_id: string;
     };
     booking_reference: string;
     booking_date: string;
     duration: {
          start_date: string;
          end_date: string;
     };
     traveller_numbers: {
          total: number;
     };
     tour: {
          tour_title: string;
     };
     contact_details: {
          first_name: string;
          last_name: string;
          address: string;
     };
}

const formatDate = (dateString: string) => {
     if (dateString?.includes("T")) {
          const date = new Date(dateString);
          return date.toLocaleDateString('en-US', {
               year: 'numeric',
               month: 'short',
               day: 'numeric'
          });
     }
     return dateString;
};

// Add currency formatting function
const formatCurrency = (amount: string | number) => {
     return `৳ ${amount || '0'}`;
};

// Create compact styles for single page
const styles = StyleSheet.create({
     page: {
          flexDirection: 'column',
          backgroundColor: '#ffffff',
          padding: 20,
          fontFamily: 'NotoBengali',
          fontSize: 10, // Reduced base font size
     },
     header: {
          marginBottom: 15, // Reduced margin
     },
     logo: {
          width: 130, // Increased from 162 for better quality
          height: 'auto', // Maintain aspect ratio
          marginBottom: 10,
     },
     title: {
          fontSize: 14, // Reduced from 18
          color: '#000000',
          marginBottom: 15, // Reduced margin
     },
     greeting: {
          fontSize: 10,
          color: '#404040',
          marginBottom: 8, // Reduced margin
     },
     description: {
          fontSize: 9, // Reduced font size
          color: '#404040',
          lineHeight: 1.3, // Reduced line height
          marginBottom: 12, // Reduced margin
     },
     button: {
          backgroundColor: '#0D3FC6',
          color: '#ffffff',
          padding: '6 12',
          borderRadius: 20,
          fontSize: 8,
          fontWeight: 'bold',
          alignSelf: 'flex-start',
          marginBottom: 15,
     },
     sectionTitle: {
          fontSize: 10, // Reduced font size
          fontWeight: 'bold',
          color: '#404040',
          marginBottom: 6, // Reduced margin
          marginTop: 8, // Added small top margin
     },
     detailsBox: {
          backgroundColor: '#F8FAFC',
          padding: 10, // Reduced padding
          marginBottom: 15, // Reduced margin
          borderRadius: 3,
     },
     detailsText: {
          fontSize: 9, // Reduced font size
          color: '#000000',
          marginBottom: 3, // Reduced margin
     },
     detailsBold: {
          fontSize: 9, // Reduced font size
          fontWeight: 'bold',
          color: '#000000',
          marginBottom: 3, // Reduced margin
     },
     tableContainer: {
          marginBottom: 15, // Reduced margin
     },
     tableHeader: {
          flexDirection: 'row',
          backgroundColor: '#F8FAFC',
          padding: 6, // Reduced padding
          borderBottom: '1 solid #E5E7EB',
     },
     tableHeaderText: {
          fontSize: 8, // Reduced font size
          fontWeight: 'bold',
          color: '#000000',
     },
     tableRow: {
          flexDirection: 'row',
          padding: 6, // Reduced padding
          borderBottom: '1 solid #E5E7EB',
     },
     tableCell: {
          fontSize: 8, // Reduced font size
          color: '#000000',
          lineHeight: 1.2,
     },
     grandTotalSection: {
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginTop: 10, // Reduced margin
          marginBottom: 15, // Reduced margin
          paddingTop: 10, // Reduced padding
          borderTop: '1 solid #E5E7EB',
     },
     grandTotalLabel: {
          fontSize: 12, // Reduced font size
          fontWeight: 'bold',
          color: '#000000',
     },
     grandTotalAmount: {
          fontSize: 16, // Reduced font size
          fontWeight: 'bold',
          color: '#000000',
     },
     taxText: {
          fontSize: 8, // Reduced font size
          color: '#EF4444',
          marginTop: 1,
     },

     compactSpacing: {
          marginBottom: 8, // Compact spacing for regards section
     },
     regards: {
          fontSize: 9, // Reduced font size
          color: '#404040',
          marginBottom: 2, // Very small margin
     },
     regardsBold: {
          fontSize: 9, // Reduced font size
          fontWeight: 'bold',
          color: '#404040',
          marginBottom: 12, // Reduced margin
     },
     systemMessage: {
          fontSize: 8,
          color: '#9CA3AF',
          marginBottom: 15,
     },
     footer: {
          borderTop: '1 solid #E5E7EB',
          paddingTop: 10,
          marginTop: 10,
     },
     footerLogo: {
          width: 120, // Smaller size for footer
          height: 'auto',
          marginBottom: 6,
     },
     copyright: {
          fontSize: 7, // Reduced font size
          color: '#9CA3AF',
          marginBottom: 2, // Small margin
     },
     tagline: {
          fontSize: 7, // Reduced font size
          color: '#9CA3AF',
     },
     socialContainer: {
          flexDirection: 'row',
          gap: 10,
          marginTop: 6,
          marginBottom: 6,
          alignItems: 'center',
     },
     socialIcon: {
          width: 20,
          height: 20,
     },
     socialLink: {
          color: '#4B5563', // gray-600
          textDecoration: 'none',
     },
});

// PDF Document Component - Compact Single Page Version
const BookingPDFDocument: React.FC<{ booking: BookingData }> = ({ booking }) => (
     <Document>
          <Page size="A4" style={styles.page}>
               {/* Header */}
               <View style={styles.header}>
                    <Image style={styles.logo} src="/logo-png.png" />
                    <Text style={styles.title}>
                         Your booking #{booking?.order?.order_id} is confirmed!
                    </Text>
               </View>

               {/* Greeting */}
               <Text style={styles.greeting}>Dear Traveler,</Text>

               {/* Description */}
               <Text style={styles.description}>
                    We are glad to inform you that your booking for {booking?.tour?.tour_title || 'your selected package'} has been confirmed. Your travel experience is scheduled as per the details below.
               </Text>

               {/* Booking Details Section */}
               <Text style={styles.sectionTitle}>Delivery Address</Text>
               <View style={styles.detailsBox}>
                    <Text style={styles.detailsBold}>{booking?.contact_details?.first_name} {booking?.contact_details?.last_name}</Text>
                    <Text style={styles.detailsText}>{booking?.contact_details?.address || 'N/A'}</Text>
                    {/* <Text style={styles.detailsText}>
                         {booking?.destination || 'Destination'} - {booking?.hotel || 'Hotel'}
                    </Text> */}
               </View>

               {/* Package Details Table */}
               <Text style={styles.sectionTitle}>Package Details</Text>
               <View style={styles.tableContainer}>
                    {/* Table Header */}
                    <View style={styles.tableHeader}>
                         <Text style={[styles.tableHeaderText, { width: '45%' }]}>PACKAGE NAME</Text>
                         <Text style={[styles.tableHeaderText, { width: '30%' }]}>TRAVEL DATES</Text>
                         <Text style={[styles.tableHeaderText, { width: '10%' }]}>GUESTS</Text>
                         <Text style={[styles.tableHeaderText, { width: '15%' }]}>AMOUNT</Text>
                    </View>

                    {/* Table Row */}
                    <View style={styles.tableRow}>
                         <Text style={[styles.tableCell, { width: '45%' }]}>
                              {booking?.tour?.tour_title || 'Travel Package'}
                         </Text>
                         <Text style={[styles.tableCell, { width: '30%' }]}>
                              {formatDate(booking?.duration?.start_date)} - {formatDate(booking?.duration?.end_date)}
                         </Text>
                         <Text style={[styles.tableCell, { width: '10%' }]}>
                              {booking?.traveller_numbers?.total?.toString() || '1'}
                         </Text>
                         <Text style={[styles.tableCell, { width: '15%' }]}>
                              {formatCurrency(booking?.booking_amount)}
                         </Text>
                    </View>
               </View>

               {/* Compact Grand Total */}
               <View style={styles.grandTotalSection}>
                    <Text style={styles.grandTotalLabel}>GRAND TOTAL</Text>
                    <View>
                         <Text style={styles.grandTotalAmount}>
                              {formatCurrency(booking?.booking_amount)}
                         </Text>
                    </View>
               </View>


               {/* Compact Regards Section */}
               <View style={styles.compactSpacing}>
                    <Text style={styles.regards}>Best Regards,</Text>
                    <Text style={styles.regardsBold}>Team TripBooking</Text>
               </View>

               {/* System Message */}
               <Text style={styles.systemMessage}>
                    This is a system generated message. Do not reply.
               </Text>

               {/* Updated Footer with corrected SVG syntax */}
               <View style={styles.footer}>
                    <Text style={styles.footerLogo}>tripbooking.ai</Text>

                    <View style={styles.socialContainer}>
                         {/* Instagram */}
                         <Link src="https://www.instagram.com/tripbookingai/" style={styles.socialLink}>
                              <Svg style={styles.socialIcon} viewBox="0 0 24 24">
                                   <Path
                                        d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                                        fill="#999999"
                                   />
                              </Svg>
                         </Link>

                         {/* Facebook */}
                         <Link src="https://www.facebook.com/tripbookingai" style={styles.socialLink}>
                              <Svg style={styles.socialIcon} viewBox="0 0 24 24">
                                   <Path
                                        d="M20 3H4C3.447 3 3 3.448 3 4v16c0 0.552 0.447 1 1 1h8.615v-6.96h-2.338v-2.725h2.338v-2c0-2.325 1.42-3.592 3.5-3.592 0.699-0.002 1.399 0.034 2.095 0.107v2.42h-1.435c-1.128 0-1.348 0.538-1.348 1.325v1.735h2.697l-0.35 2.725h-2.348V21H20c0.553 0 1-0.448 1-1V4c0-0.552-0.447-1-1-1z"
                                        fill="#999999"
                                   />
                              </Svg>
                         </Link>

                         {/* LinkedIn */}
                         <Link src="https://www.linkedin.com/company/tripbookingai/" style={styles.socialLink}>
                              <Svg style={styles.socialIcon} viewBox="0 0 24 24">
                                   <Path
                                        d="M19 3a2 2 0 012 2v14a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h14m-.5 15.5v-5.3a3.26 3.26 0 00-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 011.4 1.4v4.93h2.79M6.88 8.56a1.68 1.68 0 001.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 00-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37h2.77z"
                                        fill="#999999"
                                   />
                              </Svg>
                         </Link>
                    </View>

                    <Text style={styles.copyright}>Copyright © 2025 TripBooking</Text>
                    <Text style={styles.tagline}>
                         Plan. Compare. Book with TripBooking AI.
                    </Text>
               </View>
          </Page>
     </Document>
);

// Download Button Component
export const BookingPDFDownload: React.FC<{ booking: BookingData }> = ({ booking }) => {
     // Create a memoized document to prevent regeneration on every render
     const document = React.useMemo(() => (
          <BookingPDFDocument booking={booking} />
     ), [booking]);
     
     return (
          <PDFDownloadLink
               document={document}
               fileName={`booking-confirmation-${booking?.booking_reference || booking?.id}.pdf`}
               className="px-4 py-2 bg-white border border-[#E5E7EB] text-[#636A7E] rounded-sm text-sm flex items-center gap-[6px] cursor-pointer hover:bg-gray-50"
          >
               {({ loading }) => (
                    <>
                         <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                         </svg>
                         {loading ? 'Generating...' : 'Print'}
                    </>
               )}
          </PDFDownloadLink>
     );
};

export default BookingPDFDocument;