import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import Cookies from 'js-cookie';

export const adminApi = createApi({
     reducerPath: "adminApi",
     baseQuery: fetchBaseQuery({
          baseUrl: `${import.meta.env.VITE_API_URL}`,
          credentials: "include",
          prepareHeaders: (headers, { endpoint }) => {
               const token = Cookies.get('accessToken');

               if (token && endpoint !== 'loginUser' && endpoint !== 'registerUser') {
                    try {
                         const parts = token.split('.');
                         if (parts.length === 3) {
                              JSON.parse(atob(parts[1]));
                              headers.set('Authorization', `Bearer ${token}`);
                         }
                         else {
                              Cookies.remove('accessToken');
                              window.location.reload();
                         }

                         // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    } catch (e: any) {
                         console.log(e, "::error");
                         Cookies.remove('accessToken');
                    }
               }

               return headers;
          },
     }),
     tagTypes: ['Admin', 'Bookings'],

     endpoints: (builder) => ({
          getUsersList: builder.query({
               query: () => ({
                    url: "/auth/users",
                    method: "GET",
               }),
               providesTags: ['Admin']
          }),

          getThreadsList: builder.query({
               query: (userId: string) => ({
                    url: `/chat/threads/get/${userId}/`,
                    method: "GET",
               }),
               providesTags: ['Admin']
          }),

          getMessagesList: builder.query({
               query: (uid: string) => ({
                    url: `/chat/threads/${uid}/`,
                    method: "GET",
               }),
               providesTags: ['Admin']
          }),

          analyticsDashboard: builder.query({
               query: () => ({
                    url: "/chat/analytics/dashboard/",
                    method: "GET",
               }),
               providesTags: ['Admin']
          }),

          packagesDashboard: builder.query({
               query: () => ({
                    url: "/database/package-analytics/",
                    method: "GET"
               }),
               providesTags: ['Admin']
          }),

          lineChartGroupBy: builder.query({
               query: (groupBy: string) => ({
                    url: `/chat/analytics/dashboard/line-chart/`,
                    method: "GET",
                    params: {
                         group_by: groupBy
                    }
               }),
               providesTags: ['Admin']
          }),

          updateUserRole: builder.mutation({
               query: (data) => ({
                    url: "auth/users/update-role/",
                    method: "PUT",
                    body: data,
               }),
               invalidatesTags: ['Admin']
          }),

          updateBookingStatus: builder.mutation({
               query: (data) => ({
                    url: "/booking/order/update/status/",
                    method: "POST",
                    body: data,
               }),
               invalidatesTags: ['Admin', 'Bookings']
          }),

          getAdminBookings: builder.query({
               query: () => (
                    {
                         url: "/booking/admin/get/",
                         method: "GET"
                    }
               ),
               providesTags: ['Bookings']
          }),

          getContactDetails: builder.query({
               query: () => ({
                    url: "/chat/contact-messages/",
                    method: "GET",
               }),
               providesTags: ['Admin']
          }),

          getErrorLogs: builder.query({
               query: () => ({
                    url: "/chat/error-logs/",
                    method: "GET"
               }),
               providesTags: ['Admin']
          }),
     }),
});

export const { useGetUsersListQuery, useGetThreadsListQuery, useGetMessagesListQuery, useAnalyticsDashboardQuery, usePackagesDashboardQuery, useLineChartGroupByQuery, useUpdateUserRoleMutation, useUpdateBookingStatusMutation, useGetContactDetailsQuery, useGetAdminBookingsQuery, useGetErrorLogsQuery } = adminApi;
