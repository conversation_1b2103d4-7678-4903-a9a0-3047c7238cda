const PaymentSkeleton = () => {
     return (
        <div className="md:mt-0 mt-[60px] max-w-[1240px] sm:px-[30px] px-[10px] mx-auto py-[30px]">
            {/* Stats Cards Skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 items-center max-w-[880px] gap-[14px] mb-[30px]">
                {/* Total Spent Card */}
                <div className="bg-gray-100 rounded-[10px] p-[14px] animate-pulse">
                    <div className="h-4 bg-gray-300 rounded w-20 mb-2"></div>
                    <div className="h-6 bg-gray-300 rounded w-24 mb-1"></div>
                    <div className="h-3 bg-gray-300 rounded w-16"></div>
                </div>

                {/* Pending Payments Card */}
                <div className="bg-gray-100 rounded-[10px] p-[14px] animate-pulse">
                    <div className="h-4 bg-gray-300 rounded w-28 mb-2"></div>
                    <div className="h-6 bg-gray-300 rounded w-20 mb-1"></div>
                    <div className="h-3 bg-gray-300 rounded w-18"></div>
                </div>

                {/* Third Card (if needed) */}
                <div className="bg-gray-100 rounded-[10px] p-[14px] animate-pulse lg:block hidden">
                    <div className="h-4 bg-gray-300 rounded w-32 mb-2"></div>
                    <div className="h-6 bg-gray-300 rounded w-24 mb-1"></div>
                    <div className="h-3 bg-gray-300 rounded w-20"></div>
                </div>
            </div>

            {/* Filters Section Skeleton */}
            <div className="flex justify-end items-center gap-[9px] py-[16px]">
                <div className="max-w-[145px] w-full">
                    <div className="h-[38px] bg-gray-200 rounded-[8px] animate-pulse"></div>
                </div>
                <div className="max-w-[250px] w-full">
                    <div className="h-[38px] bg-gray-200 rounded-[8px] animate-pulse"></div>
                </div>
            </div>

            {/* Table Skeleton */}
            <div className="border border-[#EBEBEE] rounded-[10px] overflow-hidden">
                <div className="overflow-auto w-[calc(100vw-24px)] md:w-[calc(100vw-310px)] lg:w-[calc(100vw-338px)]">
                    <table className="w-full whitespace-nowrap">
                        {/* Table Header */}
                        <thead>
                            <tr className="bg-[#F9FAFB] border-b border-[#E5E7EB]">
                                <th className="px-[20px] py-[12px] text-start">
                                    <div className="h-3 bg-gray-300 rounded w-16 animate-pulse"></div>
                                </th>
                                <th className="px-[20px] py-[12px] text-start">
                                    <div className="h-3 bg-gray-300 rounded w-20 animate-pulse"></div>
                                </th>
                                <th className="px-[20px] py-[12px] text-start">
                                    <div className="h-3 bg-gray-300 rounded w-14 animate-pulse"></div>
                                </th>
                                <th className="px-[20px] py-[12px] text-start">
                                    <div className="h-3 bg-gray-300 rounded w-12 animate-pulse"></div>
                                </th>
                            </tr>
                        </thead>

                        {/* Table Body */}
                        <tbody>
                            {[...Array(8)].map((_, index) => (
                                <tr key={index} className="border-b border-[#E5E7EB] animate-pulse">
                                    {/* Date Column */}
                                    <td className="px-[20px] py-[14px]">
                                        <div className="h-4 bg-gray-200 rounded w-20"></div>
                                    </td>
                                    
                                    {/* Tour Details Column */}
                                    <td className="px-[20px] py-[14px]">
                                        <div className="space-y-1">
                                            <div className="h-4 bg-gray-200 rounded w-32"></div>
                                            <div className="h-3 bg-gray-200 rounded w-16"></div>
                                        </div>
                                    </td>
                                    
                                    {/* Amount Column */}
                                    <td className="px-[20px] py-[14px]">
                                        <div className="h-4 bg-gray-200 rounded w-16"></div>
                                    </td>
                                    
                                    {/* Status Column */}
                                    <td className="px-[20px] py-[14px]">
                                        <div className="h-6 bg-gray-200 rounded-[70px] w-20"></div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {/* Pagination Skeleton */}
                <div className="flex justify-between items-center py-[16px] lg:px-[20px] px-[10px] sm:flex-row flex-col gap-[10px]">
                    {/* Left side - Page selector */}
                    <div className="flex items-center gap-[9px] w-full">
                        <div className="h-4 bg-gray-200 rounded w-8 animate-pulse"></div>
                        <div className="max-w-[70px] w-full">
                            <div className="h-[38px] bg-gray-200 rounded-[8px] animate-pulse"></div>
                        </div>
                        <div className="h-4 bg-gray-200 rounded w-12 animate-pulse"></div>
                    </div>
                    
                    {/* Right side - Pagination buttons */}
                    <div className="flex justify-end items-center gap-[5px] w-full">
                        <div className="h-4 bg-gray-200 rounded w-8 animate-pulse"></div>
                        <div className="h-[32px] w-[32px] bg-gray-200 rounded-[8px] animate-pulse"></div>
                        <div className="h-[32px] w-[32px] bg-gray-200 rounded-[8px] animate-pulse"></div>
                        <div className="h-[32px] w-[32px] bg-gray-200 rounded-[8px] animate-pulse"></div>
                        <div className="h-4 bg-gray-200 rounded w-4 animate-pulse"></div>
                        <div className="h-[32px] w-[32px] bg-gray-200 rounded-[8px] animate-pulse"></div>
                        <div className="h-4 bg-gray-200 rounded w-8 animate-pulse"></div>
                    </div>
                </div>
            </div>
          </div>
    );
};

export default PaymentSkeleton;