import userIcon from '../../assets/user-icon.svg';
import airplane from '../../assets/plane-icon.svg';
import LockIcon from '../../assets/lock-icon.svg';
import video from '../../assets/videos/signup-video.mp4';
import emailIcon from '../../assets/email-icon.svg';
import { useForm } from 'react-hook-form';
import AuthButton from '../../components/auth/AuthButton';
import { useRegisterUserMutation } from '../../store/features/auth/authApi';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { useState, useRef, KeyboardEvent } from 'react';
import { useDispatch } from 'react-redux';
import { setRegistrationData, clearRegistrationData } from '../../store/features/auth/authSlice';
import { usePhoneNumberOtpMutation, useVerifyPhoneNumberOtpMutation } from '../../store/features/auth/authApi';
import LeftSide from '../../components/auth/LeftSide';
import SocialLogin from '../../components/auth/SocialLogin';

interface RegisterFormInputs {
  username: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
}

interface OtpFormInputs {
  otp: string;
}

interface OtpInputs {
  [key: string]: string;
}

const Register = () => {
  const dispatch = useDispatch();
  const [registerUser] = useRegisterUserMutation();
  const [sendOtp, { isLoading: isSendingOtp }] = usePhoneNumberOtpMutation();
  const [verifyOtp, { isLoading: isVerifyingOtp }] = useVerifyPhoneNumberOtpMutation();
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<RegisterFormInputs>();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showOtpForm, setShowOtpForm] = useState(false);
  const [verificationId, setVerificationId] = useState<string>('');
  const [otpValues, setOtpValues] = useState<OtpInputs>({});
  const otpInputs = useRef<(HTMLInputElement | null)[]>([]);

  const password = watch("password");
  const navigate = useNavigate();

  const {
    handleSubmit: handleOtpSubmit,
    formState: { errors: otpErrors },
  } = useForm<OtpFormInputs>();

  const handleOtpChange = (index: number, value: string) => {
    if (!/^\d*$/.test(value)) return;

    const newOtpValues = { ...otpValues, [index]: value };
    setOtpValues(newOtpValues);

    if (value && index < 5) {
      otpInputs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !otpValues[index] && index > 0) {
      otpInputs.current[index - 1]?.focus();
    }
  };

  const onSubmit = async (data: RegisterFormInputs) => {
    try {
      const userData = {
        username: data.username,
        email: data.email,
        phone: data.phone,
        password: data.password,
      };

      // Store registration data in Redux
      dispatch(setRegistrationData(userData));

      // Send OTP
      const otpResponse = await sendOtp({ phone: data.phone });

      if ('error' in otpResponse) {
        // @ts-expect-error error handling
        toast.error(otpResponse.error.data?.msg || "Failed to send OTP");
        return;
      }

      // Store verification_id from response
      const verification_id = otpResponse.data?.verification_id;
      if (!verification_id) {
        toast.error("Failed to get verification ID");
        return;
      }

      setVerificationId(verification_id);
      toast.success("OTP sent successfully!");
      setShowOtpForm(true);

    } catch (error) {
      console.log("Error: ", error);
      toast.error("Failed to send OTP");
    }
  };

  // @ts-expect-error error handling
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const onOtpSubmit = async (data: OtpFormInputs) => {
    try {
      const otpString = Object.values(otpValues).join('');

      const verifyResponse = await verifyOtp({
        phone: watch('phone'),
        otp: otpString,
        verification_id: verificationId
      });

      if ('error' in verifyResponse) {
        // @ts-expect-error error handling
        toast.error(verifyResponse.error.data?.msg || "Invalid OTP");
        return;
      }
      const verificationToken = verifyResponse?.data?.verification_token;

      const userData = {
        username: watch('username'),
        email: watch('email'),
        phone: watch('phone'),
        password: watch('password'),
        verification_token: verificationToken
      };

      const response = await registerUser(userData);

      if ('error' in response) {
        // @ts-expect-error error handling
        toast.error(response.error.data?.msg || "Registration failed");
        return;
      }

      toast.success(response?.data?.msg || "Account created successfully");
      dispatch(clearRegistrationData());
      navigate("/login");

    } catch (error) {
      console.log("Error: ", error);
      toast.error("Registration failed");
    }
  };

  return (
    <div className="flex h-auto md:h-screen w-full">
      <LeftSide video={video} title="Your next adventure start here - Find , Compare & Book in seconds!" description="Log in and uncover the best travel deals in seconds." />

      {/* Right side with login form */}
      {showOtpForm ?
        <div className="h-screen overflow-y-auto w-full md:w-1/2 bg-white flex flex-col justify-center items-center px-[20px] pt-[160px] py-[30px] relative">
          <div className="w-full max-w-[460px] px-[20px]">
            <div className='text-center mb-[20px]'>
              <h1 className="text-[30px] font-bold leading-[30px] text-[#0D3FC6] mb-[12px]">Verification</h1>
              <p className="text-[#********] text-[16px]">Enter the 6 digits OTP sent your Phone Number</p>
            </div>

            <form onSubmit={handleOtpSubmit(onOtpSubmit)}>
              <div className="mb-[24px]">
                <div className="flex justify-between gap-2">
                  {[...Array(6)].map((_, index) => (
                    <input
                      key={index}
                      type="text"
                      maxLength={1}
                      ref={el => { otpInputs.current[index] = el }}
                      className="w-12 h-12 text-center text-xl font-semibold border border-gray-300 rounded-[8px] focus:outline-none focus:border-[#0D3FC6]"
                      value={otpValues[index] || ''}
                      onChange={(e) => handleOtpChange(index, e.target.value)}
                      onKeyDown={(e) => handleKeyDown(index, e)}
                    />
                  ))}
                </div>
                {otpErrors.otp && (
                  <span className="text-red-500 text-sm mt-1 block text-center">{otpErrors.otp.message}</span>
                )}
              </div>

              <AuthButton isLoading={isVerifyingOtp}>VERIFY OTP</AuthButton>
            </form>
          </div>
        </div> : <div className="h-screen overflow-y-auto w-full md:w-1/2 bg-white flex flex-col justify-center items-center px-[20px] pt-[90px] relative">
          <div className="w-full max-w-[460px] py-[10px]">
            <div className='absolute right-0 sm:w-[224px] w-[150px] mt-[-50px] ms-auto'>
              <img src={airplane} alt="Airplane" className='w-full h-full' />
            </div>
            <div className='text-center mb-[15px]'>
              <h1 className="lg:text-[55px] sm:text-[40px] text-[30px] font-bold sm:leading-[48px] md:leading-[40px] leading-[30px] text-[#0D3FC6] mb-[12px]">Sign up</h1>
              <p className="text-[#********] text-[16px]">Create your account</p>
            </div>

            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="mb-[24px] relative">
                <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">
                  User Name
                </label>
                <div className="relative">
                  <img src={userIcon} alt="user" className='w-[30px] h-[18px] absolute sm:left-[30px] left-[15px] top-1/2 transform -translate-y-1/2' />
                  <input
                    {...register("username", {
                      required: "Username is required",
                      minLength: {
                        value: 3,
                        message: "Username must be at least 3 characters"
                      },
                      pattern: {
                        value: /^[^\s]+$/,
                        message: "Spaces are not allowed in username"
                      },
                      validate: (value) => {
                        if (value.trim() === '') {
                          return "Username cannot be empty or contain only spaces";
                        }
                        return true;
                      }
                    })}
                    type="text"
                    className="w-full py-[17px] px-[18px] sm:ps-[70px] ps-[50px] text-[14px] font-semibold text-[#000000] leading-[18px] border border-[#0D9BC6] focus:outline-none placeholder:text-[#********] rounded-[8px]"
                    placeholder="Enter your username"
                  />
                </div>
                {errors.username && (
                  <span className="text-red-500 text-sm mt-1">{errors.username.message}</span>
                )}
              </div>

              <div className="mb-[24px] relative">
                <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">
                  Email
                </label>
                <div className="relative">
                  <img src={emailIcon} alt="email" className='w-[30px] h-[20px] absolute sm:left-[30px] left-[15px] top-1/2 transform -translate-y-1/2' />
                  <input
                    {...register("email", {
                      required: "Email is required",
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: "Invalid email address"
                      }
                    })}
                    type="email"
                    className="w-full py-[17px] px-[18px] sm:ps-[70px] ps-[50px] text-[14px] font-semibold text-[#000000] leading-[18px] border border-[#0D9BC6] focus:outline-none placeholder:text-[#********] rounded-[8px]"
                    placeholder="Enter your email"
                  />
                </div>
                {errors.email && (
                  <span className="text-red-500 text-sm mt-1">{errors.email.message}</span>
                )}
              </div>

              <div className="relative mb-[24px]">
                <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">
                  Phone
                </label>
                <div className="relative">
                  <img src="./phone.svg" alt="phone" className='w-[30px] h-[25px] absolute sm:left-[30px] left-[15px] top-1/2 transform -translate-y-1/2' />
                  <input
                    {...register("phone", {
                      required: "Phone number is required",
                      pattern: {
                        value: /^(\+?88)?01[3-9]\d{8}$/,
                        message: "Enter a valid Bangladeshi phone number (e.g. +8801XXXXXXXXX or 01XXXXXXXXX)"
                      }
                    })}
                    type="tel"
                    className="w-full py-[17px] px-[18px] sm:ps-[70px] ps-[50px] text-[14px] font-semibold text-[#000000] leading-[18px] border border-[#0D9BC6] focus:outline-none placeholder:text-[#********] rounded-[8px]"
                    placeholder="Enter your phone number"
                  />
                </div>
                {errors.phone && (
                  <span className="text-red-500 text-sm mt-1">{errors.phone.message}</span>
                )}
              </div>

              <div className="relative mb-[24px]">
                <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">
                  Password
                </label>
                <div className="relative">
                  <img src={LockIcon} alt="lock" className='w-[30px] h-[25px] absolute sm:left-[30px] left-[15px] top-1/2 transform -translate-y-1/2' />

                  <div className='relative'>
                    <input
                      {...register("password", {
                        required: "Password is required",
                        minLength: {
                          value: 6,
                          message: "Password must be at least 6 characters"
                        },
                        pattern: {
                          value: /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]).+$/,
                          message: "Password must contain at least one uppercase letter, one number, and one special character"
                        },
                        validate: (value) => {
                          if (/\s/.test(value)) {
                            return "Password cannot contain spaces";
                          }
                          return true;
                        }
                      })}
                      type={showPassword ? "text" : "password"}
                      className="w-full py-[17px] px-[18px] sm:ps-[70px] ps-[50px] text-[14px] font-semibold text-[#000000] leading-[18px] border border-[#0D9BC6] focus:outline-none placeholder:text-[#********] rounded-[8px]"
                      placeholder="Enter your password"
                    />
                    <img src={showPassword ? "/eye-open.svg" : "/eye-close.svg"} alt="Eye Close" className=' cursor-pointer absolute right-5 top-0 bottom-0 my-auto w-6 h-6' onClick={() => setShowPassword(!showPassword)} />
                  </div>
                </div>
                {errors.password && (
                  <span className="text-red-500 text-sm mt-1">{errors.password.message}</span>
                )}
              </div>

              <div className="relative mb-[24px]">
                <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">
                  Confirm Password
                </label>
                <div className="relative">
                  <img src={LockIcon} alt="lock" className='w-[30px] h-[25px] absolute sm:left-[30px] left-[15px] top-1/2 transform -translate-y-1/2' />
                  <div className='relative'>
                    <input
                      {...register("confirmPassword", {
                        required: "Please confirm your password",
                        validate: value => {
                          if (/\s/.test(value)) {
                            return "Password cannot contain spaces";
                          }
                          if (value !== password) {
                            return "Passwords do not match";
                          }
                          return true;
                        }
                      })}
                      type={showConfirmPassword ? "text" : "password"}
                      className="w-full py-[17px] px-[18px] sm:ps-[70px] ps-[50px] text-[14px] font-semibold text-[#000000] leading-[18px] border border-[#0D9BC6] focus:outline-none placeholder:text-[#********] rounded-[8px]"
                      placeholder="Confirm your password"
                    />
                    <img src={showConfirmPassword ? "/eye-open.svg" : "/eye-close.svg"} alt="Eye Close" className=' cursor-pointer absolute right-5 top-0 bottom-0 my-auto w-6 h-6' onClick={() => setShowConfirmPassword(!showConfirmPassword)} />
                  </div>
                </div>
                {errors.confirmPassword && (
                  <span className="text-red-500 text-sm mt-1">{errors.confirmPassword.message}</span>
                )}
              </div>

              <AuthButton isLoading={isSendingOtp}>SIGN UP</AuthButton>
            </form>

            <SocialLogin />

            <div className="text-center text-[#05073C] font-normal mt-[30px] text-[14px] leading-[18px]">
              Already have an account? {" "}
              <Link to="/login" className="text-[#0D3FC6] font-semibold">
                Sign In
              </Link>
            </div>
          </div>
        </div>}
    </div>
  );
};

export default Register;
