import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import Cookies from 'js-cookie';

export const bookingsApi = createApi({
     reducerPath: "bookingsApi",
     baseQuery: fetchBaseQuery({
          baseUrl: `${import.meta.env.VITE_API_URL}/booking`,
          credentials: "include",
          prepareHeaders: (headers, { endpoint }) => {
               const token = Cookies.get('accessToken');

               if (token && endpoint !== 'loginUser' && endpoint !== 'registerUser') {
                    try {
                         const parts = token.split('.');
                         if (parts.length === 3) {
                              JSON.parse(atob(parts[1]));
                              headers.set('Authorization', `Bearer ${token}`);
                         } else {
                              Cookies.remove('accessToken');
                              window.location.reload();
                         }
                         // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    } catch (e: any) {
                         console.log(e, "::error");
                         Cookies.remove('accessToken');
                    }
               }

               return headers;
          },
     }),
     tagTypes: ['Bookings'],

     endpoints: (builder) => ({
          getBookings: builder.query({
               query: () => ({
                    url: "/get",
                    method: "GET"
               }),
               providesTags: ['Bookings']
          }),

          getBookinGateways: builder.query({
               query: (orderId: string) => ({
                    url: `/get/payment-gateways`,
                    params: { order_id: orderId },
                    method: "GET"
               })
          }),

          selectBookingGateway: builder.mutation({
               // eslint-disable-next-line @typescript-eslint/no-explicit-any
               query: (data: any) => ({
                    url: `/change/payment-gateway/`,
                    method: "POST",
                    body: data
               })
          }),

          directBankPayment: builder.mutation({
               // eslint-disable-next-line @typescript-eslint/no-explicit-any
               query: (data: any) => ({
                    url: `/order/update/status/`,
                    method: "POST",
                    body: data
               }),
               invalidatesTags: ['Bookings']
          }),

          initiatePayment: builder.mutation({
               // eslint-disable-next-line @typescript-eslint/no-explicit-any
               query: (data: any) => ({
                    url: `/initiate-payment/`,
                    method: "POST",
                    body: data
               })
          }),

          getPayments: builder.query({
               query: () => ({
                    url: `/payments/`,
                    method: "GET"
               }),
               providesTags: ['Bookings']
          }),

          getAdminPayments: builder.query({
               query: () => (
                    {
                         url: "/payments/admin/get/",
                         method: "GET"
                    }
               )
          })
     }),
});

export const { useGetBookingsQuery, useGetBookinGatewaysQuery, useSelectBookingGatewayMutation, useDirectBankPaymentMutation, useInitiatePaymentMutation, useGetPaymentsQuery, useGetAdminPaymentsQuery } = bookingsApi;
