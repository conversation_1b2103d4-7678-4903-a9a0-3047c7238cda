import { usePaginationMutation } from "../../../store/features/tours/toursApi";
import { useNavigate } from "react-router-dom";
import Loader from "../../../components/common/Loader";
interface MetaDetails {
     pagination: {
          has_next: boolean;
     };
}

const ViewMoreButton = ({ meta_details, threadUid }: { meta_details: MetaDetails, threadUid: string | null }) => {
     const navigate = useNavigate();
     const [pagination, { isLoading }] = usePaginationMutation();

     const handleViewMore = async () => {
          try {
               const response = await pagination(meta_details);
               const paginateDetails = response?.data
               localStorage.setItem("paginateDetails", JSON.stringify(paginateDetails))

               navigate(`/tour-details/${threadUid}`)
          } catch (error) {
               console.error("Error setting pagination cookie:", error);
          }
     }

     return (
          <div className="flex justify-end mt-4 gap-3">
               {isLoading && <Loader />}
               <button
                    onClick={handleViewMore}
                    className={`flex items-center gap-2 text-[#0D3FC6] font-semibold hover:text-[#0a32a0] transition-colors cursor-pointer ${isLoading ? "opacity-50 cursor-not-allowed" : ""}`}
               >
                    <span>See More Packages</span>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                         <path d="M3.33337 8H12.6667M12.6667 8L8.00004 3.33333M12.6667 8L8.00004 12.6667" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
               </button>
          </div>
     );
};

export default ViewMoreButton;