import { Fragment, useState } from 'react';
import { Menu, Transition } from '@headlessui/react';
import { ChevronDownIcon } from '@heroicons/react/24/solid';
import BookingDetailsModal from './BookingDetailsModal';

interface TravellerDetails {
     id: number,
     first_name: string,
     last_name: string,
     date_of_birth: string,
     nationality: string,
     passport_number: string,
     gender: string,
     special_requirements: string
}

export interface BookingDetailsModalProps {
     id: string;
     booking_status: string;
     tour: {
          tour_title: string,
          description: string,
          tour_location: string,
          location: {
               address: string
          }
     },
     duration: {
          start_date: string,
          end_date: string
     },
     booking_amount: number,
     description: string,
     traveller_numbers: {
          adult: number,
          child: number,
          infant: number
     },
     contact_details: {
          first_name: string,
          last_name: string,
          email: string,
          phone: string,
          address: string
     },
     order: {
          order_id: number,
          order_date: string,
          order_currency: string,
          order_amount: number,
          payment_status: string,
          unique_payment_id: string,
          status_reason: string,
          payment_url: string
     },
     booking_date: string,
     traveller_details: TravellerDetails[]
     nationality: string,
     passport_number: string,
     gender: string,

}

const BookingsTable = ({
     bookings,
     onStatusChange
}: {
     bookings: BookingDetailsModalProps[],
     onStatusChange: (bookingId: string, newStatus: string) => Promise<void>
}) => {
     const [updatingStatus, setUpdatingStatus] = useState<string | null>(null);
     const [selectedBooking, setSelectedBooking] = useState<BookingDetailsModalProps | null>(null);

     const handleStatusUpdate = async (orderId: string, status: string) => {
          setUpdatingStatus(orderId);
          try {
               await onStatusChange(orderId, status);
          } finally {
               setUpdatingStatus(null);
          }
     };

     const getStatusStyles = (status: string) => {
          switch (status?.toLowerCase()) {
               case 'completed':
                    return 'bg-green-50 text-green-700 ring-green-600/20';
               case 'payment_pending':
                    return 'bg-yellow-50 text-yellow-700 ring-yellow-600/20';
               case 'cancelled':
                    return 'bg-red-50 text-red-700 ring-red-600/20';
               case 'initiated':
                    return 'bg-blue-50 text-blue-700 ring-blue-600/20';
               case 'on-hold':
                    return 'bg-purple-50 text-purple-700 ring-purple-600/20';
               default:
                    return 'bg-gray-50 text-gray-700 ring-gray-600/20';
          }
     };

     const getAvailableStatuses = (currentStatus: string): string[] => {
          switch (currentStatus.toLowerCase()) {
               case 'processing':
                    return ['payment_pending', 'on-hold', 'processing', 'completed'];
               case 'payment_pending':
                    return ['payment_pending', 'on-hold', 'processing', 'completed'];
               case 'on-hold':
                    return ['payment_pending', 'on-hold', 'processing', 'completed'];
               case 'completed':
                    return ['payment_pending', 'on-hold', 'processing', 'completed'];
               default:
                    return [];
          }
     };

     return (
          <>
               <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                         <tr className="bg-gray-50">
                              <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                   Booking Title
                              </th>
                              <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                   Booking Number
                              </th>
                              <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                   Booking Date
                              </th>
                              <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider hidden sm:table-cell">
                                   Total Price
                              </th>
                              <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider hidden sm:table-cell">
                                   Duration
                              </th>
                              <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider hidden sm:table-cell">
                                   Status
                              </th>
                              <th scope="col" className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider hidden sm:table-cell">
                                   Actions
                              </th>
                         </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                         {bookings?.map((pkg: BookingDetailsModalProps) => (
                              <tr key={pkg.id} className="hover:bg-gray-50/50 transition-colors">
                                   <td className="px-6 py-4">
                                        <div className="flex flex-col">
                                             <span className="text-sm font-medium text-gray-900">{pkg?.tour?.tour_title}</span>
                                             <span className="text-xs text-gray-500 mt-1">{pkg?.tour?.tour_location}</span>
                                        </div>
                                   </td>
                                   <td className="px-6 py-4">
                                        <span className="text-sm text-gray-900 font-medium">
                                             {pkg?.order?.order_id ? <># {pkg?.order?.order_id} </> : <>N/A</>}
                                        </span>
                                   </td>
                                   <td className="px-6 py-4">
                                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 ring-1 ring-inset ring-blue-600/20 w-fit">
                                             {pkg?.booking_date.split('T')[0]}
                                        </span>
                                   </td>
                                   <td className="px-6 py-4 hidden sm:table-cell">
                                        <div className="text-sm font-medium text-gray-900">
                                             ৳ {Number(pkg?.booking_amount).toLocaleString()}
                                        </div>
                                   </td>
                                   <td className="px-6 py-4 hidden sm:table-cell">
                                        <div className="text-sm text-gray-500">
                                             {new Date(pkg?.duration?.start_date).toLocaleDateString('en-US', {
                                                  month: 'short',
                                                  day: 'numeric'
                                             })}
                                             {' - '}
                                             {new Date(pkg?.duration?.end_date).toLocaleDateString('en-US', {
                                                  month: 'short',
                                                  day: 'numeric',
                                                  year: 'numeric'
                                             })}
                                        </div>
                                   </td>
                                   <td className="px-6 py-4 hidden sm:table-cell">
                                        {getAvailableStatuses(pkg.booking_status).length > 0 ? (
                                             <Menu as="div" className="relative inline-block text-left">
                                                  <Menu.Button className={`inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset ${getStatusStyles(pkg?.booking_status)} cursor-pointer`}>
                                                       {pkg?.booking_status}
                                                       <ChevronDownIcon className="ml-1 h-4 w-4" aria-hidden="true" />
                                                  </Menu.Button>

                                                  <Transition
                                                       as={Fragment}
                                                       enter="transition ease-out duration-100"
                                                       enterFrom="transform opacity-0 scale-95"
                                                       enterTo="transform opacity-100 scale-100"
                                                       leave="transition ease-in duration-75"
                                                       leaveFrom="transform opacity-100 scale-100"
                                                       leaveTo="transform opacity-0 scale-95"
                                                  >
                                                       <Menu.Items className="absolute left-0 z-10 mt-2 w-40 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                                            <div className="py-1">
                                                                 {getAvailableStatuses(pkg.booking_status).map((status) => (
                                                                      <Menu.Item key={status}>
                                                                           {({ active }) => (
                                                                                <button
                                                                                     onClick={() => handleStatusUpdate(pkg.order?.order_id.toString(), status)}
                                                                                     disabled={updatingStatus === pkg.order?.order_id.toString()}
                                                                                     className={`${active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                                                                                          } block w-full px-4 py-2 text-left text-sm capitalize ${updatingStatus === pkg.order?.order_id?.toString() ? 'opacity-50 cursor-not-allowed' : ''
                                                                                          }`}
                                                                                >
                                                                                     {updatingStatus === pkg.order?.order_id.toString() ? (
                                                                                          <span className="flex items-center">
                                                                                               <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                                                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                                                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                                                               </svg>
                                                                                               Updating...
                                                                                          </span>
                                                                                     ) : (
                                                                                          status.replace('_', ' ')
                                                                                     )}
                                                                                </button>
                                                                           )}
                                                                      </Menu.Item>
                                                                 ))}
                                                            </div>
                                                       </Menu.Items>
                                                  </Transition>
                                             </Menu>
                                        ) : (
                                             <span className={`inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset ${getStatusStyles(pkg?.booking_status)}`}>
                                                  {pkg?.booking_status}
                                             </span>
                                        )}
                                   </td>
                                   <td className="px-6 py-4 hidden text-sm sm:table-cell">
                                        <button
                                             className="text-blue-500 hover:text-blue-700"
                                             onClick={() => setSelectedBooking(pkg)}
                                        >
                                             View Details
                                        </button>
                                   </td>
                              </tr>
                         ))}
                    </tbody>
               </table>

               {selectedBooking && (
                    <BookingDetailsModal
                         isOpen={!!selectedBooking}
                         onClose={() => setSelectedBooking(null)}
                         booking={selectedBooking}
                    />
               )}
          </>
     );
};

export default BookingsTable;