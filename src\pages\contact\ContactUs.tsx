import { useForm } from 'react-hook-form';
import { useContactUsMutation } from '../../store/features/contact-us/contactUsApi';
import { toast } from 'react-hot-toast';
import Loader from '../../components/common/Loader';

interface ContactUsFormInputs {
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    subject: string;
    message: string;
}

const ContactUs = () => {
    const [contactUs, { isLoading }] = useContactUsMutation();
    const {
        register,
        handleSubmit,
        formState: { errors },
        reset
    } = useForm<ContactUsFormInputs>({
        mode: 'onBlur'
    });

    const onSubmit = async (data: ContactUsFormInputs) => {
        try {
            const response = await contactUs(data);
            if (response.error) {
                // @ts-expect-error error
                toast.error(response.error.data?.msg || "An unexpected error occurred");
                return;
            }

            toast.success(response.data?.msg || "Message sent successfully");
            reset(); // Clear form after successful submission
        } catch (error) {
            console.log(error);
            toast.error("Failed to send message. Please try again.");
        }
    };

    return (
        <div className='flex'>
            <div className='md:ms-[275px] h-screen overflow-y-auto flex-1'>
                <div className="md:hidden block fixed md:static w-full bg-white py-[15px] sm:py-[14px] mx-auto sm:px-[30px] px-[10px] border-b border-[#E5E7EB] shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)]">
                    <h1 className="text-center md:text-start text-[20px] sm:text-[24px] leading-[32px] font-bold text-[#05073C] max-w-[1240px] mx-auto">
                        Contact Us
                    </h1>
                </div>
                <div className="md:mt-0 mt-[90px] max-w-[1240px] sm:px-[30px] px-[10px] mx-auto">
                    <div className='bg-[#E5E7EB] max-w-[830px] mx-auto mt-[60px] my-[40px] p-[20px] lg:p-[30px] border border-[#E5E7EB] rounded-[24px]'>
                        <div className="mb-[20px] sm:mb-[30px] text-center">
                            <h3 className="text-[24px] sm:text-[34px] leading-[22px] sm:leading-[32px] font-semibold text-[#05073C]">Get in Touch</h3>
                            <p className="text-[12px] sm:text-[16px] sm:leading-[28px] text-[#05073C] mt-[5px]">
                                Fill out the form below and our team will get back to you shortly.
                            </p>
                        </div>
                        <div>
                            <form onSubmit={handleSubmit(onSubmit)}>
                                <div className="grid grid-cols-1 sm:grid-cols-2 sm:gap-[10px]">
                                    <div className="mb-[10px] sm:mb-[30px]">
                                        <label className="text-[12px] sm:text-[14px] leading-[22px] font-medium text-[#636C76] mb-[5px] block">
                                            First Name <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="text"
                                            {...register('first_name', {
                                                required: 'First name is required',
                                                minLength: {
                                                    value: 3,
                                                    message: 'First name must be at least 3 characters'
                                                },
                                                maxLength: {
                                                    value: 50,
                                                    message: 'First name must not exceed 50 characters'
                                                },
                                                pattern: {
                                                    value: /^[A-Za-z]+(?:[\s][A-Za-z]+)*$/,
                                                    message: 'First name can only contain letters and single spaces between words'
                                                },
                                                validate: {
                                                    notOnlySpaces: (value) => value.trim().length > 0 || 'First name cannot be only spaces',
                                                    noConsecutiveSpaces: (value) => !/\s\s/.test(value) || 'First name cannot contain consecutive spaces'
                                                }
                                            })}
                                            className={`bg-white w-full border rounded-[8px] py-[13px] px-[20px] text-[12px] sm:text-[14px] leading-[18px] text-[#05073C] focus:outline-none transition-colors ${errors.first_name
                                                    ? 'border-red-500 focus:border-red-500'
                                                    : 'border-[#D5D5D8] focus:border-[#05073C]'
                                                }`}
                                            placeholder="Enter your first name"
                                        />
                                        {errors.first_name && (
                                            <span className="text-red-500 text-[11px] sm:text-[12px] mt-1 block">
                                                {errors.first_name.message}
                                            </span>
                                        )}
                                    </div>
                                    <div className="mb-[10px] sm:mb-[30px]">
                                        <label className="text-[12px] sm:text-[14px] leading-[22px] font-medium text-[#636C76] mb-[5px] block">
                                            Last Name <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="text"
                                            {...register('last_name', {
                                                required: 'Last name is required',
                                                minLength: {
                                                    value: 3,
                                                    message: 'Last name must be at least 3 characters'
                                                },
                                                maxLength: {
                                                    value: 50,
                                                    message: 'Last name must not exceed 50 characters'
                                                },
                                                pattern: {
                                                    value: /^[A-Za-z]+(?:[\s][A-Za-z]+)*$/,
                                                    message: 'Last name can only contain letters and single spaces between words'
                                                },
                                                validate: {
                                                    notOnlySpaces: (value) => value.trim().length > 0 || 'Last name cannot be only spaces',
                                                    noConsecutiveSpaces: (value) => !/\s\s/.test(value) || 'Last name cannot contain consecutive spaces'
                                                }
                                            })}
                                            className={`bg-white w-full border rounded-[8px] py-[13px] px-[20px] text-[12px] sm:text-[14px] leading-[18px] text-[#05073C] focus:outline-none transition-colors ${errors.last_name
                                                    ? 'border-red-500 focus:border-red-500'
                                                    : 'border-[#D5D5D8] focus:border-[#05073C]'
                                                }`}
                                            placeholder="Enter your last name"
                                        />
                                        {errors.last_name && (
                                            <span className="text-red-500 text-[11px] sm:text-[12px] mt-1 block">
                                                {errors.last_name.message}
                                            </span>
                                        )}
                                    </div>
                                </div>
                                <div className="grid grid-cols-1 sm:grid-cols-2 sm:gap-[10px]">
                                    <div className="mb-[10px] sm:mb-[30px]">
                                        <label className="text-[12px] sm:text-[14px] leading-[22px] font-medium text-[#636C76] mb-[5px] block">
                                            Email <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="email"
                                            {...register('email', {
                                                required: 'Email is required',
                                                pattern: {
                                                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                                                    message: 'Please enter a valid email address'
                                                },
                                                maxLength: {
                                                    value: 100,
                                                    message: 'Email must not exceed 100 characters'
                                                },
                                                validate: {
                                                    notOnlySpaces: (value) => value.trim().length > 0 || 'Email cannot be only spaces',
                                                    noSpaces: (value) => !/\s/.test(value) || 'Email cannot contain spaces'
                                                }
                                            })}
                                            className={`bg-white w-full border rounded-[8px] py-[13px] px-[20px] text-[12px] sm:text-[14px] leading-[18px] text-[#05073C] focus:outline-none transition-colors ${errors.email
                                                    ? 'border-red-500 focus:border-red-500'
                                                    : 'border-[#D5D5D8] focus:border-[#05073C]'
                                                }`}
                                            placeholder="Enter your email"
                                        />
                                        {errors.email && (
                                            <span className="text-red-500 text-[11px] sm:text-[12px] mt-1 block">
                                                {errors.email.message}
                                            </span>
                                        )}
                                    </div>
                                    <div className="mb-[10px] sm:mb-[30px]">
                                        <label className="text-[12px] sm:text-[14px] leading-[22px] font-medium text-[#636C76] mb-[5px] block">
                                            Phone Number <span className="text-red-500">*</span>
                                        </label>
                                        <input
                                            type="tel"
                                            {...register('phone', {
                                                required: 'Phone number is required',
                                                pattern: {
                                                    value: /^(\+?88)?01[3-9]\d{8}$/,
                                                    message: "Enter a valid Bangladeshi phone number (e.g. +8801XXXXXXXXX or 01XXXXXXXXX)"
                                                }
                                            })}
                                            className={`bg-white w-full border rounded-[8px] py-[13px] px-[20px] text-[12px] sm:text-[14px] leading-[18px] text-[#05073C] focus:outline-none transition-colors ${errors.phone
                                                    ? 'border-red-500 focus:border-red-500'
                                                    : 'border-[#D5D5D8] focus:border-[#05073C]'
                                                }`}
                                            placeholder="Enter your phone number"
                                        />
                                        {errors.phone && (
                                            <span className="text-red-500 text-[11px] sm:text-[12px] mt-1 block">
                                                {errors.phone.message}
                                            </span>
                                        )}
                                    </div>
                                </div>
                                <div className="mb-[10px] sm:mb-[30px]">
                                    <label className="text-[12px] sm:text-[14px] leading-[22px] font-medium text-[#636C76] mb-[5px] block">
                                        Subject <span className="text-red-500">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        {...register('subject', {
                                            required: 'Subject is required',
                                            minLength: {
                                                value: 5,
                                                message: 'Subject must be at least 5 characters'
                                            },
                                            maxLength: {
                                                value: 100,
                                                message: 'Subject must not exceed 100 characters'
                                            },
                                            validate: {
                                                notOnlySpaces: (value) => value.trim().length > 0 || 'Subject cannot be only spaces',
                                                noConsecutiveSpaces: (value) => !/\s\s/.test(value) || 'Subject cannot contain consecutive spaces',
                                                minWordsAfterTrim: (value) => value.trim().split(/\s+/).length >= 2 || 'Subject must contain at least 2 words'
                                            }
                                        })}
                                        className={`bg-white w-full border rounded-[8px] py-[13px] px-[20px] text-[12px] sm:text-[14px] leading-[18px] text-[#05073C] focus:outline-none transition-colors ${errors.subject
                                                ? 'border-red-500 focus:border-red-500'
                                                : 'border-[#D5D5D8] focus:border-[#05073C]'
                                            }`}
                                        placeholder="Type your subject"
                                    />
                                    {errors.subject && (
                                        <span className="text-red-500 text-[11px] sm:text-[12px] mt-1 block">
                                            {errors.subject.message}
                                        </span>
                                    )}
                                </div>
                                <div className="mb-[10px] sm:mb-[30px]">
                                    <label className="text-[12px] sm:text-[14px] leading-[22px] font-medium text-[#636C76] mb-[5px] block">
                                        Write Message <span className="text-red-500">*</span>
                                    </label>
                                    <textarea
                                        {...register('message', {
                                            required: 'Message is required',
                                            minLength: {
                                                value: 10,
                                                message: 'Message must be at least 10 characters'
                                            },
                                            maxLength: {
                                                value: 1000,
                                                message: 'Message must not exceed 1000 characters'
                                            },
                                            validate: {
                                                notOnlySpaces: (value) => value.trim().length > 0 || 'Message cannot be only spaces',
                                                noConsecutiveSpaces: (value) => !/\s\s\s/.test(value) || 'Message cannot contain more than two consecutive spaces',
                                                minWordsAfterTrim: (value) => value.trim().split(/\s+/).length >= 3 || 'Message must contain at least 3 words'
                                            }
                                        })}
                                        className={`bg-white w-full border rounded-[8px] py-[13px] px-[20px] text-[12px] sm:text-[14px] leading-[18px] text-[#05073C] focus:outline-none transition-colors h-[130px] resize-none ${errors.message
                                                ? 'border-red-500 focus:border-red-500'
                                                : 'border-[#D5D5D8] focus:border-[#05073C]'
                                            }`}
                                        placeholder="Enter your message"
                                    />
                                    {errors.message && (
                                        <span className="text-red-500 text-[11px] sm:text-[12px] mt-1 block">
                                            {errors.message.message}
                                        </span>
                                    )}
                                </div>
                                <button
                                    type="submit"
                                    className={`bg-[#0D3FC6] text-white py-[13px] px-[20px] text-[12px] sm:text-[14px] leading-[18px] rounded-[8px] min-w-[120px] cursor-pointer transition-opacity hover:bg-[#0a2fa3] ${isLoading ? "opacity-50 cursor-not-allowed" : "opacity-100"
                                        }`}
                                    disabled={isLoading}
                                >
                                    {isLoading ? <Loader /> : "Send Message"}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ContactUs;
