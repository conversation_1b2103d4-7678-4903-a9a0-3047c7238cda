import { Link } from 'react-router-dom';

const PaymentFailed = () => {
  // const navigate = useNavigate();

  // const handleTryAgain = () => {
  //   // Go back to the previous page (payment page)
  //   navigate(-1);
  // };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="md:ms-[275px] h-screen overflow-y-auto flex-1 flex flex-col items-center justify-center p-6">
        <div className="max-w-lg w-full bg-white shadow-lg rounded-lg p-8 text-center">
          <div className="w-20 h-20 bg-red-100 mx-auto rounded-full flex items-center justify-center mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>

          <h1 className="text-2xl font-bold text-[#05073C] mb-3">Payment Failed</h1>
          <p className="text-gray-600 mb-6">
            We couldn't process your payment. Your card has not been charged.
          </p>

          {/* <div className="bg-[#FFF5F5] border border-[#FED7D7] rounded-md p-4 mb-6 text-left">
            <p className="text-sm text-gray-700 mb-2">Possible reasons for failure:</p>
            <ul className="text-sm text-gray-600 list-disc pl-5">
              <li>Insufficient funds</li>
              <li>Incorrect card details</li>
              <li>Card expired or blocked</li>
              <li>Bank declined the transaction</li>
            </ul>
          </div>
           */}
          <div className="flex flex-col sm:flex-row gap-4 w-full">
            <Link
              to="/bookings"
              className="flex-1 w-fit px-6 py-3 bg-[#0D3FC6] text-white font-medium rounded-md hover:bg-[#092d94] transition-colors duration-300 text-center"
            >
              View My Bookings
            </Link>
            <Link
              to="/"
              className="flex-1 w-fit px-6 py-3 border border-[#0D3FC6] text-[#0D3FC6] font-medium rounded-md hover:bg-[#E7ECF9] transition-colors duration-300 text-center"
            >
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentFailed;