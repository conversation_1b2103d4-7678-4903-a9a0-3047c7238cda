interface ExternalClicksTours {
     id: string;
     tour_title: string;
     tour_country: string;
     tour_location: string;
     count: number;
}

const ExternalClicksTours = ({ externalClicksTours }: { externalClicksTours: ExternalClicksTours[] }) => {
     return (
          <>
               {
                    externalClicksTours?.map((pkg: ExternalClicksTours) => (
                         <tr key={pkg.id} className="hover:bg-gray-50 transition-colors">
                              <td className="px-4 sm:px-6 py-4">
                                   <div className="flex flex-col">
                                        <span className="text-sm font-medium text-gray-900 break-words">{pkg?.tour_title}</span>
                                        <span className="text-xs sm:text-sm text-gray-500 mt-1">{pkg?.tour_country}</span>
                                        {/* Mobile-only location */}
                                        <span className="text-xs text-gray-500 mt-1 sm:hidden">{pkg?.tour_location}</span>
                                   </div>
                              </td>
                              <td className="px-4 sm:px-6 py-4 text-sm text-gray-500 hidden sm:table-cell">
                                   {pkg?.tour_location}
                              </td>
                              <td className="px-4 sm:px-6 py-4">
                                   <span className="inline-flex items-center px-2 sm:px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        {pkg?.count?.toLocaleString()}
                                   </span>
                              </td>
                         </tr>
                    ))
               }
          </>
     )
}

export default ExternalClicksTours;