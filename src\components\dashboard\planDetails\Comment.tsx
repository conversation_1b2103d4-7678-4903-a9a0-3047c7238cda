import React, { useState, useRef } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import Cookies from "js-cookie";
import SignInModal from "../chat/SignInModal";
import SignUpModal from "../chat/SignUpModal";
import { useAddReviewMutation } from "../../../store/features/reviews/reviews";
import { toast } from "react-hot-toast";
import { useParams } from "react-router-dom";

const ratingOptions = [
     {
          id: 1,
          name: "Accommodation",
          field: "accommodation"
     },
     {
          id: 2,
          name: "Transportation",
          field: "transportation"
     },
     {
          id: 3,
          name: "Activities",
          field: "activities"
     },
     {
          id: 4,
          name: "Accuracy of Package",
          field: "accuracy_of_package"
     },
     {
          id: 5,
          name: "Value for Money",
          field: "value_for_money"
     },
     {
          id: 6,
          name: "Safety & Security",
          field: "safety_and_security"
     },
]

type FormValues = {
     overallRating: number;
     comment: string;
     reviewTitle: string;
     categoryRatings: { [key: string]: number };
};

const Comment = () => {
     const { control, handleSubmit, watch, reset, formState: { errors } } = useForm<FormValues>({
          defaultValues: {
               overallRating: 0,
               comment: "",
               reviewTitle: "",
               categoryRatings: {},
          },
     });

     const [addReview, { isLoading }] = useAddReviewMutation();
     const isAuthenticated = Cookies.get("accessToken");
     const [showSignInModal, setShowSignInModal] = useState(false);
     const [showSignUpModal, setShowSignUpModal] = useState(false);
     const { tour_id } = useParams();

     const [images, setImages] = useState<File[]>([]);
     const [videos, setVideos] = useState<File[]>([]);
     const [imagePreviews, setImagePreviews] = useState<string[]>([]);
     const [videoPreviews, setVideoPreviews] = useState<string[]>([]);

     const overallRating = watch("overallRating");

     const imageInputRef = useRef<HTMLInputElement>(null);
     const videoInputRef = useRef<HTMLInputElement>(null);

     // Calculate total size of files in bytes
     const getTotalSize = (imageFiles: File[], videoFiles: File[]): number => {
          const imageSize = imageFiles.reduce((total, file) => total + file.size, 0);
          const videoSize = videoFiles.reduce((total, file) => total + file.size, 0);
          return imageSize + videoSize;
     };

     const MAX_TOTAL_SIZE = 10 * 1024 * 1024; // 10MB in bytes

     const validateFileSize = (newFiles: File[], isVideo: boolean): boolean => {
          const totalSize = getTotalSize(
               isVideo ? images : [...images, ...newFiles],
               isVideo ? [...videos, ...newFiles] : videos
          );

          if (totalSize > MAX_TOTAL_SIZE) {
               toast.error("Total size of images and videos cannot exceed 10MB");
               return false;
          }
          return true;
     };

     const onImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
          if (e.target.files) {
               const files = Array.from(e.target.files);

               if (!validateFileSize(files, false)) {
                    if (imageInputRef.current) {
                         imageInputRef.current.value = "";
                    }
                    return;
               }

               setImages(prev => [...prev, ...files]);
               const newPreviews = files.map(file => URL.createObjectURL(file));
               setImagePreviews(prev => [...prev, ...newPreviews]);
          }
     };

     const onVideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
          if (e.target.files) {
               const files = Array.from(e.target.files);

               if (!validateFileSize(files, true)) {
                    if (videoInputRef.current) {
                         videoInputRef.current.value = "";
                    }
                    return;
               }

               setVideos(prev => [...prev, ...files]);
               const newPreviews = files.map(file => URL.createObjectURL(file));
               setVideoPreviews(prev => [...prev, ...newPreviews]);
          }
     };

     // Remove image by index
     const removeImage = (idx: number) => {
          setImages(prev => prev.filter((_, i) => i !== idx));
          setImagePreviews(prev => prev.filter((_, i) => i !== idx));

          // Reset file input if all images are removed
          if (images.length === 1 && imageInputRef.current) {
               imageInputRef.current.value = "";
          }
     };

     // Remove video by index
     const removeVideo = (idx: number) => {
          setVideos(prev => prev.filter((_, i) => i !== idx));
          setVideoPreviews(prev => prev.filter((_, i) => i !== idx));

          // Reset file input if all videos are removed
          if (videos.length === 1 && videoInputRef.current) {
               videoInputRef.current.value = "";
          }
     };

     const onSubmit = async (data: FormValues) => {
          try {
               const formData = new FormData();

               // Add tour_id
               formData.append("tour_id", tour_id || "");

               // Add tour guide rating (using overall rating value)
               formData.append("tour_guide_rating", data.overallRating.toString());

               // Add ratings for each category
               ratingOptions.forEach(option => {
                    const rating = data.categoryRatings[option.id] || 0;
                    formData.append(option.field, rating.toString());
               });

               // Add review title and description
               formData.append("review_title", data.reviewTitle);
               formData.append("review_description", data.comment);


               // Add media files (images and videos)
               images.forEach(image => {
                    formData.append("media", image);
               });

               videos.forEach(video => {
                    formData.append("media", video);
               });

               const response = await addReview(formData);

               if ("error" in response) {
                    toast.error("Failed to submit review");
               } else {
                    toast.success("Review submitted successfully");
                    reset();
                    setImages([]);
                    setVideos([]);
                    setImagePreviews([]);
                    setVideoPreviews([]);
               }
          } catch (error) {
               console.error("Error submitting review:", error);
               toast.error("Error submitting review");
          }
     };

     if (!isAuthenticated) {
          return (
               <div className="mt-[40px] bg-[#F9FBFF] border border-[#E7ECF9] rounded-[15px] p-[30px] max-w-[950px] shadow-sm">
                    <div className="flex flex-col items-center justify-center text-center gap-4">
                         <p className="text-[18px] font-medium text-[#05073C]">Login to Leave a Review</p>
                         <p className="text-[16px] text-[#6C6C6C]">You need to be logged in to share your travel experience.</p>
                         <div className="flex gap-4 mt-2">
                              <button
                                   onClick={() => setShowSignInModal(true)}
                                   className="bg-[#0D3FC6] text-white py-[10px] px-[20px] rounded-[8px] font-medium hover:bg-[#092d94] transition-all"
                              >
                                   Sign In
                              </button>
                              <button
                                   onClick={() => setShowSignUpModal(true)}
                                   className="border border-[#0D3FC6] text-[#0D3FC6] py-[10px] px-[20px] rounded-[8px] font-medium hover:bg-[#E7ECF9] transition-all"
                              >
                                   Sign Up
                              </button>
                         </div>
                    </div>

                    {showSignInModal && (
                         <SignInModal
                              onClose={() => setShowSignInModal(false)}
                              onSignUpClick={() => {
                                   setShowSignInModal(false);
                                   setShowSignUpModal(true);
                              }}
                         />
                    )}

                    {showSignUpModal && (
                         <SignUpModal
                              onClose={() => setShowSignUpModal(false)}
                              onSignInClick={() => {
                                   setShowSignUpModal(false);
                                   setShowSignInModal(true);
                              }}
                         />
                    )}
               </div>
          );
     }

     return (
          <form
               onSubmit={handleSubmit(onSubmit)}
               className="mt-[40px] bg-[#F9FBFF] border border-[#E7ECF9] rounded-[15px] p-[30px] max-w-[950px] shadow-sm"
          >
               {/* Overall Rating */}
               <div className="mb-[20px] flex items-center justify-center flex-col">
                    <p className="text-[18px] font-medium text-[#05073C] mb-[12px]">Leave a Review</p>
                    <Controller
                         name="overallRating"
                         control={control}
                         rules={{ required: "Please select a rating", min: { value: 1, message: "Please select at least 1 star" } }}
                         render={({ field }) => (
                              <div className="flex flex-col items-center">
                                   <div className="flex gap-2">
                                        {[1, 2, 3, 4, 5].map((star) => (
                                             <button
                                                  key={star}
                                                  type="button"
                                                  onClick={() => field.onChange(star)}
                                                  className="focus:outline-none transform hover:scale-110 transition-transform"
                                             >
                                                  <img
                                                       src={star <= field.value ? "/star-fill.svg" : "/star.svg"}
                                                       alt="star"
                                                       className="w-10 h-10"
                                                  />
                                             </button>
                                        ))}
                                   </div>
                                   {errors.overallRating && (
                                        <p className="text-red-500 text-sm mt-2">{errors.overallRating.message}</p>
                                   )}
                              </div>
                         )}
                    />
               </div>

               {/* Divider when rating is selected */}
               {overallRating > 0 && <hr className="border-t border-[#E7ECF9] mb-[25px]" />}

               {/* Show only if overall rating is selected */}
               {overallRating > 0 && (
                    <>
                         {/* Title Field */}
                         <div className="mb-[30px]">
                              <label className="block mb-2 text-[16px] font-medium text-[#05073C]">
                                   Review Title <span className="text-red-500">*</span>
                              </label>
                              <Controller
                                   name="reviewTitle"
                                   control={control}
                                   rules={{
                                        required: "Review title is required",
                                        validate: {
                                             noWhitespace: (value) => value.trim() !== "" || "Title cannot be empty",
                                             minLength: (value) => value.trim().length >= 3 || "Title must be at least 3 characters"
                                        }
                                   }}
                                   render={({ field }) => (
                                        <div>
                                             <input
                                                  type="text"
                                                  placeholder="Enter a title for your review"
                                                  className={`w-full border rounded-[10px] p-[15px] focus:outline-none focus:ring-2 focus:ring-[#0D3FC6] focus:border-transparent transition-all ${errors.reviewTitle ? 'border-red-500' : 'border-[#E7ECF9]'
                                                       }`}
                                                  {...field}
                                             />
                                             {errors.reviewTitle && (
                                                  <p className="text-red-500 text-sm mt-1">{errors.reviewTitle.message}</p>
                                             )}
                                        </div>
                                   )}
                              />
                         </div>

                         {/* Category Ratings */}
                         <div className="grid grid-cols-1 md:grid-cols-2 gap-y-[20px] gap-x-[40px] mb-[30px]">
                              {ratingOptions.map((option) => (
                                   <div key={option.id} className="flex justify-between items-center">
                                        <p className="text-[16px] font-medium text-[#05073C] w-[250px]">{option.name}</p>
                                        <Controller
                                             name={`categoryRatings.${option.id}` as const}
                                             control={control}
                                             defaultValue={0}
                                             render={({ field }) => (
                                                  <div className="flex gap-1">
                                                       {[1, 2, 3, 4, 5].map((star) => (
                                                            <button
                                                                 key={star}
                                                                 type="button"
                                                                 onClick={() => field.onChange(star)}
                                                                 className="focus:outline-none hover:opacity-80 transition-opacity"
                                                            >
                                                                 <img
                                                                      src={star <= (field.value || 0) ? "/star-fill.svg" : "/star.svg"}
                                                                      alt="star"
                                                                      className="w-6 h-6"
                                                                 />
                                                            </button>
                                                       ))}
                                                  </div>
                                             )}
                                        />
                                   </div>
                              ))}
                         </div>

                         {/* Divider before media upload */}
                         <hr className="border-t border-[#E7ECF9] mb-[25px]" />

                         {/* Image Upload */}
                         <div className="mb-[20px]">
                              <label className="block mb-2 text-[16px] font-medium text-[#05073C]">Add Images</label>
                              <input
                                   ref={imageInputRef}
                                   type="file"
                                   accept="image/*"
                                   multiple
                                   onChange={onImageChange}
                                   className="block w-full text-sm text-gray-500
                                   file:mr-4 file:py-2 file:px-4
                                   file:rounded-full file:border-0
                                   file:text-sm file:font-semibold
                                   file:bg-[#E7ECF9] file:text-[#0D3FC6]
                                   hover:file:bg-[#dbeafe]"
                              />
                              {/* Image Previews */}
                              {imagePreviews.length > 0 && (
                                   <div className="flex gap-3 mt-3 flex-wrap">
                                        {imagePreviews.map((src, idx) => (
                                             <div key={idx} className="relative group">
                                                  <img
                                                       src={src}
                                                       alt={`preview-${idx}`}
                                                       className="w-[180px] h-[180px] object-cover rounded-lg shadow-md"
                                                  />
                                                  <button
                                                       type="button"
                                                       onClick={() => removeImage(idx)}
                                                       className="absolute top-2 right-2 bg-white bg-opacity-80 h-8 w-8 rounded-full p-1 text-red-600 hover:bg-opacity-100 shadow group-hover:visible"
                                                       title="Remove image"
                                                  >
                                                       &times;
                                                  </button>
                                             </div>
                                        ))}
                                   </div>
                              )}
                         </div>

                         {/* Video Upload */}
                         <div className="mb-[20px]">
                              <label className="block mb-2 text-[16px] font-medium text-[#05073C]">Add Videos</label>
                              <input
                                   ref={videoInputRef}
                                   type="file"
                                   accept="video/*"
                                   multiple
                                   onChange={onVideoChange}
                                   className="block w-full text-sm text-gray-500
                                   file:mr-4 file:py-2 file:px-4
                                   file:rounded-full file:border-0
                                   file:text-sm file:font-semibold
                                   file:bg-[#E7ECF9] file:text-[#0D3FC6]
                                   hover:file:bg-[#dbeafe]"
                              />
                              {/* Video Previews */}
                              {videoPreviews.length > 0 && (
                                   <div className="flex gap-3 mt-3 flex-wrap">
                                        {videoPreviews.map((src, idx) => (
                                             <div key={idx} className="relative group">
                                                  <video
                                                       src={src}
                                                       controls
                                                       className="w-[180px] h-[180px] object-cover rounded-lg shadow-md"
                                                  />
                                                  <button
                                                       type="button"
                                                       onClick={() => removeVideo(idx)}
                                                       className="absolute top-2 right-2 bg-white bg-opacity-80 h-8 w-8 rounded-full p-1 text-red-600 hover:bg-opacity-100 shadow group-hover:visible"
                                                       title="Remove video"
                                                  >
                                                       &times;
                                                  </button>
                                             </div>
                                        ))}
                                   </div>
                              )}
                         </div>

                         <hr className="border-t border-[#E7ECF9] mb-[25px]" />

                         {/* Comment Textarea */}
                         <div className="mb-[30px]">
                              <label className="block mb-2 text-[16px] font-medium text-[#05073C]">
                                   Review Description <span className="text-red-500">*</span>
                              </label>
                              <Controller
                                   name="comment"
                                   control={control}
                                   rules={{
                                        required: "Review description is required",
                                        validate: {
                                             noWhitespace: (value) => value.trim() !== "" || "Description cannot be empty",
                                             minLength: (value) => value.trim().length >= 10 || "Description must be at least 10 characters"
                                        }
                                   }}
                                   render={({ field }) => (
                                        <div>
                                             <textarea
                                                  placeholder="Share your experience..."
                                                  className={`w-full h-[120px] border rounded-[10px] p-[15px] resize-none focus:outline-none focus:ring-2 focus:ring-[#0D3FC6] focus:border-transparent transition-all ${errors.comment ? 'border-red-500' : 'border-[#E7ECF9]'
                                                       }`}
                                                  {...field}
                                             />
                                             {errors.comment && (
                                                  <p className="text-red-500 text-sm mt-1">{errors.comment.message}</p>
                                             )}
                                        </div>
                                   )}
                              />
                         </div>

                         {/* Post Comment Button */}
                         <button
                              type="submit"
                              className="bg-[#0D3FC6] text-white py-[12px] px-[30px] rounded-[12px] font-semibold text-[16px] hover:bg-[#092d94] transition-all duration-300 shadow-sm disabled:opacity-50"
                              disabled={isLoading}
                         >
                              {isLoading ? "Submitting..." : "Post Comment"}
                         </button>
                    </>
               )}
          </form>
     )
}

export default Comment;  