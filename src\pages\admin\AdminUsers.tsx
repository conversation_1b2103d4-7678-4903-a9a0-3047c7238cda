import { useState } from "react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useGetUsersListQuery } from "../../store/features/admin/adminApi";
import AdminUsersTableSkeleton from "../../components/skeletons/AdminUsersTableSkeleton";
import { Menu, Transition } from "@headlessui/react";
import { ChevronDownIcon } from "@heroicons/react/24/solid";
import { Fragment } from "react";
import { toast } from "react-hot-toast";
import { useUpdateUserRoleMutation } from "../../store/features/admin/adminApi"

interface User {
  id: string;
  username: string;
  email: string;
  phone: string;
  status: string;
  LastChat: string;
  TotalThreads: number;
  role: string;
}

const AdminUsers = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const { data: usersData, isLoading } = useGetUsersListQuery({});
  const [searchTerm, setSearchTerm] = useState("");
  const navigate = useNavigate();
  const { userId } = useParams();
  const [updateUserRole] = useUpdateUserRoleMutation()

  // Filter users based on search
  const filteredUsers = usersData?.filter((user: User) =>
    user?.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user?.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Calculate pagination
  const totalUsers = filteredUsers?.length || 0;
  const totalPages = Math.ceil(totalUsers / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentUsers = filteredUsers?.slice(indexOfFirstItem, indexOfLastItem);

  const handleViewThreads = (userId: string) => {
    navigate(`/admin/users/${userId}/threads`);
  }

  const handleRoleUpdate = async (userId: string, newRole: 'staff' | 'user' | 'superadmin') => {
    try {
      const payload = {
        id: userId,
        is_staff: newRole === 'staff',
        is_superuser: newRole === 'superadmin'
      }
      await updateUserRole(payload).unwrap();
      toast.success(`User role updated successfully`);
    } catch (error) {
      console.error('Failed to update role:', error);
      toast.error('Failed to update user role');
    }
  }

  // Pagination controls
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const handleLastThreadClick = (lastThread: string) => {
    if (!lastThread) {
      return;
    }
    navigate(`/admin/users/threads/${lastThread}/${userId}/logs`);
  }

  const renderPaginationButtons = () => {
    const buttons = [];

    // Previous button
    buttons.push(
      <button
        key="prev"
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:bg-gray-100 disabled:cursor-not-allowed"
      >
        <span className="sr-only">Previous</span>
        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
      </button>
    );

    const renderPageButton = (pageNum: number) => (
      <button
        key={pageNum}
        onClick={() => handlePageChange(pageNum)}
        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${currentPage === pageNum
          ? 'z-10 bg-blue-600 border-blue-600 text-white'
          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
          }`}
      >
        {pageNum}
      </button>
    );

    if (totalPages <= 4) {
      // If 4 or fewer pages, show all page numbers
      for (let i = 1; i <= totalPages; i++) {
        buttons.push(renderPageButton(i));
      }
    } else {
      // Always show first two pages
      buttons.push(renderPageButton(1));
      buttons.push(renderPageButton(2));

      // If current page is not near the start or end, show ellipsis
      if (currentPage > 3) {
        buttons.push(
          <span key="ellipsis-start" className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
            ...
          </span>
        );
      }

      // If we're not near the start, and not near the end, show current page
      if (currentPage > 3 && currentPage < totalPages - 2) {
        buttons.push(renderPageButton(currentPage));
      }

      // If current page is not near the start, show ellipsis
      if (currentPage < totalPages - 2) {
        buttons.push(
          <span key="ellipsis-end" className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
            ...
          </span>
        );
      }

      // Always show last two pages
      buttons.push(renderPageButton(totalPages - 1));
      buttons.push(renderPageButton(totalPages));
    }

    // Next button
    buttons.push(
      <button
        key="next"
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:bg-gray-100 disabled:cursor-not-allowed"
      >
        <span className="sr-only">Next</span>
        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
        </svg>
      </button>
    );

    return buttons;
  };

  return (
    <>
      {isLoading ? <AdminUsersTableSkeleton /> :
        <>

          <div className="mb-4 sm:mb-6">
            <div className="relative w-full sm:max-w-md">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search users..."
                className="w-full pl-10 pr-4 py-2 text-sm sm:text-base border border-[#E5E7EB] rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <svg className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>

          {/* Desktop Table View */}
          <div className="sm:block hidden" >
            <div className="overflow-x-auto  max-h-[calc(100vh-240px)] max-w-[calc(100vw-350px)]">
              <table className="divide-y divide-[#E5E7EB]">
                <thead className="bg-gray-50 ">
                  <tr>
                    <th className="px-6 py-3 rounded-lg  text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Chat</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Threads</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-[#E5E7EB]">
                  {currentUsers?.map((user: User) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                            <span className="text-blue-600 font-medium">{user?.username?.charAt(0).toUpperCase()}</span>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-[#05073C]">{user?.username}</div>
                            <div className="text-sm text-[#6B7280]">ID: {user?.id}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-[#05073C]">{user?.email}</div>
                        <div className="text-sm text-[#6B7280]">{user?.phone}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:underline cursor-pointer" onClick={() => handleLastThreadClick(user?.LastChat)}>
                        {user?.LastChat}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {user?.TotalThreads} threads
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col gap-1">
                          <Menu as="div" className="relative inline-block text-left">
                            <div>
                              <Menu.Button className={`inline-flex w-full items-center justify-between px-3 py-2 text-sm font-medium rounded-md ${user?.role === 'superuser'
                                ? 'bg-purple-100 text-purple-800'
                                : user?.role === 'staff'
                                  ? 'bg-blue-100 text-blue-800'
                                  : 'bg-gray-100 text-gray-800'
                                } hover:bg-opacity-80 focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75`}>
                                <span>
                                  {user?.role === 'superuser'
                                    ? 'Super Admin'
                                    : user?.role === 'staff'
                                      ? 'Staff'
                                      : 'User'}
                                </span>
                                <ChevronDownIcon
                                  className="ml-2 -mr-1 h-5 w-5"
                                  aria-hidden="true"
                                />
                              </Menu.Button>
                            </div>
                            <Transition
                              as={Fragment}
                              enter="transition ease-out duration-100"
                              enterFrom="transform opacity-0 scale-95"
                              enterTo="transform opacity-100 scale-100"
                              leave="transition ease-in duration-75"
                              leaveFrom="transform opacity-100 scale-100"
                              leaveTo="transform opacity-0 scale-95"
                            >
                              <Menu.Items className="absolute right-0 mt-2 w-40 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                                <div className="px-1 py-1">
                                  <Menu.Item>
                                    {({ active }) => (
                                      <button
                                        onClick={() => handleRoleUpdate(user.id, 'staff')}
                                        className={`${active || user?.role === 'staff' ? 'bg-blue-100 text-blue-900' : 'text-gray-900'
                                          } group flex w-full items-center rounded-md px-2 py-2 text-sm`}
                                        disabled={user?.role === 'staff'}
                                      >
                                        Staff
                                      </button>
                                    )}
                                  </Menu.Item>
                                  <Menu.Item>
                                    {({ active }) => (
                                      <button
                                        onClick={() => handleRoleUpdate(user.id, 'user')}
                                        className={`${active || user?.role === 'user' ? 'bg-gray-100 text-gray-900' : 'text-gray-900'
                                          } group flex w-full items-center rounded-md px-2 py-2 text-sm`}
                                        disabled={user?.role === 'user'}
                                      >
                                        User
                                      </button>
                                    )}
                                  </Menu.Item>
                                </div>
                              </Menu.Items>
                            </Transition>
                          </Menu>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          type='button'
                          className="text-blue-600 hover:text-blue-900 cursor-pointer"
                          onClick={() => handleViewThreads(user?.id)}
                        >
                          View Threads
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Mobile Card View */}
          <div className="lg:hidden space-y-4">
            {currentUsers?.map((user: User) => (
              <div key={user.id} className="bg-white rounded-lg border border-[#E5E7EB] shadow-sm p-4">
                {/* User Header */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                      <span className="text-blue-600 font-medium text-sm">{user?.username?.charAt(0)?.toUpperCase()}</span>
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-[#05073C]">{user?.username}</div>
                      <div className="text-xs text-[#6B7280]">ID: {user?.id}</div>
                    </div>
                  </div>
                </div>

                {/* User Details */}
                <div className="space-y-2 mb-3">
                  <div className="flex flex-col sm:flex-row sm:justify-between">
                    <div className="text-xs text-[#6B7280] mb-1 sm:mb-0">Email:</div>
                    <div className="text-sm text-[#05073C] break-all">{user?.email}</div>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:justify-between">
                    <div className="text-xs text-[#6B7280] mb-1 sm:mb-0">Last Chat:</div>
                    <div className="text-sm text-blue-500 hover:underline cursor-pointer">{user?.LastChat}</div>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:justify-between">
                    <div className="text-xs text-[#6B7280] mb-1 sm:mb-0">Threads:</div>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 w-fit mt-1.5 sm:mt-0">
                      {user?.TotalThreads} threads
                    </span>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:justify-between">
                    <div className="text-xs text-[#6B7280] mb-1 sm:mb-0">Role:</div>
                    <Menu as="div" className="relative inline-block text-left">
                      <div>
                        <Menu.Button className={`inline-flex w-full items-center justify-between px-3 py-2 text-sm font-medium rounded-md ${user?.role === 'superuser'
                          ? 'bg-purple-100 text-purple-800'
                          : user?.role === 'staff'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-800'
                          } hover:bg-opacity-80 focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75`}>
                          <span>
                            {user?.role === 'superuser'
                              ? 'Super Admin'
                              : user?.role === 'staff'
                                ? 'Staff'
                                : 'User'}
                          </span>

                          <ChevronDownIcon
                            className="ml-2 -mr-1 h-5 w-5"
                            aria-hidden="true"
                          />
                        </Menu.Button>
                      </div>
                      <Transition
                        as={Fragment}
                        enter="transition ease-out duration-100"
                        enterFrom="transform opacity-0 scale-95"
                        enterTo="transform opacity-100 scale-100"
                        leave="transition ease-in duration-75"
                        leaveFrom="transform opacity-100 scale-100"
                        leaveTo="transform opacity-0 scale-95"
                      >
                        <Menu.Items className="absolute right-0 mt-2 w-40 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                          <div className="px-1 py-1">

                            <Menu.Item>
                              {({ active }) => (
                                <button
                                  onClick={() => handleRoleUpdate(user.id, 'staff')}
                                  className={`${active || (user?.role === 'staff') ? 'bg-blue-100 text-blue-900' : 'text-gray-900'
                                    } group flex w-full items-center rounded-md px-2 py-2 text-sm`}
                                  disabled={user?.role === 'staff'}
                                >
                                  Staff
                                </button>
                              )}
                            </Menu.Item>
                            <Menu.Item>
                              {({ active }) => (
                                <button
                                  onClick={() => handleRoleUpdate(user.id, 'user')}
                                  className={`${active || user?.role === 'user' ? 'bg-gray-100 text-gray-900' : 'text-gray-900'
                                    } group flex w-full items-center rounded-md px-2 py-2 text-sm`}
                                  disabled={user?.role === 'user'}
                                >
                                  User
                                </button>
                              )}
                            </Menu.Item>
                          </div>
                        </Menu.Items>
                      </Transition>
                    </Menu>
                  </div>
                </div>

                {/* Action Button */}
                <div className="pt-3 border-t border-[#E5E7EB]">
                  <Link
                    to={`/admin/users/${user?.id}/threads`}
                    className="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    View Threads
                  </Link>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination Controls */}
          {filteredUsers?.length > 0 && (
            <div className="mt-4 sm:mt-6 flex justify-between items-center">
              <div className="text-sm text-gray-700">
                Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(indexOfLastItem, totalUsers)}
                </span>{' '}
                of <span className="font-medium">{totalUsers}</span> results
              </div>
              <div className="flex justify-center space-x-1">
                {renderPaginationButtons()}
              </div>
            </div>
          )}

          {/* No Results */}
          {filteredUsers?.length === 0 && (
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No users found</h3>
              <p className="mt-1 text-sm text-gray-500">Try adjusting your search criteria.</p>
            </div>
          )}
        </>}
    </>
  );
};

export default AdminUsers;   