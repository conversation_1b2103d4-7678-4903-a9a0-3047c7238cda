import LockIcon from '../../assets/lock-icon-pass.svg';
import { useState } from 'react';
import { useResetPasswordMutation } from '../../store/features/auth/authApi';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';

interface PasswordFormInputs {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
}

const AdminChangePassword = () => {
    const [showCurrentPassword, setShowCurrentPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [resetPassword, { isLoading }] = useResetPasswordMutation();

    const {
        register,
        handleSubmit,
        formState: { errors },
        watch,
        reset
    } = useForm<PasswordFormInputs>();

    const onSubmit = async (data: PasswordFormInputs) => {
        try {
            const response = await resetPassword({
                old_password: data.currentPassword,
                new_password: data.newPassword
            }).unwrap();

            if (response) {
                toast.success('Password changed successfully');
                reset();
            }
        } catch (error: unknown) {
            const err = error as { data: { response: string } };
            toast.error(err?.data?.response || 'Failed to change password');
        }
    };

    return (
        <div className='flex'>
            <div className='md:ms-[275px] h-screen overflow-y-auto flex-1'>
                <div className="px-[10px] sm:px-[20px]">
                    <div className='bg-[#E5E7EB] max-w-[635px] mx-auto mt-[60px] my-[40px] p-[20px] lg:p-[30px] border border-[#E5E7EB] rounded-[24px]'>
                        <div className="mb-[20px] sm:mb-[30px] text-center">
                            <h3 className="text-[24px] sm:text-[34px] leading-[22px] sm:leading-[32px] font-semibold text-[#05073C]">Change Password</h3>
                            <p className="text-[12px] sm:text-[16px] sm:leading-[28px] text-[#05073C] mt-[5px]">
                                Enter your current and new password to make the change.
                            </p>
                        </div>

                        <div>
                            <form onSubmit={handleSubmit(onSubmit)}>
                                <div className="mb-[10px] sm:mb-[30px]">
                                    <label className="text-[12px] sm:text-[14px] leading-[22px] font-medium text-[#636C76] mb-[5px] block">
                                        Current Password
                                    </label>
                                    <div className='relative'>
                                        <img src={LockIcon} alt="Lock" className='absolute top-1/2 left-3 -translate-y-1/2 w-[20px] h-[20px]' />
                                        <input
                                            type={showCurrentPassword ? "text" : "password"}
                                            className={`bg-white w-full border ${errors.currentPassword ? 'border-red-500' : 'border-[#D5D5D8]'} rounded-[8px] py-[13px] px-[20px] ps-[40px] text-[12px] sm:text-[14px] leading-[18px] text-[#05073C] focus:outline-none focus:border-[#05073C]`}
                                            placeholder="Enter your current password"
                                            {...register('currentPassword', {
                                                required: 'Current password is required',
                                                minLength: {
                                                    value: 8,
                                                    message: 'Password must be at least 8 characters'
                                                }
                                            })}
                                        />
                                        <img
                                            src={showCurrentPassword ? "/eye-open.svg" : "/eye-close.svg"}
                                            alt="Eye"
                                            className='absolute top-1/2 right-3 -translate-y-1/2 w-[20px] h-[20px] cursor-pointer'
                                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                        />
                                    </div>
                                    {errors.currentPassword && (
                                        <p className="text-red-500 text-xs mt-1">{errors.currentPassword.message}</p>
                                    )}
                                </div>

                                <div className="mb-[10px] sm:mb-[30px]">
                                    <label className="text-[12px] sm:text-[14px] leading-[22px] font-medium text-[#636C76] mb-[5px] block">
                                        New Password
                                    </label>
                                    <div className='relative'>
                                        <img src={LockIcon} alt="Lock" className='absolute top-1/2 left-3 -translate-y-1/2 w-[20px] h-[20px]' />
                                        <input
                                            type={showNewPassword ? "text" : "password"}
                                            className={`bg-white w-full border ${errors.newPassword ? 'border-red-500' : 'border-[#D5D5D8]'} rounded-[8px] py-[13px] px-[20px] ps-[40px] text-[12px] sm:text-[14px] leading-[18px] text-[#05073C] focus:outline-none focus:border-[#05073C]`}
                                            placeholder="Enter your new password"
                                            {...register('newPassword', {
                                                required: 'New password is required',
                                                minLength: {
                                                    value: 8,
                                                    message: 'Password must be at least 8 characters'
                                                },
                                                pattern: {
                                                    value: /^(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.*[a-z]).{8,}$/,
                                                    message: 'Password must contain at least one uppercase letter, one number, and one special character'
                                                }
                                            })}
                                        />
                                        <img
                                            src={showNewPassword ? "/eye-open.svg" : "/eye-close.svg"}
                                            alt="Eye"
                                            className='absolute top-1/2 right-3 -translate-y-1/2 w-[20px] h-[20px] cursor-pointer'
                                            onClick={() => setShowNewPassword(!showNewPassword)}
                                        />
                                    </div>
                                    {errors.newPassword && (
                                        <p className="text-red-500 text-xs mt-1">{errors.newPassword.message}</p>
                                    )}
                                </div>

                                <div className="mb-[15px] sm:mb-[30px]">
                                    <label className="text-[12px] sm:text-[14px] leading-[22px] font-medium text-[#636C76] mb-[5px] block">
                                        Confirm New Password
                                    </label>
                                    <div className='relative'>
                                        <img src={LockIcon} alt="Lock" className='absolute top-1/2 left-3 -translate-y-1/2 w-[20px] h-[20px]' />
                                        <input
                                            type={showConfirmPassword ? "text" : "password"}
                                            className={`bg-white w-full border ${errors.confirmPassword ? 'border-red-500' : 'border-[#D5D5D8]'} rounded-[8px] py-[13px] px-[20px] ps-[40px] text-[12px] sm:text-[14px] leading-[18px] text-[#05073C] focus:outline-none focus:border-[#05073C]`}
                                            placeholder="Confirm your new password"
                                            {...register('confirmPassword', {
                                                required: 'Please confirm your password',
                                                validate: (value) => value === watch('newPassword') || 'Passwords do not match'
                                            })}
                                        />
                                        <img
                                            src={showConfirmPassword ? "/eye-open.svg" : "/eye-close.svg"}
                                            alt="Eye"
                                            className='absolute top-1/2 right-3 -translate-y-1/2 w-[20px] h-[20px] cursor-pointer'
                                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                        />
                                    </div>
                                    {errors.confirmPassword && (
                                        <p className="text-red-500 text-xs mt-1">{errors.confirmPassword.message}</p>
                                    )}
                                </div>

                                <button
                                    type="submit"
                                    disabled={isLoading}
                                    className={`bg-[#0D3FC6] text-white py-[13px] px-[25px] text-[12px] sm:text-[14px] leading-[18px] rounded-[8px] w-max cursor-pointer ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
                                >
                                    {isLoading ? 'Changing Password...' : 'Change Password'}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AdminChangePassword;
