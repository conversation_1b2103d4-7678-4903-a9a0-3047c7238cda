import { useNavigate, useParams } from "react-router-dom";
import { useGetThreadsListQuery } from "../../store/features/admin/adminApi";
import AdminChatThreadsSkeleton from "../../components/skeletons/AdminChatThreadsSkeleton";
import { useDispatch } from "react-redux";
import { setMessages, threadUid as setThreadUid } from "../../store/features/chat/chatSlice";
import { useState } from "react";

interface ChatThread {
  uid: string;
  created_at: string;
  thread_name: string;
  user: string;
}

const AdminChatThreads = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const { data: userThreads, isLoading } = useGetThreadsListQuery(userId!, { skip: !userId });
  const userThreadsData = userThreads?.data;
  const dispatch = useDispatch();

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Calculate pagination
  const totalThreads = userThreadsData?.length || 0;
  const totalPages = Math.ceil(totalThreads / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentThreads = userThreadsData?.slice(indexOfFirstItem, indexOfLastItem);

  const handleViewHistory = (threadId: string) => {
    dispatch(setMessages([]));
    dispatch(setThreadUid(null));
    navigate(`/admin/users/threads/${threadId}/${userId}/logs`);
  }

  // Pagination controls
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const renderPaginationButtons = () => {
    const buttons = [];

    // Previous button
    buttons.push(
      <button
        key="prev"
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:bg-gray-100 disabled:cursor-not-allowed"
      >
        <span className="sr-only">Previous</span>
        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
      </button>
    );

    const renderPageButton = (pageNum: number) => (
      <button
        key={pageNum}
        onClick={() => handlePageChange(pageNum)}
        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${currentPage === pageNum
            ? 'z-10 bg-blue-600 border-blue-600 text-white'
            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
          }`}
      >
        {pageNum}
      </button>
    );

    if (totalPages <= 4) {
      // If 4 or fewer pages, show all page numbers
      for (let i = 1; i <= totalPages; i++) {
        buttons.push(renderPageButton(i));
      }
    } else {
      // Always show first two pages
      buttons.push(renderPageButton(1));
      buttons.push(renderPageButton(2));

      // If current page is not near the start or end, show ellipsis
      if (currentPage > 3) {
        buttons.push(
          <span key="ellipsis-start" className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
            ...
          </span>
        );
      }

      // If we're not near the start, and not near the end, show current page
      if (currentPage > 3 && currentPage < totalPages - 2) {
        buttons.push(renderPageButton(currentPage));
      }

      // If current page is not near the start, show ellipsis
      if (currentPage < totalPages - 2) {
        buttons.push(
          <span key="ellipsis-end" className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
            ...
          </span>
        );
      }

      // Always show last two pages
      buttons.push(renderPageButton(totalPages - 1));
      buttons.push(renderPageButton(totalPages));
    }

    // Next button
    buttons.push(
      <button
        key="next"
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:bg-gray-100 disabled:cursor-not-allowed"
      >
        <span className="sr-only">Next</span>
        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
        </svg>
      </button>
    );

    return buttons;
  };

  return (
    <>
      {isLoading ? <AdminChatThreadsSkeleton /> :
        <div className="mt-[60px] max-w-[1240px] sm:px-[30px] px-[10px] mx-auto py-4 sm:py-6">
          {/* Threads Table/Cards */}
          <div className="bg-white rounded-lg border border-[#E5E7EB] shadow-sm overflow-hidden">
            {/* Mobile View - Card Layout */}
            <div className="block sm:hidden">
              {currentThreads?.map((thread: ChatThread) => (
                <div key={thread?.uid} className="p-4 border-b border-[#E5E7EB] hover:bg-gray-50">
                  <div className="space-y-3">
                    <div>
                      <div className="text-xs text-gray-500 uppercase font-medium">Thread ID</div>
                      <div className="text-sm font-medium text-[#05073C] mt-1 break-all">{thread?.uid}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500 uppercase font-medium">Thread Title</div>
                      <div className="text-sm text-[#6B7280] mt-1">{thread?.thread_name}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500 uppercase font-medium">Created Date</div>
                      <div className="text-sm text-[#6B7280] mt-1">{thread?.created_at?.split("T")[0]}</div>
                    </div>
                    <button
                      onClick={() => handleViewHistory(thread?.uid)}
                      className="mt-2 w-full text-center py-2 px-4 text-sm text-blue-600 hover:text-blue-700 border border-blue-200 rounded-md hover:bg-blue-50 transition-colors"
                    >
                      View History
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Desktop/Tablet View - Table Layout */}
            <div className="hidden sm:block overflow-x-auto ">
              <div className="min-w-full inline-block align-middle">
                <table className="min-w-full divide-y divide-[#E5E7EB]">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thread ID</th>
                      <th className="px-4 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thread Title</th>
                      <th className="px-4 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created Date</th>
                      <th className="px-4 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">History</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-[#E5E7EB]">
                    {currentThreads?.map((thread: ChatThread) => (
                      <tr key={thread?.uid} className="hover:bg-gray-50">
                        <td className="px-4 md:px-6 py-4">
                          <div className="text-sm font-medium text-[#05073C] break-all">{thread?.uid}</div>
                        </td>
                        <td className="px-4 md:px-6 py-4">
                          <div className="text-sm text-[#6B7280] line-clamp-2">{thread?.thread_name}</div>
                        </td>
                        <td className="px-4 md:px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-[#6B7280]">{thread?.created_at?.split("T")[0]}</div>
                        </td>
                        <td className="px-4 md:px-6 py-4 whitespace-nowrap">
                          <button
                            onClick={() => handleViewHistory(thread?.uid)}
                            className="text-sm text-blue-600 hover:text-blue-700 hover:underline"
                          >
                            View History
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Pagination Controls - Responsive */}
          {userThreadsData?.length > 0 && (
            <div className="mt-4 sm:mt-6 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
              <div className="text-sm text-gray-700 text-center sm:text-left">
                Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(indexOfLastItem, totalThreads)}
                </span>{' '}
                of <span className="font-medium">{totalThreads}</span> results
              </div>
              <div className="flex justify-center space-x-1 overflow-x-auto max-w-full pb-2 sm:pb-0">
                {renderPaginationButtons()}
              </div>
            </div>
          )}

          {/* Empty State - Responsive */}
          {userThreadsData?.length === 0 && (
            <div className="text-center py-8 sm:py-12">
              <svg className="mx-auto h-10 w-10 sm:h-12 sm:w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No chat threads found</h3>
              <p className="mt-1 text-sm text-gray-500 px-4">This user hasn't started any conversations yet.</p>
            </div>
          )}
        </div>}
    </>
  );
};

export default AdminChatThreads;