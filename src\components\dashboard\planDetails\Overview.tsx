import DisclaimerIcon from "../../../assets/disclaimer-icon.svg";
import { useAppSelector } from "../../../store/hooks";

const Overview = () => {
     const { tourDetails } = useAppSelector((state) => state.tours);

     // Transform the highlights into a list
     const formatHighlights = (highlights: string) => {
          if (!highlights) return [];
          // Split by ✔️ and filter out empty entries
          return highlights.split('✔️').filter(item => item.trim().length > 0)
               .map(item => item.trim());
     };

     const highlightsList = formatHighlights(tourDetails?.additional_information);

     return (
          <div className="py-[20px] lg:py-[30px]">
               <h2 className="text-[18px] sm:text-[24px] md:text-[30px] md:leading-[40px] leading-[24px] sm:leading-[30px] font-bold mb-[6px]">
                    Tour Overview
               </h2>
               <div className="mb-[24px]">
                    <div
                         className="tour-description"
                         dangerouslySetInnerHTML={{
                              __html: tourDetails?.tour_description || ''
                         }}
                    />
               </div>

               <h3 className="text-[14px] sm:text-[18px] leading-[24px] font-medium text-[#05073C] mb-[14px]">
                    Tour Highlights
               </h3>

               {highlightsList.length > 0 ? (
                    <div className="tour-highlights">
                         <ul className="sm:space-y-1 list-disc ps-[20px]">
                              {highlightsList.map((highlight, index) => (
                                   <p key={`highlight-${index}`} className="text-[#4B5563] text-[14px] leading-[20px]" dangerouslySetInnerHTML={{ __html: highlight }}></p>
                              ))}
                         </ul>
                    </div>
               ) : (
                    <div>{tourDetails?.additional_information}</div>
               )}

               {tourDetails?.booking_url && <div className="mt-[15px] md:mt-[30px] p-[10px] md:p-[12px] bg-[#F4F4F6] rounded-[8px] flex items-start gap-[14px]">
                    <div className="bg-[#FAB4001A] rounded-[5px] h-[30px] w-full max-w-[30px] flex items-center justify-center">
                         <img
                              src={DisclaimerIcon}
                              alt="Disclaimer"
                              className=""
                         />
                    </div>
                    <p className="text-[#000000BD] text-[12px] sm:text-[14px] md:text-[16px] sm:leading-[25px] leading-[20px]">
                         This package data was collected from publicly available sources such as websites or social media.
                         Please verify all details directly with the agency before booking. We're actively working to onboard more trusted partners to ensure a safer, smoother exprience for you.
                    </p>
               </div>}

               {!tourDetails?.booking_url && <div className="mt-[15px] md:mt-[30px] p-[10px] md:p-[12px] bg-[#F4F4F6] rounded-[8px] flex items-start gap-[14px]">
                    <div className="bg-[#FAB4001A] rounded-[5px] h-[30px] w-full max-w-[30px] flex items-center justify-center">
                         <img
                              src={DisclaimerIcon}
                              alt="Disclaimer"
                              className=""
                         />
                    </div>
                    <p className="text-[#000000BD] text-[12px] sm:text-[14px] md:text-[16px] sm:leading-[25px] leading-[20px]">
                         Package Price and Itinerary may change based on
                         availability from our travel partner, but we will
                         always ensure you get the best travel deals!
                    </p>
               </div>}
          </div>
     )
}

export default Overview;
