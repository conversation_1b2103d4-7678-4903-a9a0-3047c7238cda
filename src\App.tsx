import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './pages/auth/Login';
import Register from './pages/auth/Register';
import ForgotPassword from './pages/auth/ForgotPassword';
import OTP from './pages/auth/OtpVerification';
import Chat from './pages/chat/Chat';
import PlanDetails from './pages/planDetails/PlanDetails';
import ConfirmPassword from './pages/auth/ConfirmPassword';
import Bookings from './pages/bookings/Bookings';
import Payment from './pages/payments/Payments';
import ContactUs from './pages/contact/ContactUs';
import Layout from './Layout';
import ChangePasswordAdmin from './pages/changePassword/AdminChangePassword';
import Terms from './pages/legal/Terms';
import PrivacyPolicy from './pages/legal/PrivacyPolicy';
import ToursDetails from './pages/tourDetails/ToursDetails';
import PaymentGateway from './pages/payments/PaymentGateway';
import PaymentSuccess from './pages/payments/PaymentSuccess';
import ThankYou from './pages/payments/ThankYou';
import PaymentFailed from './pages/payments/PaymentFailed';
import AdminLayout from './components/admin/AdminLayout';
import AdminDashboard from './pages/admin/AdminDashboard';
import AdminUsers from './pages/admin/AdminUsers';
import AdminChatThreads from './pages/admin/AdminChatThreads';
import AdminChatLogs from './pages/admin/AdminChatLogs';
import AdminAnalytics from './pages/admin/AdminAnalytics';
import AdminPackages from './pages/admin/AdminPackages';
import AdminBookings from './pages/admin/AdminBookings';
import AdminContactDetails from './pages/admin/AdminContactDetails';
import AdminErrorLogs from './pages/admin/AdminErrorLogs';
import AdminLogin from './pages/admin/AdminLogin';
import Cookies from 'js-cookie';
import BankDetails from './pages/payments/BankDetails';

function App() {
  const isAdmin = import.meta.env.VITE_APP_TYPE;
  const isAuthenticated = Cookies.get("accessToken");
  return (
    <>
      <Router>
        <Routes>
          {isAdmin === 'user' && <>
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/confirm-password" element={<ConfirmPassword />} />
            <Route path="/otp-verification" element={<OTP />} />
            <Route path="/" element={<Layout><Chat /></Layout>} />
            <Route path="/chat/:threadUid" element={<Layout><Chat /></Layout>} />
            <Route path="/plan-details/:threadUid/:tour_id" element={<Layout><PlanDetails /></Layout>} />
            <Route path="/bookings" element={<Layout><Bookings /></Layout>} />
            <Route path="/payments" element={<Layout><Payment /></Layout>} />
            <Route path="/contact" element={<Layout><ContactUs /></Layout>} />
            <Route path="/change-password" element={<Layout><ChangePasswordAdmin /></Layout>} />
            <Route path="/terms" element={<Layout><Terms /></Layout>} />
            <Route path="/privacy-policy" element={<Layout><PrivacyPolicy /></Layout>} />
            <Route path="/tour-details/:threadUid" element={<Layout><ToursDetails /></Layout>} />
            <Route path="/payment-gateway/:orderId" element={<Layout><PaymentGateway /></Layout>} />
            <Route path="/payment-success/:orderId" element={<Layout><PaymentSuccess /></Layout>} />
            <Route path="/payment-failed" element={<Layout><PaymentFailed /></Layout>} />
            <Route path="/thank-you" element={<Layout><ThankYou /></Layout>} />
            <Route path="/bank-details/:orderId" element={<Layout><BankDetails /></Layout>} />
          </>
          }
          {isAdmin === 'admin' &&
            <>
              <Route path="/" element={<Navigate to="/admin/login" replace />} />
              <Route path="/admin/login" element={<AdminLogin />} />
              <Route path="/admin/dashboard" element={isAuthenticated ? <AdminLayout title="Admin Dashboard"><AdminDashboard /></AdminLayout> : <Navigate to="/admin/login" replace />} />
              <Route path="/admin/users" element={<AdminLayout title="Admin Users"><AdminUsers /></AdminLayout>} />
              <Route path="/admin/users/:userId/threads" element={<AdminLayout title="Admin Chat Threads" backLink="/admin/users"><AdminChatThreads /></AdminLayout>} />
              <Route path="/admin/users/threads/:threadUid/:userId/logs" element={<AdminChatLogs />} />
              <Route path="/admin/error-logs/threads/:threadUid/logs" element={<AdminChatLogs />} />
              <Route path="/admin/analytics" element={<AdminLayout title="Admin Analytics"><AdminAnalytics /></AdminLayout>} />
              <Route path="/admin/packages" element={<AdminLayout title="Packages"><AdminPackages /></AdminLayout>} />
              <Route path="/admin/bookings" element={<AdminLayout title="Bookings"><AdminBookings /></AdminLayout>} />
              <Route path="/admin/contact-details" element={<AdminLayout title="Contact Details"><AdminContactDetails /></AdminLayout>} />
              <Route path="/admin/error-logs" element={<AdminLayout title="Error Logs"><AdminErrorLogs /></AdminLayout>} />
            </>
          }
        </Routes >
      </Router >
    </>
  );
}

export default App;
