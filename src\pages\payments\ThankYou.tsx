import { Link } from 'react-router-dom';

const ThankYou = () => {
  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="md:ms-[275px] h-screen overflow-y-auto flex-1 flex flex-col items-center justify-center p-6">
        <div className="max-w-lg w-full bg-white shadow-lg rounded-lg p-8 text-center">
          <div className="w-20 h-20 bg-green-100 mx-auto rounded-full flex items-center justify-center mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          
          <h1 className="text-2xl font-bold text-[#05073C] mb-3">Payment Successful!</h1>
          <p className="text-gray-600 mb-6">
            Thank you for your booking. Your transaction has been completed successfully.
          </p>
          
          {/* <div className="bg-[#F9FBFF] border border-[#E7ECF9] rounded-md p-4 mb-6 text-left">
            <p className="text-sm text-gray-500 mb-2">A confirmation email has been sent to your registered email address with all the details.</p>
         
          </div> */}
          
          <div className="flex flex-col sm:flex-row gap-4 w-full">
            <Link 
              to="/bookings" 
              className="flex-1 w-fit px-6 py-3 bg-[#0D3FC6] text-white font-medium rounded-md hover:bg-[#092d94] transition-colors duration-300 text-center"
            >
              View My Bookings
            </Link>
            <Link 
              to="/" 
              className="flex-1 w-fit px-6 py-3 border border-[#0D3FC6] text-[#0D3FC6] font-medium rounded-md hover:bg-[#E7ECF9] transition-colors duration-300 text-center"
            >
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThankYou;