import { useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { addMessage, threadUid as setThreadUid, setLoading } from "../../../store/features/chat/chatSlice";
import { useSendMessageMutation } from "../../../store/features/chat/chatApi";
import { RootState } from "../../../store/store";

type PersonalInfoFormData = {
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    address: string;
};

const PersonalInformation = () => {
    const { register, handleSubmit, formState: { errors } } = useForm<PersonalInfoFormData>();
    const dispatch = useDispatch();
    const [sendMessage] = useSendMessageMutation();
    const threadUid = useSelector((state: RootState) => state.chat.threadUid);

    const onSubmit = async (data: PersonalInfoFormData) => {
        // Create the message text
        const messageText = "Here's my personal information";

        // Add user message to the chat
        dispatch(addMessage({
            content: messageText,
            sender: "user"
        }));

        // Set loading state to true to show the chat loader
        dispatch(setLoading(true));

        // Create the API payload in the exact format requested
        const messagePayload = {
            message: messageText,
            thread_uid: threadUid || "",
            context_data: {
                intent: "booking",
                action: "contact_info",
                data: {
                    first_name: data.first_name,
                    last_name: data.last_name,
                    email: data.email,
                    phone: data.phone,
                    address: data.address
                }
            }
        };

        try {
            // Send the message to the API
            const response = await sendMessage(messagePayload).unwrap();

            // Get thread UID from the response
            const responseThreadUid = response?.data?.thread_uid || threadUid;

            // Update the thread UID in the Redux store
            if (responseThreadUid) {
                dispatch(setThreadUid(responseThreadUid));
            }

            // Process AI response
            let aiResponseMessage = "";
            let responseType = "text";

            if (response.data?.ai_response?.type) {
                responseType = response.data.ai_response.type;
            }

            if (response.data?.ai_response?.data?.text_response?.message) {
                aiResponseMessage = response.data.ai_response.data.text_response.message;
            }
            else if (response.data?.ai_response?.text_response?.message) {
                aiResponseMessage = response.data.ai_response.text_response.message;
            }
            else if (response.data?.ai_response?.response?.message) {
                aiResponseMessage = response.data.ai_response.response.message;
            }

            // Add AI response to chat
            if (aiResponseMessage) {
                dispatch(addMessage({
                    content: aiResponseMessage,
                    sender: "ai",
                    responseType: responseType
                }));
            } else {
                dispatch(addMessage({
                    content: "Thank you for providing your personal information. How else can I assist you?",
                    sender: "ai"
                }));
            }
        } catch (error) {
            console.error("Error sending personal information:", error);

            // Add error message to chat
            dispatch(addMessage({
                content: "Sorry, I couldn't process your information at the moment. Please try again later.",
                sender: "ai"
            }));
        } finally {
            // Set loading state back to false
            dispatch(setLoading(false));
        }
    };

    // Add this validation function
    const validateNotOnlyWhitespace = (value: string) => {
        return value.trim().length > 0 || "This field cannot contain only spaces";
    };

    return (
        <div className="max-w-[740px]">
            <div className="ms-[45px] max-w-[475px] bg-[#F4F4F6] rounded-[24px] py-[20px] px-[18px] border border-[#E5E7EB]">
                <h3 className="mb-[15px] sm:mb-[23px] text-[16px] sm:text-[20px] leading-[20px] sm:leading-[24px] font-semibold text-[#05073C]">Enter your personal information</h3>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <div className="mb-[10px] sm:mb-[15px]">
                        <label className="block text-[14px] font-semibold text-[#636C76] mb-1">First Name</label>
                        <input
                            type="text"
                            className={`rounded-[30px] border ${errors.first_name ? 'border-red-500' : 'border-[#D5D5D8]'} bg-[#FFFFFF] py-[13px] px-[18px] text-[14px] leading-[18px] text-[#05073C] w-full placeholder:text-[#989EAE] focus:outline-[#0D3FC6]`}
                            placeholder="Enter your first name"
                            {...register("first_name", {
                                required: "First name is required",
                                validate: {
                                    notWhitespace: validateNotOnlyWhitespace,
                                },
                                minLength: {
                                    value: 3,
                                    message: "First name must be at least 3 characters"
                                },
                                maxLength: {
                                    value: 50,
                                    message: "First name must not exceed 50 characters"
                                },
                                pattern: {
                                    value: /^[A-Za-z\s]+$/,
                                    message: "First name can only contain letters and spaces"
                                }
                            })}
                        />
                        {errors.first_name && <span className="text-red-500 text-xs mt-1">{errors.first_name.message}</span>}
                    </div>

                    <div className="mb-[10px] sm:mb-[15px]">
                        <label className="block text-[14px] font-semibold text-[#636C76] mb-1">Last Name</label>
                        <input
                            type="text"
                            className={`rounded-[30px] border ${errors.last_name ? 'border-red-500' : 'border-[#D5D5D8]'} bg-[#FFFFFF] py-[13px] px-[18px] text-[14px] leading-[18px] text-[#05073C] w-full placeholder:text-[#989EAE] focus:outline-[#0D3FC6]`}
                            placeholder="Enter your last name"
                            {...register("last_name", {
                                required: "Last name is required",
                                validate: {
                                    notWhitespace: validateNotOnlyWhitespace,
                                },
                                minLength: {
                                    value: 3,
                                    message: "Last name must be at least 3 characters"
                                },
                                maxLength: {
                                    value: 50,
                                    message: "Last name must not exceed 50 characters"
                                },
                                pattern: {
                                    value: /^[A-Za-z\s]+$/,
                                    message: "Last name can only contain letters and spaces"
                                }
                            })}
                        />
                        {errors.last_name && <span className="text-red-500 text-xs mt-1">{errors.last_name.message}</span>}
                    </div>

                    <div className="mb-[10px] sm:mb-[15px]">
                        <label className="block text-[14px] font-semibold text-[#636C76] mb-1">Email</label>
                        <input
                            type="email"
                            className={`rounded-[30px] border ${errors.email ? 'border-red-500' : 'border-[#D5D5D8]'} bg-[#FFFFFF] py-[13px] px-[18px] text-[14px] leading-[18px] text-[#05073C] w-full placeholder:text-[#989EAE] focus:outline-[#0D3FC6]`}
                            placeholder="Enter your email"
                            {...register("email", {
                                required: "Email is required",
                                pattern: {
                                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                                    message: "Please enter a valid email address"
                                },
                                maxLength: {
                                    value: 100,
                                    message: "Email must not exceed 100 characters"
                                }
                            })}
                        />
                        {errors.email && <span className="text-red-500 text-xs mt-1">{errors.email.message}</span>}
                    </div>

                    <div className="mb-[10px] sm:mb-[15px]">
                        <label className="block text-[14px] font-semibold text-[#636C76] mb-1">Phone Number</label>
                        <input
                            type="tel"
                            className={`rounded-[30px] border ${errors.phone ? 'border-red-500' : 'border-[#D5D5D8]'} bg-[#FFFFFF] py-[13px] px-[18px] text-[14px] leading-[18px] text-[#05073C] w-full placeholder:text-[#989EAE] focus:outline-[#0D3FC6]`}
                            placeholder="Enter your phone number"
                            {...register("phone", {
                                required: "Phone number is required",
                                pattern: {
                                    value: /^[+]?[0-9\s-()]{10,15}$/,
                                    message: "Please enter a valid phone number"
                                },
                                minLength: {
                                    value: 10,
                                    message: "Phone number must be at least 10 digits"
                                },
                                maxLength: {
                                    value: 15,
                                    message: "Phone number must not exceed 15 digits"
                                }
                            })}
                        />
                        {errors.phone && <span className="text-red-500 text-xs mt-1">{errors.phone.message}</span>}
                    </div>

                    <div className="mb-[15px] sm:mb-[20px]">
                        <label className="block text-[14px] font-semibold text-[#636C76] mb-1">Address</label>
                        <textarea
                            className={`rounded-[20px] border ${errors.address ? 'border-red-500' : 'border-[#D5D5D8]'} bg-[#FFFFFF] py-[13px] px-[18px] text-[14px] leading-[18px] text-[#05073C] w-full placeholder:text-[#989EAE] focus:outline-[#0D3FC6] min-h-[100px]`}
                            placeholder="Enter your address"
                            {...register("address", {
                                required: "Address is required",
                                validate: {
                                    notWhitespace: validateNotOnlyWhitespace,
                                },
                                minLength: {
                                    value: 10,
                                    message: "Address must be at least 10 characters"
                                },
                                maxLength: {
                                    value: 200,
                                    message: "Address must not exceed 200 characters"
                                }
                            })}
                        />
                        {errors.address && <span className="text-red-500 text-xs mt-1">{errors.address.message}</span>}
                    </div>

                    <div className="text-center">
                        <button
                            type="submit"
                            className="w-full bg-[#0D3FC6] text-white py-[16px] rounded-[30px] font-normal hover:bg-[#1A339B] transition-colors cursor-pointer text-[14px] leading-[18px] uppercase"
                        >
                            Continue
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default PersonalInformation;
