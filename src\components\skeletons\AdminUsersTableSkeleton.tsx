const AdminUsersTableSkeleton = () => {
     return (
          <div className="max-w-[1240px] sm:px-[30px] px-[10px] mx-auto py-4 sm:py-6">
               {/* Search Skeleton */}
               <div className="mb-4 sm:mb-6">
                    <div className="relative w-full sm:max-w-md">
                         <div className="h-10 bg-gray-200 rounded-lg animate-pulse"></div>
                    </div>
               </div>

               {/* Desktop Table Skeleton */}
               <div className="hidden lg:block bg-white rounded-lg border border-[#E5E7EB] shadow-sm overflow-hidden">
                    <div className="overflow-x-auto">
                         <table className="min-w-full divide-y divide-[#E5E7EB]">
                              <thead className="bg-gray-50">
                                   <tr>
                                        <th className="px-6 py-3 text-left">
                                             <div className="h-3 bg-gray-300 rounded w-12 animate-pulse"></div>
                                        </th>
                                        <th className="px-6 py-3 text-left">
                                             <div className="h-3 bg-gray-300 rounded w-16 animate-pulse"></div>
                                        </th>
                                        <th className="px-6 py-3 text-left">
                                             <div className="h-3 bg-gray-300 rounded w-20 animate-pulse"></div>
                                        </th>
                                        <th className="px-6 py-3 text-left">
                                             <div className="h-3 bg-gray-300 rounded w-16 animate-pulse"></div>
                                        </th>
                                        <th className="px-6 py-3 text-left">
                                             <div className="h-3 bg-gray-300 rounded w-16 animate-pulse"></div>
                                        </th>
                                   </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-[#E5E7EB]">
                                   {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                                        <tr key={i} className="hover:bg-gray-50">
                                             <td className="px-6 py-4 whitespace-nowrap">
                                                  <div className="flex items-center">
                                                       <div className="w-10 h-10 rounded-full bg-gray-200 animate-pulse"></div>
                                                       <div className="ml-4">
                                                            <div className="h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"></div>
                                                            <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
                                                       </div>
                                                  </div>
                                             </td>
                                             <td className="px-6 py-4 whitespace-nowrap">
                                                  <div className="h-4 bg-gray-200 rounded w-32 mb-1 animate-pulse"></div>
                                                  <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                                             </td>
                                             <td className="px-6 py-4 whitespace-nowrap">
                                                  <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
                                             </td>
                                             <td className="px-6 py-4 whitespace-nowrap">
                                                  <div className="h-6 bg-gray-200 rounded-full w-20 animate-pulse"></div>
                                             </td>
                                             <td className="px-6 py-4 whitespace-nowrap">
                                                  <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                                             </td>
                                        </tr>
                                   ))}
                              </tbody>
                         </table>
                    </div>
               </div>

               {/* Mobile Card Skeleton */}
               <div className="lg:hidden space-y-4">
                    {[1, 2, 3, 4, 5].map((i) => (
                         <div key={i} className="bg-white rounded-lg border border-[#E5E7EB] shadow-sm p-4">
                              {/* User Header Skeleton */}
                              <div className="flex items-center justify-between mb-3">
                                   <div className="flex items-center">
                                        <div className="w-10 h-10 rounded-full bg-gray-200 animate-pulse"></div>
                                        <div className="ml-3">
                                             <div className="h-4 bg-gray-200 rounded w-20 mb-1 animate-pulse"></div>
                                             <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                                        </div>
                                   </div>
                                   <div className="h-6 bg-gray-200 rounded-full w-16 animate-pulse"></div>
                              </div>

                              {/* User Details Skeleton */}
                              <div className="space-y-2 mb-3">
                                   {[1, 2, 3, 4].map((j) => (
                                        <div key={j} className="flex flex-col sm:flex-row sm:justify-between">
                                             <div className="h-3 bg-gray-200 rounded w-12 mb-1 sm:mb-0 animate-pulse"></div>
                                             <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                                        </div>
                                   ))}
                              </div>

                              {/* Action Button Skeleton */}
                              <div className="pt-3 border-t border-[#E5E7EB]">
                                   <div className="h-10 bg-gray-200 rounded-lg animate-pulse"></div>
                              </div>
                         </div>
                    ))}
               </div>
          </div>
     );
};

export default AdminUsersTableSkeleton;