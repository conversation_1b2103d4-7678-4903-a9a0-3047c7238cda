import { useState } from "react";
import { usePackagesDashboardQuery } from "../../store/features/admin/adminApi";
import TourClicksTable from "../../components/admin/TourClicksTable";
import ExternalClicksTours from "../../components/admin/ExternalClicksTours";
import Pagination from "../../components/admin/ClicksPagination";
import AdminPackagesSkeleton from "../../components/skeletons/AdminPackagesSkeleton";

const ITEMS_PER_PAGE = 10;

const AdminPackages = () => {
  const { data: packagesData, isLoading } = usePackagesDashboardQuery({});
  const externalClicksTours = packagesData?.external_click_count_by_tour;
  const tourClicksTours = packagesData?.tour_click_count_by_tour;
  const [activeTab, setActiveTab] = useState<'tour' | 'external'>('tour');
  const [currentPage, setCurrentPage] = useState(1);

  const currentData = activeTab === 'tour' ? tourClicksTours : externalClicksTours;
  const totalPages = Math.ceil((currentData?.length || 0) / ITEMS_PER_PAGE);
  const paginatedData = currentData?.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <>
      {/* Tab Navigation */}
      <div className="mb-6">
        {/* Mobile Dropdown */}
        <div className="sm:hidden">
          <select
            className="block w-full rounded-lg border-gray-300 py-2 pl-3 pr-10 text-base focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            value={activeTab}
            onChange={(e) => {
              setActiveTab(e.target.value as 'tour' | 'external');
              setCurrentPage(1);
            }}
          >
            <option value="tour">Tour Page Views</option>
            <option value="external">External Referrals</option>
          </select>
        </div>

        {/* Desktop Tabs */}
        <div className="hidden sm:block">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
              {['tour', 'external'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => {
                    setActiveTab(tab as 'tour' | 'external');
                    setCurrentPage(1);
                  }}
                  className={`${activeTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } whitespace-nowrap pb-3 px-1 border-b-2 font-medium text-sm cursor-pointer`}
                >
                  {tab === 'tour' ? 'Tour Page Views' : 'External Referrals'}
                  <span className={`ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium ${activeTab === tab ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                    }`}>
                    {tab === 'tour' ? tourClicksTours?.length : externalClicksTours?.length}
                  </span>
                </button>
              ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Table Container */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          {isLoading ? <AdminPackagesSkeleton /> :
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Package Details
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                    Location
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Clicks
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {activeTab === 'tour' ? (
                  <TourClicksTable tourClicksTours={paginatedData} />
                ) : (
                  <ExternalClicksTours externalClicksTours={paginatedData} />
                )}
              </tbody>
            </table>}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        )}
      </div>
    </>
  );
};

export default AdminPackages;