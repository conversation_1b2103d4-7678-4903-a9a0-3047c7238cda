import React, { useEffect, useState } from "react";
import LoaderDark from "../common/LoaderDark";
import { useNavigate, useLocation } from "react-router-dom";
import AdminSidebar from "./AdminSidebar";
import { useGetUserQuery } from "../../store/features/auth/authApi";
import { toast } from "react-hot-toast";
import Cookies from "js-cookie";

const AdminLayout = ({ children, title, backLink }: { children: React.ReactNode, title: string, backLink?: string }) => {
     const { data: userDetails, isLoading } = useGetUserQuery({});
     const [loading, setLoading] = useState(true);
     const navigate = useNavigate();
     const location = useLocation()

     useEffect(() => {
          if (!isLoading) {
               if (userDetails) {
                    if (userDetails?.role === "user") {
                         Cookies.remove("accessToken");
                         toast.error("You are not authorized to access the admin dashboard");
                         navigate("/admin/login");
                    }
                    if (userDetails?.role === "staff") {
                         if (location?.pathname === "/admin/dashboard" || location?.pathname === "/admin/bookings" || location?.pathname === "/admin/contact-details") {
                              setLoading(false);
                         } else {
                              toast.error("You are not authorized to access this page");
                              navigate("/admin/dashboard");
                         }
                    }

                    if (userDetails?.role === "superuser") {
                         setLoading(false);
                    }
               } else {
                    toast.error("Please login to continue");
                    navigate("/admin/login");
                    window.location.reload();

               }
          }
     }, [userDetails, navigate, location?.pathname, isLoading]);

     return (
          <div className="flex">
               <AdminSidebar />
               {loading ? (
                    <div className="w-full h-screen flex items-center justify-center">
                         <LoaderDark />
                    </div>
               ) : (
                    <div className="w-full min-h-screen md:ml-[280px] transition-all duration-300">
                         <div className={`
                              fixed top-0 right-0 z-[9] 
                              w-full md:w-[calc(100%-280px)]
                              bg-white py-[15px]
                              ${backLink ? "sm:py-[8.4px]" : "sm:py-[12.4px]"}
                              px-4 sm:px-[30px]
                              border-b border-[#E5E7EB]
                              shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)]
                         `}>
                              <div className="flex items-center justify-between max-w-[1240px] mx-auto">
                                   <h1 className="text-[18px] pl-13 sm:pl-0 sm:text-[24px] leading-[32px] font-bold text-[#05073C]">
                                        {title}
                                   </h1>
                                   {backLink && (
                                        <button
                                             type="button"
                                             onClick={() => navigate(backLink)}
                                             className="flex items-center gap-2 py-2 px-4 bg-[#EEF1FB] rounded-lg text-[#05073C] font-medium text-sm sm:text-base"
                                        >
                                             <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                  <path d="M19 12H5M5 12L12 19M5 12L12 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                             </svg>
                                             <span className="hidden sm:inline">Back</span>
                                        </button>
                                   )}
                              </div>
                         </div>
                         <div className=" mt-[50px] smmt-[40px] max-w-[1240px] sm:px-[30px] px-[10px] mx-auto py-6">
                              {children}
                         </div>
                    </div>
               )}
          </div>
     );
};

export default AdminLayout;