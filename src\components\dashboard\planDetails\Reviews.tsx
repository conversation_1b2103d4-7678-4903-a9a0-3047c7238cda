import RatingIcon from "../../../assets/rating-star.svg";
// import EmptyStarIcon from "../../../assets/rating-star-empty.svg";
import Comment from "./Comment";
import { useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../store/store";

const Reviews = () => {
     const reviews = useSelector((state: RootState) => state.tours.reviews);
     const [visibleReviews, setVisibleReviews] = useState(2);
     const [modalMedia, setModalMedia] = useState<{ type: "image" | "video"; src: string } | null>(null);

     const calculateStarPercentage = (star: string) => {
          if (!reviews?.overall_rating?.distribution) return 0;

          const totalReviews = reviews.total_reviews || 1;
          const starCount = reviews.overall_rating.distribution[star] || 0;
          return (starCount / totalReviews) * 100;
     };

     const calculateCategoryPercentage = (rating: number) => {
          return (rating / 5) * 100;
     };

     const handleSeeMoreReviews = () => {
          setVisibleReviews(reviews?.reviews?.length || 0);
     };

     const hasMoreReviews = reviews?.reviews?.length > visibleReviews;

     const renderStars = (rating: number) => {
          const stars = [];
          const maxRating = 5;

          for (let i = 0; i < Math.min(rating, maxRating); i++) {
               stars.push(<img key={`filled-${i}`} src={RatingIcon} alt="Filled Star" />);
          }

          for (let i = rating; i < maxRating; i++) {
               stars.push(<img key={`empty-${i}`} src={RatingIcon} alt="Empty Star" className="opacity-30" />);
          }

          return stars;
     };

     return (
          <div className="py-[30px]">
               <h2 className="text-[18px] sm:text-[24px] md:text-[30px] md:leading-[40px] leading-[24px] sm:leading-[30px] font-bold mb-[6px]">
                    Customer Reviews
               </h2>
               <div className="bg-[#F9FBFF] border border-[#E7ECF9] rounded-[15px] py-[10px] sm:py-[20px] px-[12px] mt-[14px] max-w-[650px]">
                    <h4 className="text-[18px] sm:text-[24px] sm:leading-[45px] leading-[20px] font-bold text-[#05073C] mb-[10px] flex items-center gap-4">
                         <span>
                              <span className="text-gray-500 text-sm font-normal mr-1">Overall Rating:</span>
                              {reviews?.overall_rating?.average}
                         </span>
                         <span className="text-gray-300">|</span>
                         <span>
                              <span className="text-gray-500 font-normal mr-1.5 text-sm">Total Reviews:</span>
                              {reviews?.total_reviews}
                         </span>
                    </h4>
                    <div className="flex items-start gap-[10px] justify-between flex-wrap">
                         <div>
                              <h6 className="text-[14px] sm:text-[16px] sm:leading-[24px] leading-[20px] font-semibold text-[#05073C] mb-[5px]">
                                   Overall Ratings
                              </h6>
                              <div className="flex items-center gap-[10px] mb-[10px]">
                                   <span className="text-[12px] sm:text-[14px] leading-[20px] font-semibod text-[#05073C]">
                                        5
                                   </span>
                                   <div className="w-[104px] h-[4px] bg-[#D3D3D3] rounded-full mr-2">
                                        <div
                                             className="h-[4px] bg-[#1F2637] rounded-full"
                                             style={{ width: `${calculateStarPercentage("5")}%` }}
                                        ></div>
                                   </div>
                              </div>
                              <div className="flex items-center gap-[10px] mb-[10px]">
                                   <span className="text-[12px] sm:text-[14px] leading-[20px] font-semibod text-[#05073C]">
                                        4
                                   </span>
                                   <div className="w-[104px] h-[4px] bg-[#D3D3D3] rounded-full mr-2">
                                        <div
                                             className="h-[4px] bg-[#1F2637] rounded-full"
                                             style={{ width: `${calculateStarPercentage("4")}%` }}
                                        ></div>
                                   </div>
                              </div>

                              <div className="flex items-center gap-[10px] mb-[10px]">
                                   <span className="text-[12px] sm:text-[14px] leading-[20px] font-semibod text-[#05073C]">
                                        3
                                   </span>
                                   <div className="w-[104px] h-[4px] bg-[#D3D3D3] rounded-full mr-2">
                                        <div
                                             className="h-[4px] bg-[#1F2637] rounded-full"
                                             style={{ width: `${calculateStarPercentage("3")}%` }}
                                        ></div>
                                   </div>
                              </div>
                              <div className="flex items-center gap-[10px] mb-[10px]">
                                   <span className="text-[12px] sm:text-[14px] leading-[20px] font-semibod text-[#05073C]">
                                        2
                                   </span>
                                   <div className="w-[104px] h-[4px] bg-[#D3D3D3] rounded-full mr-2">
                                        <div
                                             className="h-[4px] bg-[#1F2637] rounded-full"
                                             style={{ width: `${calculateStarPercentage("2")}%` }}
                                        ></div>
                                   </div>
                              </div>
                              <div className="flex items-center gap-[10px] mb-[10px]">
                                   <span className="text-[12px] sm:text-[14px] leading-[20px] font-semibod text-[#05073C]">
                                        1
                                   </span>
                                   <div className="w-[104px] h-[4px] bg-[#D3D3D3] rounded-full mr-2">
                                        <div
                                             className="h-[4px] bg-[#1F2637] rounded-full"
                                             style={{ width: `${calculateStarPercentage("1")}%` }}
                                        ></div>
                                   </div>
                              </div>
                         </div>
                         <div>
                              <div className="flex items-center gap-[10px] mb-[8px]">
                                   <div className="w-[160px] text-[12px] sm:text-[14px] leading-[20px] font-normal text-[#05073C]">
                                        Accomodation
                                   </div>
                                   <div className="flex items-center gap-[10px]">
                                        <div className="w-[104px] h-[4px] bg-[#D3D3D3] rounded-full mr-2">
                                             <div
                                                  className="h-[4px] bg-[#1F2637] rounded-full"
                                                  style={{ width: `${calculateCategoryPercentage(reviews?.average_fields?.accommodation || 0)}%` }}
                                             ></div>
                                        </div>
                                        <span>{reviews?.average_fields?.accommodation}</span>
                                   </div>
                              </div>

                              <div className="flex items-center gap-[10px] mb-[8px]">
                                   <div className="w-[160px] text-[12px] sm:text-[14px] leading-[20px] font-normal text-[#05073C]">
                                        Transportation
                                   </div>
                                   <div className="flex items-center gap-[10px]">
                                        <div className="w-[104px] h-[4px] bg-[#D3D3D3] rounded-full mr-2">
                                             <div
                                                  className="h-[4px] bg-[#1F2637] rounded-full"
                                                  style={{ width: `${calculateCategoryPercentage(reviews?.average_fields?.transportation || 0)}%` }}
                                             ></div>
                                        </div>
                                        <span>{reviews?.average_fields?.transportation}</span>
                                   </div>
                              </div>

                              <div className="flex items-center gap-[10px] mb-[8px]">
                                   <div className="w-[160px] text-[12px] sm:text-[14px] leading-[20px] font-normal text-[#05073C]">
                                        Activities
                                   </div>
                                   <div className="flex items-center gap-[10px]">
                                        <div className="w-[104px] h-[4px] bg-[#D3D3D3] rounded-full mr-2">
                                             <div
                                                  className="h-[4px] bg-[#1F2637] rounded-full"
                                                  style={{ width: `${calculateCategoryPercentage(reviews?.average_fields?.activities || 0)}%` }}
                                             ></div>
                                        </div>
                                        <span>{reviews?.average_fields?.activities}</span>
                                   </div>
                              </div>

                              <div className="flex items-center gap-[10px] mb-[8px]">
                                   <div className="w-[160px] text-[12px] sm:text-[14px] leading-[20px] font-normal text-[#05073C]">
                                        Accuracy Of Package
                                   </div>
                                   <div className="flex items-center gap-[10px]">
                                        <div className="w-[104px] h-[4px] bg-[#D3D3D3] rounded-full mr-2">
                                             <div
                                                  className="h-[4px] bg-[#1F2637] rounded-full"
                                                  style={{ width: `${calculateCategoryPercentage(reviews?.average_fields?.accuracy_of_package || 0)}%` }}
                                             ></div>
                                        </div>
                                        <span>{reviews?.average_fields?.accuracy_of_package}</span>
                                   </div>
                              </div>

                              <div className="flex items-center gap-[10px] mb-[8px]">
                                   <div className="w-[160px] text-[12px] sm:text-[14px] leading-[20px] font-normal text-[#05073C]">
                                        Value For Money
                                   </div>
                                   <div className="flex items-center gap-[10px]">
                                        <div className="w-[104px] h-[4px] bg-[#D3D3D3] rounded-full mr-2">
                                             <div
                                                  className="h-[4px] bg-[#1F2637] rounded-full"
                                                  style={{ width: `${calculateCategoryPercentage(reviews?.average_fields?.value_for_money || 0)}%` }}
                                             ></div>
                                        </div>
                                        <span>{reviews?.average_fields?.value_for_money}</span>
                                   </div>
                              </div>

                              <div className="flex items-center gap-[10px] mb-[8px]">
                                   <div className="w-[160px] text-[12px] sm:text-[14px] leading-[20px] font-normal text-[#05073C]">
                                        Safety and Security
                                   </div>
                                   <div className="flex items-center gap-[10px]">
                                        <div className="w-[104px] h-[4px] bg-[#D3D3D3] rounded-full mr-2">
                                             <div
                                                  className="h-[4px] bg-[#1F2637] rounded-full"
                                                  style={{ width: `${calculateCategoryPercentage(reviews?.average_fields?.safety_and_security || 0)}%` }}
                                             ></div>
                                        </div>
                                        <span>{reviews?.average_fields?.safety_and_security}</span>
                                   </div>
                              </div>
                         </div>
                    </div>
               </div>

               <div className="mt-[38px]">
                    {reviews?.reviews?.slice(0, visibleReviews).map((review: {
                         review_title: string,
                         review_description: string,
                         uploaded_media: {
                              images: string[],
                              videos: string[]
                         },
                         user_name: string,
                         created_at: string,
                         tour_guide_rating: number
                    }, index: number) => {
                         const reviewDate = new Date(review.created_at);
                         const formattedDate = reviewDate.toLocaleDateString('en-US', {
                              month: 'long',
                              year: 'numeric'
                         });

                         const initials = review.user_name
                              ? review.user_name.split(' ').map(n => n[0]).join('').toUpperCase()
                              : 'U';

                         return (
                              <div className="flex flex-col mb-[60px]" key={index}>
                                   <div className="flex items-center gap-4 mb-[10px]">
                                        <div className="w-full max-w-[45px] h-[45px] bg-[#212947] rounded-full flex items-center justify-center text-white font-bold">
                                             {initials}
                                        </div>
                                        <div className="flex w-full justify-between">
                                             <span className="font-medium text-[15px] leading-[24px] text-[#05073C]">{review.user_name}</span>
                                             <div className="">
                                                  <span className="text-[#717171] text-[14px] leading-[20px] font-normal">
                                                       {formattedDate}
                                                  </span>
                                             </div>
                                        </div>
                                   </div>

                                   <div className="">
                                        <div className="flex items-center gap-[16px] mb-[8px]">
                                             <div className="flex items-center">
                                                  {renderStars(review.tour_guide_rating || 0)}
                                             </div>
                                             <h4 className="font-bold mb-0 text-[14px] leading-[24px] text-[#05073C] first-letter:capitalize">
                                                  {review?.review_title}
                                             </h4>
                                        </div>
                                        <p className="text-[14px] leading-[28px] font-normal text-[#05073C] mb-[30px]">
                                             {review?.review_description}
                                        </p>
                                   </div>
                                   <div className="flex items-center gap-4">
                                        {review?.uploaded_media?.images?.length > 0 && (
                                             <div className="flex flex-wrap gap-[10px] max-w-[590px]">
                                                  {review?.uploaded_media?.images?.map((image: string, i: number) => (
                                                       <img
                                                            key={i}
                                                            src={`${import.meta.env.VITE_API_URL}${image}`}
                                                            alt="review image"
                                                            className="w-[180px] h-[130px] object-cover rounded-xl cursor-pointer"
                                                            onClick={() => setModalMedia({ type: "image", src: `${import.meta.env.VITE_API_URL}${image}` })}
                                                       />
                                                  ))}
                                             </div>
                                        )}

                                        {review?.uploaded_media?.videos?.length > 0 && (
                                             <div className="flex flex-wrap gap-[10px] max-w-[590px]">
                                                  {review?.uploaded_media?.videos?.map((video: string, i: number) => (
                                                       <video
                                                            key={i}
                                                            src={`${import.meta.env.VITE_API_URL}${video}`}
                                                            className="w-[180px] h-[130px] object-cover rounded-xl cursor-pointer"
                                                            onClick={() => setModalMedia({ type: "video", src: `${import.meta.env.VITE_API_URL}${video}` })}
                                                            controls={false}
                                                       />
                                                  ))}
                                             </div>
                                        )}
                                   </div>
                              </div>
                         );
                    })}
               </div>

               {hasMoreReviews && (
                    <div>
                         <button
                              onClick={handleSeeMoreReviews}
                              className="text-[16px] leading-[20px] font-semibod text-[#0D3FC6] py-[10px] sm:py-[17px] w-full sm:max-w-[210px] max-w-[170px] border border-[#0D3FC6] inline-block text-center rounded-[12px] hover:bg-[#0D3FC6] hover:text-white transition-all duration-300"
                         >
                              See more reviews
                         </button>
                    </div>
               )}

               {/* Modal for image/video preview */}
               {modalMedia && (
                    <div
                         className="fixed inset-0 bg-black/70 bg-opacity-70 flex items-center justify-center z-[999]"
                         onClick={() => setModalMedia(null)}
                    >
                         <div
                              className="relative bg-white rounded-lg p-4"
                              onClick={e => e.stopPropagation()}
                         >
                              <button
                                   className="absolute bg-white p-2 w-10 h-10 text-center shadow-lg cursor-pointer rounded-full -top-5 -right-5 text-black text-2xl font-bold flex items-center justify-center"
                                   onClick={() => setModalMedia(null)}
                              >
                                   &times;
                              </button>
                              {modalMedia.type === "image" ? (
                                   <img src={modalMedia.src} alt="Preview" className="max-w-[90vw] max-h-[80vh] rounded-lg" />
                              ) : (
                                   <video src={modalMedia.src} controls autoPlay className="max-w-[90vw] max-h-[80vh] rounded-lg" />
                              )}
                         </div>
                    </div>
               )}

               <Comment />
          </div>
     )
}

export default Reviews;
