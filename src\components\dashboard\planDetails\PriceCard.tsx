import { useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { addMessage, threadUid as setThreadUid, setLoading } from "../../../store/features/chat/chatSlice";
import { useSendMessageMutation } from "../../../store/features/chat/chatApi";
import { RootState } from "../../../store/store";
import { setPackageName } from "../../../store/features/tours/toursSlice";
import { useExternalTourClickMutation } from "../../../store/features/external-tour-click/tourClickApi";

const PriceCard = ({
     price,
     duration,
     onContactClick,
     tourTitle,
     tourId,
     booking_url,
     groupSize
}: {
     price: number,
     duration: number,
     onContactClick: () => void,
     tourTitle: string,
     tourId: string | undefined,
     booking_url: string | "",
     groupSize: string
}) => {

     const navigate = useNavigate();
     const dispatch = useDispatch();
     const [sendMessage] = useSendMessageMutation();
     const threadUid = useSelector((state: RootState) => state.chat.threadUid);
     const { threadUid: urlThreadUid } = useParams();
     const [externalTourClick] = useExternalTourClickMutation({});

     const handleBookNow = () => {
          dispatch(setPackageName(tourTitle));
          const messageText = `Please book me ${tourTitle}`;
          dispatch(addMessage({
               content: messageText,
               sender: "user"
          }));

          dispatch(setLoading(true));

          // Create the API payload
          const messagePayload = {
               message: messageText,
               thread_uid: threadUid || "",
               context_data: {
                    intent: "booking",
                    action: "book_now",
                    data: {
                         tour_id: tourId || ""
                    }
               }
          };
          navigate(`/chat/${urlThreadUid}`);
          (async () => {
               try {
                    // Send the message to the API
                    const response = await sendMessage(messagePayload).unwrap();

                    // Get thread UID from the response
                    const responseThreadUid = response?.data?.thread_uid || threadUid;

                    // Update the thread UID in the Redux store
                    if (responseThreadUid) {
                         dispatch(setThreadUid(responseThreadUid));
                    }

                    // Process AI response
                    let aiResponseMessage = "";
                    let responseType = "text";

                    if (response.data?.ai_response?.type) {
                         responseType = response.data.ai_response.type;
                    }

                    if (response.data?.ai_response?.data?.text_response?.message) {
                         aiResponseMessage = response.data.ai_response.data.text_response.message;
                    }
                    else if (response.data?.ai_response?.text_response?.message) {
                         aiResponseMessage = response.data.ai_response.text_response.message;
                    }
                    else if (response.data?.ai_response?.response?.message) {
                         aiResponseMessage = response.data.ai_response.response.message;
                    }

                    // Add AI response to chat
                    if (aiResponseMessage) {
                         if (responseType === "booking") {
                              dispatch(addMessage({
                                   content: aiResponseMessage,
                                   sender: "ai",
                                   responseType: responseType,
                              }));
                         } else {
                              dispatch(addMessage({
                                   content: aiResponseMessage || "I'll help you with your booking request.",
                                   sender: "ai",
                                   responseType: responseType
                              }));
                         }
                    } else {
                         dispatch(addMessage({
                              content: "I'll help you with your booking request.",
                              sender: "ai"
                         }));
                    }

               } catch (error) {
                    console.error("Error sending booking message:", error);

                    // Add error message to chat
                    dispatch(addMessage({
                         content: "Sorry, I couldn't process your booking request at the moment. Please try again later.",
                         sender: "ai"
                    }));

                    navigate('/', { replace: true });
               } finally {
                    dispatch(setLoading(false));
               }
          })();
     };

     const handleContactAgent = async () => {
          const response = await externalTourClick({
               tour_id: tourId || "",
          });
          if (response?.data?.status) {
               window.open(booking_url, "_blank");
          }
     }

     return (
          <div className="w-full max-w-[350px]">
               <div className="bg-[#E7ECF9] border border-[#C9D8FF] shadow-[0px_3px_8px_0px_rgba(86,121,215,0.25)] py-[26px] px-[15px] rounded-lg">
                    <div className="mb-[16px] flex items-center justify-between gap-1">
                         <div className="text-[14px] leading-[24px] font-normal text-[#05073C]">
                              Price starts from
                         </div>
                         <div className="flex items-baseline">
                              <span className="text-[18px]  sm:text-[22px] leading-[32px] font-bold text-[#0D3FC6]">
                                   ৳ {Number(price).toLocaleString() || 0}
                              </span>
                              <span className="text-[14px] sm:text-[16px] leading-[24px] font-semibold text-[#05073C] ml-1">
                                   / person
                              </span>
                         </div>
                    </div>

                    {groupSize && <div className="mb-[16px] flex items-center justify-between gap-1">
                         <div className="text-[14px] leading-[20px] font-normal text-[#05073C]">
                              Group Size
                         </div>
                         <div className="text-[14px] sm:text-[16px] leading-[24px] font-semibold text-[#05073C]">
                              {groupSize}
                         </div>
                    </div>}

                    <div className="mb-[16px] flex items-center justify-between gap-1">
                         <div className="text-[14px] leading-[20px] font-normal text-[#05073C]">
                              Duration
                         </div>
                         <div className="text-[14px] sm:text-[16px] leading-[24px] font-semibold text-[#05073C]">
                              {duration} Days
                         </div>
                    </div>


                    <div className="flex gap-4 items-center justify-between">
                         {booking_url ? <button type="button" onClick={handleContactAgent}
                              className="w-full py-[8px] text-center text-[14px] cursor-pointer leading-[20px] font-normal text-white bg-[#FAB400] rounded-[4px]"
                         >
                              Contact Agent
                         </button> : <button
                              className="w-full py-[8px] text-[14px] cursor-pointer leading-[20px] font-normal text-white bg-[#FAB400] rounded-[4px]"
                              onClick={handleBookNow}
                         >
                              Request Booking
                         </button>}
                         <button
                              className="w-full py-[8px] text-[14px] cursor-pointer leading-[20px] font-normal text-white bg-[#1A339B] rounded-[4px]"
                              onClick={onContactClick}
                         >
                              Contact Us
                         </button>
                    </div>
               </div>
          </div>
     );
};

export default PriceCard;


