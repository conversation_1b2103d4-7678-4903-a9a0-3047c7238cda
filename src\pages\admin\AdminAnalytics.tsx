import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Bar,
  Legend
} from 'recharts';
import { useAnalyticsDashboardQuery, useLineChartGroupByQuery } from "../../store/features/admin/adminApi";
import AdminAnalyticsSkeleton from "../../components/skeletons/AdminAnalyticsSkeleton";
import { useState, useRef, useLayoutEffect } from "react";

const GROUP_OPTIONS = [
  { label: "Hourly", value: "hourly" },
  { label: "Daily", value: "daily" },
  { label: "Weekly", value: "weekly" },
  { label: "Monthly", value: "monthly" },
  { label: "Yearly", value: "yearly" },
];

const AdminAnalytics = () => {
  const { data: analytics, isLoading: isSummaryLoading } = useAnalyticsDashboardQuery({});
  const analyticsData = analytics?.data;
  const [groupBy, setGroupBy] = useState("hourly");
  const [sliderStyle, setSliderStyle] = useState({
    width: '80px',
    transform: 'translateX(0)',
  });
  const buttonRefs = useRef<(HTMLButtonElement | null)[]>([]);

  useLayoutEffect(() => {
    const updateSliderPosition = () => {
      const activeButton = buttonRefs.current[GROUP_OPTIONS.findIndex(opt => opt.value === groupBy)];
      if (activeButton) {
        setSliderStyle({
          width: `${activeButton.offsetWidth}px`,
          transform: `translateX(${activeButton.offsetLeft}px)`,
        });
      }
    };

    updateSliderPosition();

    const timer = setTimeout(updateSliderPosition, 0);
    return () => clearTimeout(timer);
  }, [groupBy]);

  const handleGroupChange = (value: string) => {
    setGroupBy(value);
  };

  const { data: lineChartData, isLoading: isLineLoading } = useLineChartGroupByQuery(groupBy);

  return (
    <>
      {isSummaryLoading ? <AdminAnalyticsSkeleton /> :
        <>
          {/* Summary Cards - Improved grid responsiveness */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 mb-6 sm:mb-8">
            <div className="bg-white p-3 sm:p-6 rounded-lg border border-[#E5E7EB] shadow-sm">
              <div className="text-xs sm:text-sm font-medium text-[#6B7280] mb-1">Total Chats</div>
              <div className="text-lg sm:text-2xl font-bold text-[#05073C]">
                {analyticsData?.total_chats}
              </div>
            </div>
            <div className="bg-white p-3 sm:p-6 rounded-lg border border-[#E5E7EB] shadow-sm">
              <div className="text-xs sm:text-sm font-medium text-[#6B7280] mb-1">Active Users</div>
              <div className="text-lg sm:text-2xl font-bold text-[#05073C]">
                {analyticsData?.total_users}
              </div>
            </div>
            <div className="bg-white p-3 sm:p-6 rounded-lg border border-[#E5E7EB] shadow-sm">
              <div className="text-xs sm:text-sm font-medium text-[#6B7280] mb-1">Fallback Rate</div>
              <div className="text-lg sm:text-2xl font-bold text-red-600">
                {analyticsData?.fallback_rate?.toFixed(2)}%
              </div>
            </div>
            <div className="bg-white p-3 sm:p-6 rounded-lg border border-[#E5E7EB] shadow-sm">
              <div className="text-xs sm:text-sm font-medium text-[#6B7280] mb-1">Top Destination</div>
              <div className="text-lg sm:text-2xl font-bold text-[#05073C] first-letter:capitalize">
                {analyticsData?.top_destinations[0]?.destination}
              </div>
            </div>
          </div>

          {/* Chart Header - Improved responsive layout */}
          <div className="bg-white rounded-t-lg border border-[#E5E7EB] shadow-sm">
            <div className="p-3 sm:p-4 md:p-6 border-b border-[#E5E7EB]">
              <div className="flex flex-col xs:flex-row items-start xs:items-center justify-between gap-3 sm:gap-4">
                <h2 className="text-base sm:text-lg font-semibold text-[#05073C]">Chats Over Time</h2>

                <div className="relative bg-gray-100 p-1 rounded-lg w-fit xs:w-auto overflow-x-auto">
                  {/* Sliding Background - Updated with transition */}
                  <div
                    className="absolute top-1 left-1 bottom-1 bg-white rounded-md shadow-sm transition-all duration-300 ease-out z-10"
                    style={sliderStyle}
                  />

                  {/* Buttons - Improved spacing */}
                  <div className="relative flex z-10 min-w-fit">
                    {GROUP_OPTIONS.map((option, index) => (
                      <button
                        key={option.value}
                        ref={(el: HTMLButtonElement | null) => {
                          buttonRefs.current[index] = el;
                        }}
                        onClick={() => handleGroupChange(option.value)}
                        className={`
                        whitespace-nowrap px-2 sm:px-3 py-1.5 sm:py-2 text-xs sm:text-sm font-medium rounded-md transition-colors relative cursor-pointer
                        ${groupBy === option.value
                            ? 'text-blue-600'
                            : 'text-gray-600 hover:text-gray-900'
                          }
                        ${index !== GROUP_OPTIONS.length - 1 ? 'mr-1' : ''}
                      `}
                      >
                        {option.label}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Time Period Description */}
              <div className="mt-2 text-sm text-gray-500">
                Showing chat statistics {groupBy === 'hourly' ? 'per hour'
                  : groupBy === 'daily' ? 'per day'
                    : groupBy === 'weekly' ? 'per week'
                      : groupBy === 'monthly' ? 'per month'
                        : 'per year'}
              </div>
            </div>

            {/* Chart Content - Improved responsive heights */}
            <div className="p-3 sm:p-4 md:p-6">
              <div className="h-48 xs:h-64 sm:h-72 md:h-80">
                {isLineLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="flex items-center gap-3">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                      <span className="text-gray-600">Loading chart data...</span>
                    </div>
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={lineChartData?.data?.data || []}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                      <XAxis
                        dataKey="date"
                        stroke="#6B7280"
                        fontSize={12}
                        tickLine={false}
                      />
                      <YAxis
                        stroke="#6B7280"
                        fontSize={12}
                        tickLine={false}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'white',
                          border: '1px solid #E5E7EB',
                          borderRadius: '8px',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="chats"
                        stroke="#3B82F6"
                        strokeWidth={3}
                        dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                        name="Chats"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                )}
              </div>
            </div>
          </div>

          {/* Top Destinations Chart - Improved responsive layout */}
          <div className="mt-6">
            <div className="bg-white rounded-lg border border-[#E5E7EB] shadow-sm">
              <div className="p-3 sm:p-4 md:p-6 border-b border-[#E5E7EB]">
                <h2 className="text-base sm:text-lg font-semibold text-[#05073C]">Top Destinations</h2>
              </div>
              <div className="p-3 sm:p-4">
                <div className="h-48 xs:h-64 sm:h-72 md:h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={analyticsData?.top_destinations}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 60
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                      <XAxis
                        dataKey="destination"
                        stroke="#6B7280"
                        fontSize={12}
                        tickLine={false}
                        angle={-45}
                        textAnchor="end"
                      />
                      <YAxis
                        stroke="#6B7280"
                        fontSize={12}
                        tickLine={false}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'white',
                          border: '1px solid #E5E7EB',
                          borderRadius: '8px',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Legend />
                      <Bar
                        dataKey="count"
                        name="Visits"
                        fill="#3B82F6"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
          </div>

        </>}
    </>
  );
};

export default AdminAnalytics;