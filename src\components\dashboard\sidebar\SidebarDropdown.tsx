import { <PERSON>, useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import { useDispatch, useSelector } from "react-redux";
import { addMessage, setMessages, threadUid as setThreadUid } from "../../../store/features/chat/chatSlice";
import { RootState } from "../../../store/store";

interface SidebarDropdownProps {
     onClose: () => void;
}

const SidebarDropdown: React.FC<SidebarDropdownProps> = ({ onClose }) => {
     const navigate = useNavigate();
     const dispatch = useDispatch();
     const { userDetails } = useSelector((state: RootState) => state.auth);
     const logout = () => {
          Cookies.remove("accessToken");
          dispatch(setThreadUid(null));
          dispatch(setMessages([]));
          dispatch(addMessage({
               content: "Hi there! I'm <PERSON>, your travel assistant. I'm here to help you find the best travel packages. To get started, where would you like to go? And how long are you planning to stay?",
               sender: "ai"
          }));
          navigate("/");
          onClose();
          window.location.reload();
     }

     const handleChangePassword = () => {
          onClose();
     }

     return (
          <div className="absolute bottom-[75px] left-[50%] translate-x-[-50%] bg-white rounded-[15px_0px_15px_0px] shadow-lg border border-[#E5E7EB] w-full py-[6px] px-[6px] max-w-[265px]">
               {userDetails?.registration_source === "regular" && <Link
                    to="/change-password"
                    className="text-[14px] text-[#636C76] font-semibold px-[6px] py-[8px] block hover:bg-[#E7ECF9] border border-white rounded-[5px] hover:border hover:border-[#0D3FC6]"
                    onClick={handleChangePassword}
               >
                    Change Password
               </Link>}
               <button
                    type="button"
                    onClick={logout}
                    className="text-[14px] w-full text-start text-[#636C76] font-semibold px-[6px] py-[8px] block hover:bg-[#E7ECF9] border border-white rounded-[5px] hover:border hover:border-[#0D3FC6]"
               >
                    Logout
               </button>
          </div>
     );
}

export default SidebarDropdown;
