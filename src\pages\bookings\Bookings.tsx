import { useState, useEffect } from "react";
import SearchIcon from "../../assets/search-icon.svg";
import SortByIcon from "../../assets/sort-by-icon.svg";
import FilterIcon from "../../assets/filter-icon.svg";
import BookingCards from "../../components/dashboard/bookings/BookingCards";
import BookingModal from "../../components/dashboard/bookings/BookingModal";
import BookingFilter from "../../components/dashboard/bookings/BookingFilter";
import { useGetBookingsQuery } from "../../store/features/bookings/bookingsApi";
import { threadUid as setThreadUid, setMessages, addMessage } from "../../store/features/chat/chatSlice";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import BookingCardsFilter, { FilterValues } from "../../components/dashboard/bookings/BookingCardsFilter";

interface Booking {
  id: number;
  destination: string;
  hotel: string;
  dates: string;
  booking_status: string;
  guests: string;
  booking_amount: string;
  status: string;
  image: string;
  type: string;
  order: {
    order_id: string;
    order_date: string;
    order_amount: string;
  };
  booking_reference: string;
  booking_date: string;
  duration: {
    start_date: string;
    end_date: string;
  };
  traveller_numbers: {
    total: number;
  };
  tour: {
    tour_title: string;
    tour_gallery_images: string[];
  };
  contact_details: {
    first_name: string;
    last_name: string;
    address: string;
  };

}

interface SelectedBookings {
  id: number;
  destination: string;
  hotel: string;
  dates: string;
  guests: string;
  booking_reference: string;
  booking_date: string;
  duration: {
    start_date: string;
    end_date: string;
  };
  traveller_numbers: {
    total: number;
  };
  booking_amount: string;
  booking_currency: string;
  booking_status: string;
  booking_type: string;
  status: string;
  tour: {
    location: {
      address: string;
    }
  },
  order: {
    order_id: string;
    payment_status: string;
  }
}

// Loader Component
const Loader = () => (
  <div className="flex flex-col items-center justify-center py-10">
    <div className="w-12 h-12 rounded-full border-4 border-[#E5E7EB] border-t-[#0D3FC6] animate-spin"></div>
    <p className="mt-4 text-gray-600 font-medium">Loading your bookings...</p>
    
  </div>
);

const Bookings = () => {
  const navigate = useNavigate();
  const { data, isLoading } = useGetBookingsQuery({});
  const [activeTab, setActiveTab] = useState("in_progress");
  const [selectedBooking, setSelectedBooking] = useState<SelectedBookings | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const dispatch = useDispatch();
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<"newest" | "oldest" | "price-high" | "price-low">("newest");
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);

  // Add saved filters state
  const [savedFilters, setSavedFilters] = useState<FilterValues>({
    dateRange: { from: '', to: '' },
    priceRange: { min: '', max: '' },
    status: ''
  });

  const handleNewBooking = () => {
    dispatch(setThreadUid(null));
    dispatch(setMessages([]));
    dispatch(addMessage({
      content: "Hi there! I'm Zoe, your travel assistant. I'm here to help you find the best travel packages. To get started, where would you like to go? And how long are you planning to stay?",
      sender: "ai"
    }));
    navigate('/');
  }

  const sortBookings = (bookings: Booking[]) => {
    if (!bookings) return [];

    return [...bookings].sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return new Date(b.booking_date || '').getTime() - new Date(a.booking_date || '').getTime();
        case "oldest":
          return new Date(a.booking_date || '').getTime() - new Date(b.booking_date || '').getTime();
        case "price-high": {
          const priceA = parseFloat(a.booking_amount?.replace(/[^0-9.-]+/g, '') || '0');
          const priceB = parseFloat(b.booking_amount?.replace(/[^0-9.-]+/g, '') || '0');
          return priceB - priceA;
        }
        case "price-low": {
          const priceC = parseFloat(a.booking_amount?.replace(/[^0-9.-]+/g, '') || '0');
          const priceD = parseFloat(b.booking_amount?.replace(/[^0-9.-]+/g, '') || '0');
          return priceC - priceD;
        }
        default:
          return 0;
      }
    });
  };

  const filterBookings = (bookings: Booking[]) => {
    if (!bookings) return [];

    return bookings.filter(booking => {
      // Apply search term filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase().replace(/[-]/g, '');
        const searchableFields = [
          booking.destination,
          booking.hotel,
          booking.booking_reference,
          booking.contact_details?.first_name,
          booking.contact_details?.last_name,
          booking.order?.order_id,
          booking.tour?.tour_title
        ].map(field => (field || '').toLowerCase().replace(/[-]/g, ''));

        if (!searchableFields.some(field => field.includes(searchLower))) {
          return false;
        }
      }

      // Filter by status
      if (savedFilters.status && booking.booking_status !== savedFilters.status) {
        return false;
      }

      // Filter by order date range
      if (savedFilters.dateRange.from || savedFilters.dateRange.to) {
        const orderDate = new Date(booking.order?.order_date);
        // Convert dates to start and end of day for accurate comparison
        const fromDate = savedFilters.dateRange.from ? new Date(savedFilters.dateRange.from + 'T00:00:00') : null;
        const toDate = savedFilters.dateRange.to ? new Date(savedFilters.dateRange.to + 'T23:59:59') : null;

        if (fromDate && orderDate < fromDate) {
          return false;
        }
        if (toDate && orderDate > toDate) {
          return false;
        }
      }

      // Filter by price range
      if (savedFilters.priceRange.min || savedFilters.priceRange.max) {
        const price = parseFloat(booking.order?.order_amount?.replace(/[^0-9.-]+/g, '') || '0');
        if (savedFilters.priceRange.min && price < parseFloat(savedFilters.priceRange.min)) {
          return false;
        }
        if (savedFilters.priceRange.max && price > parseFloat(savedFilters.priceRange.max)) {
          return false;
        }
      }

      return true;
    });
  };

  const handleFilterClick = () => {
    setShowFilterModal(true);
  };

  const handleFilterApply = (filters: FilterValues) => {
    setSavedFilters(filters);
    setShowFilterModal(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showSortDropdown && !(event.target as Element).closest('.sort-dropdown')) {
        setShowSortDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSortDropdown]);

  return (
    <div className="flex">
      <div className="md:ms-[275px] h-screen overflow-y-auto flex-1">
        <div className="fixed md:static z-10 w-full bg-white py-[15px] sm:py-[14px] mx-auto sm:px-[30px] px-[10px] border-b border-[#E5E7EB] shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)]">
          <h1 className="text-center md:text-start text-[20px] sm:text-[24px] leading-[32px] font-bold text-[#05073C] max-w-[1240px] mx-auto">
            My Bookings
          </h1>
        </div>

        <div className="md:mt-0 mt-[60px]  max-w-[1240px] sm:px-[30px] px-[10px] mx-auto">
          {/* Tabs */}
          <BookingFilter activeTab={activeTab} setActiveTab={setActiveTab} />

          {/* search and filter */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
            <div className="relative max-w-[350px] flex-1">
              <span className="absolute inset-y-0 left-[13px] flex items-center text-gray-400">
                <img
                  src={SearchIcon}
                  alt="Search"
                  className="w-[18px] h-[18px]"
                />
              </span>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search Bookings"
                className="w-full pl-[40px] pr-[10px] py-[9px] rounded-[8px] text-[14px] font-semibold leading-[20px] placeholder:text-[#6B7280] border border-[#E5E7EB] focus:outline-none"
              />
            </div>
            <div className="flex w-full sm:w-auto flex-wrap gap-[10px]">
              <button
                onClick={handleFilterClick}
                className="px-4 py-2 flex gap-[6px] bg-white border border-[#E5E7EB] rounded-sm text-sm text-[#636A7E] items-center cursor-pointer"
              >
                <img src={FilterIcon} alt="Filter" className="" />
                Filter
                {(savedFilters?.status || savedFilters.dateRange.from || savedFilters.dateRange.to || savedFilters.priceRange.min || savedFilters.priceRange.max) && (
                  <span className="w-2 h-2 bg-blue-500 rounded-full ml-1"></span>
                )}
              </button>
              <div className="relative sort-dropdown">
                <button
                  onClick={() => setShowSortDropdown(!showSortDropdown)}
                  className="px-4 py-2 flex gap-[6px] bg-white border border-[#E5E7EB] rounded-sm text-sm text-[#636A7E] items-center cursor-pointer"
                >
                  <img src={SortByIcon} alt="Sort" className="" />
                  Sort by {sortBy.replace("-", " ")}
                </button>

                {showSortDropdown && (
                  <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                    <div className="py-1">
                      <button
                        onClick={() => {
                          setSortBy("newest");
                          setShowSortDropdown(false);
                        }}
                        className={`block w-full text-left px-4 py-2 text-sm ${sortBy === "newest" ? "bg-blue-50 text-blue-700" : "text-gray-700 hover:bg-gray-50"
                          }`}
                      >
                        Newest First
                      </button>
                      <button
                        onClick={() => {
                          setSortBy("oldest");
                          setShowSortDropdown(false);
                        }}
                        className={`block w-full text-left px-4 py-2 text-sm ${sortBy === "oldest" ? "bg-blue-50 text-blue-700" : "text-gray-700 hover:bg-gray-50"
                          }`}
                      >
                        Oldest First
                      </button>
                      <button
                        onClick={() => {
                          setSortBy("price-high");
                          setShowSortDropdown(false);
                        }}
                        className={`block w-full text-left px-4 py-2 text-sm ${sortBy === "price-high" ? "bg-blue-50 text-blue-700" : "text-gray-700 hover:bg-gray-50"
                          }`}
                      >
                        Price: High to Low
                      </button>
                      <button
                        onClick={() => {
                          setSortBy("price-low");
                          setShowSortDropdown(false);
                        }}
                        className={`block w-full text-left px-4 py-2 text-sm ${sortBy === "price-low" ? "bg-blue-50 text-blue-700" : "text-gray-700 hover:bg-gray-50"
                          }`}
                      >
                        Price: Low to High
                      </button>
                    </div>
                  </div>
                )}
              </div>
              <button onClick={handleNewBooking} className="px-4 py-2 cursor-pointer bg-[#0D3FC6] text-white text-sm flex items-center rounded-[4px]">
                New Booking
              </button>
            </div>
          </div>

          {/* Loader while fetching data */}
          {isLoading ? (
            <Loader />
          ) : (
            <>
              {/* Empty state when no bookings are found */}
              {(!data?.upcoming?.length && activeTab === "upcoming") ||
                (!data?.previous?.length && activeTab === "previous") ||
                (!data?.cancelled?.length && activeTab === "cancelled") ||
                (!data?.in_progress?.length && activeTab === "in_progress") ? (
                <div className="flex flex-col items-center justify-center py-10 text-center">
                  <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 5H7C5.89543 5 5 5.89543 5 7V19C5 20.1046 5.89543 21 7 21H17C18.1046 21 19 20.1046 19 19V7C19 5.89543 18.1046 5 17 5H15M9 5C9 6.10457 9.89543 7 11 7H13C14.1046 7 15 6.10457 15 5M9 5C9 3.89543 9.89543 3 11 3H13C14.1046 3 15 3.89543 15 5" stroke="#9CA3AF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                  <p className="mt-4 text-gray-600 font-medium">No {activeTab} bookings found</p>
                  <p className="text-gray-500">Your {activeTab} bookings will appear here</p>
                </div>
              ) : (
                <>
                  {/* upcoming tab Content */}
                  {activeTab === "upcoming" &&
                    sortBookings(filterBookings(data?.upcoming))?.map((booking: Booking) => (
                      <BookingCards
                        key={booking.id}
                        booking={booking}
                        setSelectedBooking={setSelectedBooking}
                        setShowDetailModal={setShowDetailModal}
                      />
                    ))}

                  {/* Previous Content */}
                  {activeTab === "previous" &&
                    sortBookings(filterBookings(data?.previous))?.map((booking: Booking) => (
                      <BookingCards
                        key={booking.id}
                        booking={booking}
                        setSelectedBooking={setSelectedBooking}
                        setShowDetailModal={setShowDetailModal}
                      />
                    ))}

                  {/* Cancelled Content */}
                  {activeTab === "cancelled" &&
                    sortBookings(filterBookings(data?.cancelled))?.map((booking: Booking) => (
                      <BookingCards
                        key={booking.id}
                        booking={booking}
                        setSelectedBooking={setSelectedBooking}
                        setShowDetailModal={setShowDetailModal}
                      />
                    ))}

                  {/* in_progress Content */}
                  {activeTab === "in_progress" &&
                    sortBookings(filterBookings(data?.in_progress))?.map((booking: Booking) => (
                      <BookingCards
                        key={booking.id}
                        booking={booking}
                        setSelectedBooking={setSelectedBooking}
                        setShowDetailModal={setShowDetailModal}
                      />
                    ))}
                </>
              )}
            </>
          )}
        </div>
      </div>

      {showDetailModal && selectedBooking && (
        <BookingModal
          setShowDetailModal={setShowDetailModal}
          selectedBooking={selectedBooking}
        />
      )}

      {showFilterModal && (
        <BookingCardsFilter
          onFilterApply={handleFilterApply}
          onClose={() => setShowFilterModal(false)}
          initialFilters={savedFilters}
        />
      )}
    </div>
  );
};

export default Bookings;