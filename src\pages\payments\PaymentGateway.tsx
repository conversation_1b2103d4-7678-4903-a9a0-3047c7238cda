import { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useGetBookinGatewaysQuery, useDirectBankPaymentMutation, useInitiatePaymentMutation } from '../../store/features/bookings/bookingsApi';

const PaymentGateway = () => {
     const [selectedGateway, setSelectedGateway] = useState<string | null>(null);
     const [tempSelectedGateway, setTempSelectedGateway] = useState<{ id: string, method_title: string } | null>(null);
     const { orderId } = useParams();
     const { data: gateways, isLoading } = useGetBookinGatewaysQuery(orderId || "");
     // const [selectBookingGateway] = useSelectBookingGatewayMutation();
     const [directBankPayment] = useDirectBankPaymentMutation();
     const [initiatePayment] = useInitiatePaymentMutation();
     const [isProcessing, setIsProcessing] = useState(false);

     const handleContinuePayment = async () => {
          if (!tempSelectedGateway) return;

          setIsProcessing(true);

         try {
               // Pass payment_method_title as payment_method
               const directBankPayload = {
                    order_id: orderId,
                    status: "on-hold",
                    payment_method: tempSelectedGateway.method_title
               };

               const initiatePaymentPayload = {
                    order_id: orderId,
                    payment_method: tempSelectedGateway.method_title
               };

               if (tempSelectedGateway?.id === "sslcommerz") {
                    const response = await initiatePayment(initiatePaymentPayload);
                    if (response?.data?.GatewayPageURL) {
                         window.location.href = response?.data?.GatewayPageURL;
                    } else {
                         window.location.href = "/payment-failed";
                    }
               }
               else {
                    const response = await directBankPayment(directBankPayload);
                    if (response?.data?.success) {
                         if (tempSelectedGateway?.id === "bacs") {
                              window.location.href = `/bank-details/${orderId}`;
                         } else {
                              window.location.href = "/thank-you";
                         }
                    } else {
                         window.location.href = "/payment-failed";
                    }
               }
         } catch (error) {
               console.error("Payment processing error:", error);
         } finally {
               setIsProcessing(false);
         }
     };

     return (
          <div className="flex">
               <div className="md:ms-[275px] h-screen overflow-y-auto flex-1">
                    <div className="py-[15px] sm:py-[14px] mx-auto sm:px-[30px] px-[10px] border-b border-[#E5E7EB] shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)]">
                         <h1 className="text-center lg:text-start text-[20px] sm:text-[24px] leading-[32px] font-bold text-[#05073C] max-w-[1240px] mx-auto">
                              Payment Gateway
                         </h1>
                    </div>

                    <div className="max-w-[740px] lg:max-w-[1240px] mx-auto lg:px-[30px] sm:px-[15px] px-[8px] py-[30px]">
                         <div className="bg-white rounded-[10px] shadow-md p-[20px] sm:p-[30px] max-w-[600px] mx-auto">
                              <h2 className="text-[18px] font-semibold mb-[20px] text-[#05073C]">Select Payment Method</h2>

                              {isLoading ? (
                                   <div className="flex justify-center py-[40px]">
                                        <div className="animate-spin rounded-full h-[40px] w-[40px] border-b-2 border-[#0D3FC6]"></div>
                                   </div>
                              ) : (
                                   <div className="space-y-[10px] w-full">
                                        {gateways?.payment_gateways
                                             ?.filter((gateway: { id: string }) =>
                                                  ['bacs', 'cod', 'sslcommerz'].includes(gateway.id)
                                             )
                                             .map((gateway: { id: string, title: string, description: string, method_title: string }) => (
                                                  <label
                                                       key={gateway?.id}
                                                       className={`flex items-center space-x-[15px] p-[15px] border rounded-[8px] cursor-pointer transition-all duration-200 w-full
                                                       ${selectedGateway === gateway?.title
                                                                 ? 'border-[#0D3FC6] bg-[#F3F4FF]'
                                                                 : 'border-[#E5E7EB] hover:border-[#0D3FC6] hover:bg-[#F9FAFB]'}`}
                                                  >
                                                       <input
                                                            type="radio"
                                                            name="paymentMethod"
                                                            checked={selectedGateway === gateway?.title}
                                                            onChange={() => {
                                                                 setSelectedGateway(gateway?.title);
                                                                 setTempSelectedGateway(gateway);
                                                            }}
                                                            className="h-[18px] w-[18px] flex-shrink-0 text-[#0D3FC6] focus:ring-[#0D3FC6]"
                                                       />
                                                       <div className="flex flex-col flex-1 min-w-0">
                                                            <span className="font-medium text-[14px] sm:text-[15px] text-[#05073C] break-words">{gateway?.title}</span>
                                                            {gateway?.description && (
                                                                 <span className="text-[12px] sm:text-[13px] text-[#6B7280] mt-[5px] break-words line-clamp-3">{gateway?.description}</span>
                                                            )}
                                                       </div>
                                                  </label>
                                             ))}


                                   </div>
                              )}

                              <button
                                   className={`mt-[30px] w-full py-[12px] px-[20px] rounded-[8px] font-medium text-[15px] transition-all duration-200
                                        ${!selectedGateway || isProcessing
                                             ? 'bg-[#E5E7EB] text-[#9CA3AF] cursor-not-allowed'
                                             : 'bg-[#0D3FC6] hover:bg-[#0A309A] text-white'
                                        }`}
                                   disabled={!selectedGateway || isProcessing}
                                   onClick={handleContinuePayment}
                              >
                                   {isProcessing ? (
                                        <span className="flex items-center justify-center">
                                             <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                             </svg>
                                             Processing...
                                        </span>
                                   ) : 'Continue to Payment'}
                              </button>
                         </div>
                    </div>
               </div>
          </div>
     );
};

export default PaymentGateway;
