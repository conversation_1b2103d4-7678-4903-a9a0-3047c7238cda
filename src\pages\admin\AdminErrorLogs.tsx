import { useGetErrorLogsQuery } from "../../store/features/admin/adminApi";
import AdminPackagesSkeleton from "../../components/skeletons/AdminPackagesSkeleton";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { setMessages, threadUid as setThreadUid } from "../../store/features/chat/chatSlice";
import { useState } from "react";

interface ErrorLog {
     error_message: string;
     thread: {
          thread_name: string;
          created_at: string;
          uid: string;
          user: string;
     };
     user: string;
}

interface ModalState {
    isOpen: boolean;
    message: string;
}

const AdminErrorLogs = () => {
     const { data: errorLogs, isLoading } = useGetErrorLogsQuery({});
     const errorLogsData = errorLogs?.data;
     const navigate = useNavigate();
     const dispatch = useDispatch()
     const [modal, setModal] = useState<ModalState>({ isOpen: false, message: "" });

     const formatDate = (dateString: string) => {
          const date = new Date(dateString);
          return date.toLocaleDateString('en-US', {
               year: 'numeric',
               month: 'short',
               day: 'numeric',
               hour: '2-digit',
               minute: '2-digit'
          });
     };

     const handleViewHistory = (threadId: string) => {
          dispatch(setMessages([]));
          dispatch(setThreadUid(null));
          navigate(`/admin/error-logs/threads/${threadId}/logs`);
     }

     const handleViewMessage = (message: string) => {
          setModal({ isOpen: true, message });
     };

     return (
          <>
               <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                    {isLoading ? (
                         <AdminPackagesSkeleton />
                    ) : (
                         <div className="max-h-[calc(100vh-100px)] sm:max-h-[calc(100vh-180px)] md:max-h-[calc(100vh-200px)] overflow-auto">
                              {/* Mobile View - Card Layout */}
                              <div className="block sm:hidden">
                                   <div className="divide-y divide-gray-200">
                                        {errorLogsData?.map((log: ErrorLog) => (
                                             <div key={log?.thread?.uid} className="p-3 sm:p-4 hover:bg-gray-50/50 transition-colors">
                                                  <div className="space-y-2.5">
                                                       <div>
                                                            <div className="text-[11px] sm:text-xs font-medium text-gray-500 uppercase">Thread Name</div>
                                                            <div className="text-xs sm:text-sm mt-0.5 sm:mt-1">{log?.thread?.thread_name}</div>
                                                       </div>
                                                       <div>
                                                            <div className="text-[11px] sm:text-xs font-medium text-gray-500 uppercase">Thread ID</div>
                                                            <div className="text-xs sm:text-sm mt-0.5 sm:mt-1 break-all">{log?.thread?.uid}</div>
                                                       </div>
                                                       <div>
                                                            <div className="text-[11px] sm:text-xs font-medium text-gray-500 uppercase">Error Message</div>
                                                            <div className="mt-0.5 sm:mt-1">
                                                                 <button
                                                                      onClick={() => handleViewMessage(log?.error_message)}
                                                                      className="inline-block px-2 py-0.5 sm:px-2.5 sm:py-1 rounded-full text-[11px] sm:text-xs font-medium bg-red-50 text-red-700 ring-1 ring-inset ring-red-600/20 hover:bg-red-100"
                                                                 >
                                                                      View Message
                                                                 </button>
                                                            </div>
                                                       </div>
                                                       <div>
                                                            <div className="text-[11px] sm:text-xs font-medium text-gray-500 uppercase">User Email</div>
                                                            <div className="text-xs sm:text-sm mt-0.5 sm:mt-1 break-all">{log?.thread?.user}</div>
                                                       </div>
                                                       <div>
                                                            <div className="text-[11px] sm:text-xs font-medium text-gray-500 uppercase">Created At</div>
                                                            <div className="text-xs sm:text-sm mt-0.5 sm:mt-1 text-gray-500">{formatDate(log?.thread?.created_at)}</div>
                                                       </div>
                                                       <button
                                                            onClick={() => handleViewHistory(log?.thread?.uid)}
                                                            className="mt-3 w-full text-center py-1.5 sm:py-2 px-3 sm:px-4 text-xs sm:text-sm text-blue-600 hover:text-blue-700 border border-blue-200 rounded-md hover:bg-blue-50 transition-colors"
                                                       >
                                                            View History
                                                       </button>
                                                  </div>
                                             </div>
                                        ))}
                                   </div>
                              </div>

                              {/* Desktop/Tablet View - Table Layout */}
                              <div className="hidden sm:block">
                                   <div className="min-w-full inline-block align-middle">
                                        <div className="overflow-x-auto">
                                             <table className="min-w-full divide-y divide-gray-200">
                                                  <thead className="bg-gray-50 sticky top-0">
                                                       <tr>
                                                            <th scope="col" className="px-2 sm:px-3 md:px-4 lg:px-6 py-2 sm:py-3 text-left text-[11px] sm:text-xs font-semibold text-gray-600 uppercase tracking-wider rounded-tl-lg">
                                                                 Thread Name
                                                            </th>
                                                            <th scope="col" className="px-2 sm:px-3 md:px-4 lg:px-6 py-2 sm:py-3 text-left text-[11px] sm:text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                                 Thread Id
                                                            </th>
                                                            <th scope="col" className="px-2 sm:px-3 md:px-4 lg:px-6 py-2 sm:py-3 text-left text-[11px] sm:text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                                 Error Message
                                                            </th>
                                                            <th scope="col" className="px-2 sm:px-3 md:px-4 lg:px-6 py-2 sm:py-3 text-left text-[11px] sm:text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                                 User Email
                                                            </th>
                                                            <th scope="col" className="px-2 sm:px-3 md:px-4 lg:px-6 py-2 sm:py-3 text-left text-[11px] sm:text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                                 Created At
                                                            </th>
                                                            <th scope="col" className="px-2 sm:px-3 md:px-4 lg:px-6 py-2 sm:py-3 text-left text-[11px] sm:text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                                 Actions
                                                            </th>
                                                       </tr>
                                                  </thead>
                                                  <tbody className="bg-white divide-y divide-gray-200">
                                                       {errorLogsData?.map((log: ErrorLog) => (
                                                            <tr key={log?.thread?.uid} className="hover:bg-gray-50/50 transition-colors">
                                                                 <td className="px-2 sm:px-3 md:px-4 lg:px-6 py-2 sm:py-4">
                                                                      <div className="text-xs sm:text-sm text-gray-900 line-clamp-2">
                                                                           {log?.thread?.thread_name}
                                                                      </div>
                                                                 </td>
                                                                 <td className="px-2 sm:px-3 md:px-4 lg:px-6 py-2 sm:py-4">
                                                                      <div className="text-xs sm:text-sm text-gray-900 break-all">
                                                                           {log?.thread?.uid}
                                                                      </div>
                                                                 </td>
                                                                 <td className="px-2 sm:px-3 md:px-4 lg:px-6 py-2 sm:py-4">
                                                                      <button
                                                                           onClick={() => handleViewMessage(log?.error_message)}
                                                                           className="bg-blue-50 text-blue-700 px-2 py-1 rounded-md text-xs sm:text-sm"
                                                                      >
                                                                           View Message
                                                                      </button>
                                                                 </td>
                                                                 <td className="px-2 sm:px-3 md:px-4 lg:px-6 py-2 sm:py-4">
                                                                      <div className="text-xs sm:text-sm text-gray-900 break-all">
                                                                           {log?.thread?.user}
                                                                      </div>
                                                                 </td>
                                                                 <td className="px-2 sm:px-3 md:px-4 lg:px-6 py-2 sm:py-4 whitespace-nowrap">
                                                                      <div className="text-xs sm:text-sm text-gray-500">
                                                                           {formatDate(log?.thread?.created_at)}
                                                                      </div>
                                                                 </td>
                                                                 <td className="px-2 sm:px-3 md:px-4 lg:px-6 py-2 sm:py-4 whitespace-nowrap">
                                                                      <button
                                                                           onClick={() => handleViewHistory(log?.thread?.uid)}
                                                                           className="text-xs sm:text-sm text-blue-600 hover:text-blue-700 hover:underline"
                                                                      >
                                                                           View History
                                                                      </button>
                                                                 </td>
                                                            </tr>
                                                       ))}
                                                  </tbody>
                                             </table>
                                        </div>
                                   </div>
                              </div>
                         </div>
                    )}
               </div>

               {/* Add Modal */}
               {modal.isOpen && (
                    <div className="fixed inset-0 bg-black/70 bg-opacity-50 flex items-center justify-center z-[999] p-2 sm:p-4">
                         <div className="bg-white rounded-lg p-3 sm:p-6 w-full max-w-[98%] sm:max-w-lg max-h-[95vh] overflow-y-auto">
                              <div className="flex justify-between items-center mb-4">
                                   <h3 className="text-sm sm:text-lg font-medium text-gray-900">Error Message</h3>
                                   <button
                                        onClick={() => setModal({ isOpen: false, message: "" })}
                                        className="text-gray-400 hover:text-gray-500 p-1"
                                   >
                                        <span className="sr-only">Close</span>
                                        <svg className="h-4 w-4 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                             <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                   </button>
                              </div>
                              <div className="space-y-4">
                                   <div className="bg-red-50 p-3 sm:p-4 rounded-lg">
                                        <p className="text-xs sm:text-sm text-red-700 break-words whitespace-pre-wrap">
                                             {modal.message}
                                        </p>
                                   </div>
                              </div>
                              <div className="mt-6">
                                   <button
                                        onClick={() => setModal({ isOpen: false, message: "" })}
                                        className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-3 py-1.5 sm:px-4 sm:py-2 bg-white text-xs sm:text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                   >
                                        Close
                                   </button>
                              </div>
                         </div>
                    </div>
               )}
          </>
     );
};

export default AdminErrorLogs;