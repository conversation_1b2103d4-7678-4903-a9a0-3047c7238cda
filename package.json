{"name": "travelagent", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@react-pdf/renderer": "^4.3.0", "@reduxjs/toolkit": "^2.6.1", "@tailwindcss/vite": "^4.0.17", "buffer": "^6.0.3", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "firebase": "^11.5.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "process": "^0.11.10", "react": "^19.0.0", "react-dom": "^19.0.0", "react-firebaseui": "^6.0.0", "react-hook-form": "^7.55.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.4.1", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "tailwindcss": "^4.0.17"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.17", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}