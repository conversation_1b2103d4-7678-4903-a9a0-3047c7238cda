import Logo from '../../assets/site-logo.svg';

const LeftSide = ({ video, title, description }: { video: string, title: string, description: string }) => {
     return (
          <div className="relative w-full md:w-1/2 min-h-[100vh] hidden md:flex flex-col items-center">
               <div className="w-full h-full">
                    <video
                         className="object-cover md:h-[100vh] w-full object-center"
                         autoPlay
                         muted
                         loop
                         playsInline
                    >
                         <source
                              src={video}
                              type="video/mp4"
                         />
                         Your browser does not support the video tag.
                    </video>
               </div>
               <div className='absolute left-[50%] translate-x-[-50%] text-center w-full top-[45px] max-w-[600px] px-[10px]'>
                    <img src={Logo} alt="Logo" className='mx-auto' />
                    <p className='text-[16px] leading-[24px] text-[#FFFFFF] font-normal mt-[10px]'>{title}</p>
                    <p className='text-[16px] leading-[24px] text-[#FFFFFF] font-normal mt-[10px]'>{description}</p>
               </div>
          </div>
     );
};

export default LeftSide;