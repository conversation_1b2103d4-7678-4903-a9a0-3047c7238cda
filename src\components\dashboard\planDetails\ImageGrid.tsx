import { useState } from "react";
import PlanImage1 from "../../../assets/plan-img1.png";
// import PlanImage2 from "../../../assets/plan-img2.png";
// import PlanImage3 from "../../../assets/plan-img3.png";
// import PlanImage4 from "../../../assets/plan-img4.png";

const ImageGrid = ({ images }: { images: string[] }) => {
    const [isGalleryOpen, setIsGalleryOpen] = useState(false);
    const [selectedImage, setSelectedImage] = useState<string | null>(null);

    // Only show up to 4 images in the grid
    const displayImages = images?.slice(0, 4);

    const openFullImage = (image: string) => {
        setSelectedImage(image);
    };

    const closeFullImage = () => {
        setSelectedImage(null);
    };

    return (
        <div className="relative sm:mt-0 mt-15">
            <div className="grid lg:grid-cols-3 gap-[10px] my-[15px]">
                {/* Main Image (first image) */}
                <div className="lg:col-span-2 w-full rounded-lg rounded-s-none">
                    {displayImages?.length > 0 ? displayImages[0] && (
                        <img
                            src={displayImages[0]}
                            alt="Main"
                            className="w-full h-full object-cover rounded-lg cursor-pointer"
                            onClick={() => openFullImage(displayImages[0])}
                        />
                    ) : <img src={PlanImage1} alt="Main" className="w-full h-full object-cover rounded-lg cursor-pointer" onClick={() => openFullImage(PlanImage1)} />}
                </div>

                {/* Side Images */}
                <div className="lg:col-span-1 h-full hidden sm:flex flex-col gap-[10px]">
                    {/* Second image (top right) */}
                    <div className="h-1/2 overflow-hidden rounded-lg rounded-s-none">
                        {displayImages?.length > 0 ? displayImages[1] && (
                            <img
                                src={displayImages[1]}
                                alt="Side 1"
                                className="w-full h-full object-cover rounded-lg cursor-pointer"
                                onClick={() => openFullImage(displayImages[1])}
                            />
                        ) : <img src={PlanImage1} alt="Side 1" className="w-full h-full object-cover rounded-lg cursor-pointer" onClick={() => openFullImage(PlanImage1)} />}
                    </div>
                    {/* Third and Fourth images (bottom right) */}
                    <div className="h-1/2 flex gap-[10px]">
                        <div className="w-1/2 overflow-hidden relative group">
                            {displayImages?.length > 0 ? displayImages[2] && (
                                <img
                                    src={displayImages[2]}
                                    alt="Side 2"
                                    className="w-full h-full object-cover rounded-lg cursor-pointer"
                                    onClick={() => openFullImage(displayImages[2])}
                                />
                            ) : <img src={PlanImage1} alt="Side 2" className="w-full h-full object-cover rounded-lg cursor-pointer" onClick={() => openFullImage(PlanImage1)} />}
                        </div>
                        <div className="w-1/2 overflow-hidden rounded-ee-lg relative">
                            {displayImages?.length > 0 ? displayImages[3] && (
                                <img
                                    src={displayImages[3]}
                                    alt="Side 3"
                                    className="w-full h-full object-cover rounded-lg cursor-pointer"
                                    onClick={() => openFullImage(displayImages[3])}
                                />
                            ) : <img src={PlanImage1} alt="Side 3" className="w-full h-full object-cover rounded-lg cursor-pointer" onClick={() => openFullImage(PlanImage1)} />}
                        </div>
                    </div>
                </div>
            </div>
            {/* Always show See all photos button */}
            <div className="absolute top-2 right-2 z-10">
                <button
                    className="text-[14px] leading-[20px] font-normal cursor-pointer text-white bg-[#05073C] rounded-[30px] px-[20px] py-[9px] shadow"
                    onClick={() => setIsGalleryOpen(true)}
                >
                    See all photos
                </button>
            </div>

            {/* Vertical Scrollable Gallery Modal */}
            {isGalleryOpen && (
                <div className="fixed inset-0 bg-black/75 flex items-center justify-center z-[999]">
                    <div className="bg-white rounded-lg  max-w-2xl w-full h-[90vh] overflow-y-auto relative">
                        <div className="sticky top-0 bg-white z-10 pb-2 flex justify-between items-center">
                            <h2 className="text-xl font-semibold text-[#05073C] px-4 py-2">Gallery</h2>
                            <button
                                className="text-[#05073C] bg-gray-100 rounded-full w-8 h-8 flex items-center justify-center"
                                onClick={() => setIsGalleryOpen(false)}
                            >
                                &times;
                            </button>
                        </div>

                        {/* Vertical scrollable content */}
                        <div className="flex flex-col gap-4 p-4">
                            {(images && images.length > 0 ? images : [PlanImage1]).map((img, idx) => (
                                <div key={idx} className="w-full">
                                    <img
                                        src={img}
                                        alt={`Gallery ${idx + 1}`}
                                        className="w-full h-auto object-cover rounded-lg shadow-sm cursor-pointer"
                                        onClick={() => openFullImage(img)}
                                    />
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}

            {/* Full Image Modal */}
            {selectedImage && (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[1000]" onClick={closeFullImage}>
                    <div className="relative max-w-[90vw] max-h-[90vh]">
                        <img
                            src={selectedImage}
                            alt="Full View"
                            className="max-w-full max-h-[90vh] object-contain"
                            onClick={(e) => e.stopPropagation()}
                        />
                        <button
                            className="absolute top-2 right-2 text-white bg-black/50 rounded-full w-10 h-10 flex items-center justify-center cursor-pointer"
                            onClick={closeFullImage}
                        >
                            &times;
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ImageGrid;

