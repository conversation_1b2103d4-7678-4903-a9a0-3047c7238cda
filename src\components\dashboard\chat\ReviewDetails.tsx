import { useDispatch, useSelector } from "react-redux";
import { addMessage, setLoading } from "../../../store/features/chat/chatSlice";
import { useSendMessageMutation } from "../../../store/features/chat/chatApi";
import { RootState } from "../../../store/store";

interface TravellerDetails {
     first_name: string;
     last_name: string;
}

interface Content {
     tour: {
          tour_name: string;
     };
     traveller_details: TravellerDetails[];
     duration: {
          duration: number;
          duration_type: string;
          start_date: string;
          end_date: string;
     };
     booking_currency: string;
     booking_amount: number;
     total_amount: number;
     traveller_numbers: {
          adult_price_text: string;
          child_price_text: string;
          infant_price_text: string;
     }
}

function extractAmounts(lines: string | string[]): number[] {
     const arr = Array.isArray(lines) ? lines : [lines];
     return arr?.map(line => {
          const match = line.match(/=\s*(\d+)/);
          return match ? Number(match[1]) : null;
     }).filter((num): num is number => num !== null);
}

const formatDate = (dateString: string) => {
     if (!dateString) return "";
     // Ensure the date is in YYYY-MM-DD format
     const [year, month, day] = dateString.split("-");
     if (!year || !month || !day) return "";
     const date = new Date(Number(year), Number(month) - 1, Number(day));
     return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
     });
};


const ReviewDetails = ({ content }: { content: Content }) => {
     const dispatch = useDispatch();
     const threadUid = useSelector((state: RootState) => state.chat.threadUid);
     const [sendMessage] = useSendMessageMutation();
     const handleSubmit = async () => {
          const messageText = "reviewed";
          // Add user message to chat
          dispatch(addMessage({
               content: messageText,
               sender: "user"
          }));

          // Set loading to true
          dispatch(setLoading(true));

          // Create API payload with context_data
          const messagePayload = {
               message: messageText,
               thread_uid: threadUid || "",
               context_data: {
                    intent: "booking",
                    action: "handle_booking",
                    data: {}
               }
          };

          try {
               await sendMessage(messagePayload).unwrap();
          } catch (error) {
               console.error("Error submitting review:", error);
          } finally {
               dispatch(setLoading(false));
          }
     };

     return (
          <div className="max-w-[740px] mb-[30px]">
               <div className="mt-[16px] ms-[45px] max-w-[580px] bg-[#ffffff] rounded-[24px] py-[20px] px-[18px] border border-[#E5E7EB]">
                    <h3 className="mb-[15px] sm:mb-[23px] text-[16px] sm:text-[20px] leading-[20px] sm:leading-[24px] font-semibold text-[#05073C]">Please review your booking details</h3>
                    <p className="mb-[28px] bg-[#E7ECF9] p-[7px] rounded-[5px] text-[14px] text-[#05073C]">Here's a summary of your booking. Please check all details carefully before proceeding to payment.</p>
                    <div>
                         <h5 className="text-[16px] font-semibold text-[#05073C] mb-[8px]">{content?.tour?.tour_name}</h5>
                         <p className="text-[14px] text-[#05073C]">
                              Travel Dates: {formatDate(content.duration.start_date)} - {formatDate(content.duration.end_date)}
                         </p>
                    </div>
                    <hr className="my-[14px] border-[#E5E7EB]" />
                    <div>
                         <h5 className="text-[16px] font-semibold text-[#05073C] mb-[8px]">Tarvellers</h5>
                         {content?.traveller_details?.map((details, index) => (
                              <p key={index} className="text-[14px] text-[#05073C] mb-[4px]">{index + 1}. {details?.first_name} {details?.last_name}</p>
                         ))}
                    </div>
                    <hr className="my-[14px] border-[#E5E7EB]" />
                    <div>
                         <h5 className="text-[16px] font-semibold text-[#05073C] mb-[8px]">Your Total</h5>
                         <div className="flex justify-between items-center gap-[10px] mb-[10px]">
                              <div className="flex flex-col gap-2">
                                   <p className="text-[14px] text-[#05073C]">{content?.traveller_numbers?.adult_price_text && "Adult: " + content?.traveller_numbers?.adult_price_text}</p>
                                   <p className="text-[14px] text-[#05073C]">{content?.traveller_numbers?.child_price_text && "Child: " + content?.traveller_numbers?.child_price_text}</p>
                                   <p className="text-[14px] text-[#05073C]">{content?.traveller_numbers?.infant_price_text && "Infant: " + content?.traveller_numbers?.infant_price_text}</p>
                              </div>

                              <div className="flex flex-col gap-2">
                                   <p className="text-[14px] text-[#05073C]">
                                        {content?.traveller_numbers?.adult_price_text &&
                                             (<span> ৳ {extractAmounts(content.traveller_numbers.adult_price_text).join(", ")}</span>)}
                                   </p>
                                   <p className="text-[14px] text-[#05073C]">
                                        {content?.traveller_numbers?.child_price_text &&
                                             (<span> ৳ {extractAmounts(content.traveller_numbers.child_price_text).join(", ")}</span>)}
                                   </p>
                                   <p className="text-[14px] text-[#05073C]">
                                        {content?.traveller_numbers?.infant_price_text &&
                                             (<span> ৳ {extractAmounts(content.traveller_numbers.infant_price_text).join(", ")}</span>)}
                                   </p>
                              </div>

                              {/* <p className="text-[14px] text-[#05073C]">৳ {content?.booking_amount}</p> */}
                         </div>
                    </div>
                    <hr className="my-[14px] border-[#E5E7EB]" />
                    <div className="flex justify-between items-center gap-[10px] mb-[30px]">
                         <h5 className="text-[16px] font-semibold text-[#05073C] mb-[8px]">Total Amount</h5>
                         <p className="text-[14px] text-[#0D3FC6] font-semibold">৳ {content?.booking_amount}</p>
                    </div>
                    <div className="flex flex-wrap sm:flex-nowrap gap-[11px] items-center mb-[10px]">
                         {/* <button className="w-full sm:w-auto bg-[#E7ECF9] text-[#0D3FC6] py-[18px] px-[30px] rounded-[8px] cursor-pointer text-[18px] leading-[16px] font-semibold">Edit Details</button> */}
                         <button
                              type="button"
                              onClick={handleSubmit}
                              className="w-full sm:w-auto bg-[#0D3FC6] text-white py-[18px] px-[28px] rounded-[8px] cursor-pointer text-[18px] leading-[16px] font-semibold"
                         >
                              Submit
                         </button>
                    </div>
               </div>
          </div>
     )
}

export default ReviewDetails;
