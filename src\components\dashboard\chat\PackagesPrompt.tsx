import { useState, useEffect } from "react";
import { useTourTags } from '../../../utils/useTourTags';
import TourTagChips from '../../common/TourTagChips';
// Card component for packages, so hooks can be used
const PackageCard = ({ destination, handleViewDetails, cardKey }: { destination: any, handleViewDetails: (tour_id: string) => void, cardKey: string }) => {
  const { tags, loading: tagsLoading } = useTourTags(destination.tour_id);
  return (
    <div key={cardKey} className="w-full max-w-[298px] bg-white shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1)] cursor-pointer rounded-[12px] overflow-hidden border border-[#E5E7EB] min-w-[290px] sm:min-w-[320px] flex flex-col">
      <div className="h-48 relative overflow-hidden">
        <img
          src={destination?.tour_gallery_images[0] || dummyImage}
          alt="Bali Beach"
          className="w-full h-full object-cover object-top"
        />
        {destination?.featured && (
          <div className="absolute top-2 left-4 text-white bg-[#1249CC] px-2 py-1 rounded-full text-[12px] flex items-center gap-1">
            <div className="flex items-center gap-1">
              <span>Verified</span>
            </div>
          </div>
        )}
      </div>
      <div className="p-4 flex flex-col flex-1 min-h-0">
        <div>
          <h3 className="text-lg font-semibold text-ellipsis line-clamp-1 mb-2">{destination.tour_title}</h3>
          <p className="ellipsis line-clamp-3 text-[#4B5563] text-[14px] leading-[20px]" dangerouslySetInnerHTML={{ __html: destination.tour_description.replace(/&nbsp;/g, ' ') }}></p>
        </div>
        {/* Tour tags chips */}
        <div className="flex gap-2 mt-1 mb-1 overflow-x-auto flex-nowrap scrollbar-thin scrollbar-thumb-gray-300 max-w-full">
          {tagsLoading ? (
            <span className="text-xs text-gray-400">Loading tags...</span>
          ) : (
            <TourTagChips tags={tags} />
          )}
        </div>
        <div className="flex-1 flex flex-col justify-end min-h-0">
          <div className="flex items-center justify-between gap-[8px] py-2">
            <div className="flex flex-col justify-between gap-[8px]">
              <div className="flex items-center gap-[8px] ">
                <img src={LocationIcon} className="" />
                <span className="text-[14px] text-[#4B5563] truncate max-w-[120px]">{destination.location.address}</span>
              </div>
              <div className="flex items-center gap-[8px]">
                <img src={TimeIcon} className="" />
                <span className="text-[14px] text-[#4B5563]">{destination.duration} days</span>
              </div>
            </div>
            <div className="flex flex-col  gap-[4px] justify-between items-end">
              <div className="flex items-center gap-[6px]">
                <img src="/group.svg" className="w-4 h-4" />
                <span className="text-[14px] text-[#4B5563]">{destination?.group_size}</span>
              </div>
              <div className="flex gap-1 justify-end items-end">
                <span className="block text-[10px] text-[#4B5563]">Starts from:</span>
                <span className="block text-[16px] text-[#1249CC] font-bold">৳ {Number(destination.adult_price || 0).toLocaleString()}</span>
              </div>
            </div>
          </div>
          <div>
            <button className="cursor-pointer bg-[#E7ECF9] text-[#0D3FC6] rounded-[4px] text[14px] font-semibold py-[8px] px-[10px] w-full" onClick={() => handleViewDetails(destination.tour_id)}>View Details</button>
          </div>
        </div>
      </div>
    </div>
  );
};
import { useGetPackagesMutation } from "../../../store/features/tours/toursApi";
import { useAppSelector } from "../../../store/hooks";
import dummyImage from "../../../assets/plan-img1.png"
import { useNavigate, useParams } from "react-router-dom";
import React from "react";
import LocationIcon from "../../../assets/location-icon.svg"
import TimeIcon from "../../../assets/time-icon.svg"
import ViewMoreButton from "./ViewMore";
import Cookies from "js-cookie";
import SignInModal from "./SignInModal";
import SignUpModal from "./SignUpModal";
import VerifyPhoneNumber from "../../../pages/auth/VerifyPhoneNumber";

interface PackagesPromptProps {
     packages: {
          featured: { id: string, image: string, tour_title: string, tour_description: string, location: { address: string }, duration: string, adult_price: string, tour_id: string, tour_gallery_images: string[], featured: boolean, group_size: string }[];
          cheapest: { id: string, image: string, tour_title: string, tour_description: string, location: { address: string }, duration: string, adult_price: string, tour_id: string, tour_gallery_images: string[], featured: boolean, group_size: string }[];
     },
     meta_details: { pagination: { has_next: boolean } }
}

const PackagesPrompt: React.FC<PackagesPromptProps> = ({ packages: propPackages, meta_details }: PackagesPromptProps) => {
     const [activeTab, setActiveTab] = useState("cheapest");
     const [showSignInModal, setShowSignInModal] = useState(false);
     const [showSignUpModal, setShowSignUpModal] = useState(false);
     const [showVerifyPhoneOtpModal, setShowVerifyPhoneOtpModal] = useState(false);
     const [getPackages] = useGetPackagesMutation();
     const { state, country } = useAppSelector((state) => state.tours);
     const chatState = useAppSelector((state) => state.chat);
     const navigate = useNavigate();
     const { featured, cheapest } = propPackages;
     const [threadId, setThreadId] = useState<string | null>(null);
     const { threadUid: urlThreadUid } = useParams();
     const isAuthenticated = !!Cookies.get('accessToken');
     const { userDetails } = useAppSelector((state) => state.auth);

     useEffect(() => {
          // First priority: URL parameter
          if (urlThreadUid) {
               setThreadId(urlThreadUid);
               return;
          }

          // Second priority: Redux store
          if (chatState.threadUid) {
               setThreadId(chatState.threadUid);
               return;
          }

          // If we have a threadId from previous effect runs, keep it
          if (!threadId && localStorage.getItem('threadUid')) {
               setThreadId(localStorage.getItem('threadUid'));
          }
     }, [urlThreadUid, chatState.threadUid, threadId]);

     // Persist threadId to localStorage for page reloads
     useEffect(() => {
          if (threadId) {
               localStorage.setItem('threadUid', threadId);
          }
     }, [threadId]);

     useEffect(() => {
          if (propPackages) {
               return;
          }
          const fetchPackages = async () => {
               try {
                    const response = await getPackages({ country, state });
                    if (response.error) {
                         return;
                    }
               } catch (error) {
                    console.log(error);
               }
          };

          fetchPackages();
     }, [country, state, getPackages, propPackages]);

     const handleViewDetails = (tour_id: string) => {
          if (isAuthenticated) {
               if (userDetails?.otp_verify === true) {
                    if (threadId) {
                         navigate(`/plan-details/${threadId}/${tour_id}`);
                    }
               }
               else {
                    setShowVerifyPhoneOtpModal(true);
               }
          } else {
               setShowSignInModal(true);
               return;
          }
     }

     const handleSignUp = () => {
          setShowSignUpModal(true);
          setShowSignInModal(false);
     }

     const handleSignIn = () => {
          setShowSignInModal(true);
          setShowSignUpModal(false);
     }

     return (
          <div>
               <div className="bg-[#E7ECF9] rounded-[8px] p-[5px] w-[300px] sm:w-[320px] flex justify-around ml-[19px]">
                    <button
                         className={`text-[14px] sm:text-[16px] px-[10px] sm:px-[20px] py-[6px] sm:py-[10px] w-full text-[#05073C] leading-[24px] font-normal cursor-pointer rounded-[8px]  ${activeTab === "cheapest"
                              ? "bg-[#FFFFFF] shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1)] font-semibold"
                              : ""
                              }`}
                         onClick={() => setActiveTab("cheapest")}
                    >
                         Cheapest
                    </button>
                    <button
                         className={`text-[14px] sm:text-[16px] px-[10px] sm:px-[20px] py-[6px] sm:py-[10px] w-full text-[#05073C] leading-[24px] font-normal cursor-pointer rounded-[8px]  ${activeTab === "featured"
                              ? "bg-[#FFFFFF] w-full shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1)] font-semibold"
                              : ""
                              }`}
                         onClick={() => setActiveTab("featured")}
                    >
                         Verified
                    </button>
               </div>

               {/* tabs content */}
               {activeTab === "cheapest" && (
                    <div className="py-4 mb-[20px] px-[20px]">
                         {
                              cheapest?.length > 0 ? <div>
                                   <div className="w-[100%] overflow-x-auto scrollbar-hide" >
                                        <div className="flex flex-col sm:flex-row gap-[16px]">
                                             <div className="flex flex-col sm:flex-row gap-[16px]">
                                                  {cheapest?.map((destination: any) => (
                                                    <PackageCard key={`cheapest-${destination.id}`} destination={destination} handleViewDetails={handleViewDetails} cardKey={`cheapest-${destination.id}`} />
                                                  ))}
                                             </div>
                                        </div>

                                   </div>
                                   {meta_details?.pagination?.has_next && (
                                        <ViewMoreButton meta_details={meta_details} threadUid={threadId} />
                                   )}

                              </div> :
                                   <div className="text-center text-[#4B5563] text-[14px] leading-[20px] mt-5">No cheapest packages found</div>
                         }
                    </div>
               )}

               {activeTab === "featured" && (
                    <div className="py-4 mb-[20px] px-[20px]">
                         {
                              featured?.length > 0 ? <div>
                                   <div className="w-[100%] overflow-x-auto scrollbar-hide" >
                                        <div className="flex flex-col sm:flex-row gap-[16px]">
                                             <div className="flex flex-col sm:flex-row gap-[16px]">
                                                  {featured?.map((destination: any) => (
                                                    <PackageCard key={`featured-${destination.id}`} destination={destination} handleViewDetails={handleViewDetails} cardKey={`featured-${destination.id}`} />
                                                  ))}
                                             </div>
                                        </div>

                                   </div>
                                   {meta_details?.pagination?.has_next && (
                                        <ViewMoreButton meta_details={meta_details} threadUid={threadId} />
                                   )}

                              </div> :
                                   <div className="text-center text-[#4B5563] text-[14px] leading-[20px] mt-5">No verified packages found</div>
                         }
                    </div>
               )}

               {showSignInModal && <SignInModal onClose={() => setShowSignInModal(false)} onSignUpClick={handleSignUp} />}
               {showSignUpModal && <SignUpModal onClose={() => setShowSignUpModal(false)} onSignInClick={handleSignIn} />}
               {showVerifyPhoneOtpModal && (
                    <VerifyPhoneNumber
                         isModal={true}
                         onModalClose={() => setShowVerifyPhoneOtpModal(false)}
                         onModalSuccess={() => {
                              setShowVerifyPhoneOtpModal(false);
                              window.location.reload();
                         }}
                    />
               )}
          </div>
     )
}

export default PackagesPrompt;
