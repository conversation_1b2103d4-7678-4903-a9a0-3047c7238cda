import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import Cookies from 'js-cookie';

export const authApi = createApi({
     reducerPath: "authApi",
     baseQuery: fetchBaseQuery({
          baseUrl: `${import.meta.env.VITE_API_URL}/auth`,
          credentials: "include",
          prepareHeaders: (headers, { endpoint }) => {
               const token = Cookies.get('accessToken');

               if (token && endpoint !== 'loginUser' && endpoint !== 'registerUser') {
                    try {
                         const parts = token.split('.');
                         if (parts.length === 3) {
                              JSON.parse(atob(parts[1]));
                              headers.set('Authorization', `Bearer ${token}`);
                         }
                         else {
                              Cookies.remove('accessToken');
                              window.location.reload();
                         }

                         // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    } catch (e: any) {
                         console.log(e, 'error');
                         Cookies.remove('accessToken');
                    }
               }

               return headers;
          },
     }),
     tagTypes: ['Auth'],

     endpoints: (builder) => ({
          loginUser: builder.mutation({
               query: (credentials) => ({
                    url: "/login/",
                    method: "POST",

                    body: credentials,
               }),
          }),

          registerUser: builder.mutation({
               query: (credentials) => ({
                    url: "/register/",
                    method: "POST",
                    body: credentials,
               }),
          }),

          changePassword: builder.mutation({
               query: (credentials) => ({
                    url: "/change-password",
                    method: "POST",
                    body: credentials,
               }),
          }),

          requestOtp: builder.mutation({
               query: (credentials) => ({
                    url: "/password-reset/request-otp/",
                    method: "POST",
                    body: credentials,
               }),
          }),

          verifyOtp: builder.mutation({
               query: (credentials) => ({
                    url: "/password-reset/verify-otp/",
                    method: "POST",
                    body: credentials,
               }),
          }),

          setNewPassword: builder.mutation({
               query: (credentials) => ({
                    url: "/password-reset/change-password/",
                    method: "POST",
                    body: credentials,
               }),
          }),

          socialLogin: builder.mutation({
               query: (credentials) => ({
                    url: "/",
                    method: "POST",
                    body: credentials,
               }),
          }),

          getUser: builder.query({
               query: () => ({
                    url: "/profile/",
               }),
          }),

          resetPassword: builder.mutation({
               query: (data) => ({
                    url: "/change-password/",
                    method: "POST",
                    body: data,
               }),
          }),

          phoneNumberOtp: builder.mutation({
               query: (data) => (
                    {
                         url: "/phone/send-otp/",
                         method: "POST",
                         body: data,
                    }
               )
          }),

          verifyPhoneNumberOtp: builder.mutation({
               query: (data) => (
                    {
                         url: "/phone/verify-otp/",
                         method: "POST",
                         body: data
                    }
               )
          })
     }),
});

export const { useLoginUserMutation, useRegisterUserMutation, useChangePasswordMutation, useRequestOtpMutation, useVerifyOtpMutation, useSetNewPasswordMutation, useSocialLoginMutation, useGetUserQuery, useResetPasswordMutation, usePhoneNumberOtpMutation, useVerifyPhoneNumberOtpMutation } = authApi;
