import { useGetContactDetailsQuery } from "../../store/features/admin/adminApi";
import AdminPackagesSkeleton from "../../components/skeletons/AdminPackagesSkeleton";
import { useState, useMemo } from "react";

interface ContactDetail {
     id: number;
     username: string;
     first_name: string;
     last_name: string;
     email: string;
     phone: string;
     subject: string;
     message: string;
     created_at: string;
     user: number;
}

const AdminContactDetails = () => {
     const { data: contactDetails, isLoading } = useGetContactDetailsQuery({});
     const [selectedContact, setSelectedContact] = useState<ContactDetail | null>(null);
     const [isModalOpen, setIsModalOpen] = useState(false);
     const [searchQuery, setSearchQuery] = useState("");
     const [currentPage, setCurrentPage] = useState(1);
     const itemsPerPage = 10;

     const filteredContacts = useMemo(() => {
          if (!contactDetails?.data) return [];
          return contactDetails.data.filter((contact: ContactDetail) => {
               const searchTerm = searchQuery.toLowerCase();
               return (
                    contact.username.toLowerCase().includes(searchTerm) ||
                    contact.first_name.toLowerCase().includes(searchTerm) ||
                    contact.last_name.toLowerCase().includes(searchTerm) ||
                    contact.email.toLowerCase().includes(searchTerm) ||
                    contact.subject.toLowerCase().includes(searchTerm)
               );
          });
     }, [contactDetails?.data, searchQuery]);

     const totalPages = Math.ceil(filteredContacts.length / itemsPerPage);
     const paginatedContacts = useMemo(() => {
          const startIndex = (currentPage - 1) * itemsPerPage;
          return filteredContacts.slice(startIndex, startIndex + itemsPerPage);
     }, [filteredContacts, currentPage]);

     const formatDate = (dateString: string) => {
          const date = new Date(dateString);
          return date.toLocaleDateString('en-US', {
               year: 'numeric',
               month: 'short',
               day: 'numeric',
               hour: '2-digit',
               minute: '2-digit'
          });
     };

     const handleOpenModal = (contact: ContactDetail) => {
          setSelectedContact(contact);
          setIsModalOpen(true);
     };

     const getPageNumbers = () => {
          const pageNumbers = [];
          const showEllipsis = totalPages > 5;

          if (showEllipsis) {
               // Always show first page
               pageNumbers.push(1);

               if (currentPage <= 3) {
                    // If current page is near the start
                    pageNumbers.push(2, 3);
                    pageNumbers.push('...');
                    pageNumbers.push(totalPages);
               } else if (currentPage >= totalPages - 2) {
                    // If current page is near the end
                    pageNumbers.push('...');
                    pageNumbers.push(totalPages - 2, totalPages - 1, totalPages);
               } else {
                    // If current page is in the middle
                    pageNumbers.push('...');
                    pageNumbers.push(currentPage - 1, currentPage, currentPage + 1);
                    pageNumbers.push('...');
                    pageNumbers.push(totalPages);
               }
          } else {
               // Show all pages if total pages are 5 or less
               for (let i = 1; i <= totalPages; i++) {
                    pageNumbers.push(i);
               }
          }

          return pageNumbers;
     };

     const renderPaginationButtons = () => (
          <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
               <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
               >
                    <span className="sr-only">Previous</span>
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                         <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
               </button>

               {getPageNumbers().map((page, index) => (
                    page === '...' ? (
                         <span
                              key={`ellipsis-${index}`}
                              className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                         >
                              ...
                         </span>
                    ) : (
                         <button
                              key={`page-${page}`}
                              onClick={() => setCurrentPage(Number(page))}
                              className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${currentPage === page
                                   ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                   : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                   }`}
                         >
                              {page}
                         </button>
                    )
               ))}

               <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
               >
                    <span className="sr-only">Next</span>
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                         <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
               </button>
          </nav>
     );

     return (
          <>
                    <>
                         {/* Search Input */}
                         <div className="mb-4 w-full sm:w-72">
                              <div className="relative">
                                   <input
                                        type="text"
                                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-sm"
                                        placeholder="Search contacts..."
                                        value={searchQuery}
                                        onChange={(e) => {
                                             setSearchQuery(e.target.value);
                                             setCurrentPage(1); // Reset to first page on search
                                        }}
                                   />
                                   <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                                        <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                             <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                        </svg>
                                   </div>
                              </div>
                         </div>

                         <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                              {/* Make table horizontally scrollable on small screens */}
                              <div className="overflow-x-auto">
                                   {isLoading ? (
                                        <AdminPackagesSkeleton />
                                   ) : (
                                        <>
                                             <table className="w-full divide-y divide-gray-200">
                                                  <thead>
                                                       <tr className="bg-gray-50">
                                                            <th scope="col" className="px-2 py-2 sm:px-6 sm:py-3 text-left text-[10px] sm:text-xs lg:text-sm font-semibold text-gray-600 uppercase tracking-wider whitespace-nowrap">
                                                                 User Info
                                                            </th>
                                                            <th scope="col" className="px-2 py-2 sm:px-6 sm:py-3 text-left text-[10px] sm:text-xs lg:text-sm font-semibold text-gray-600 uppercase tracking-wider whitespace-nowrap">
                                                                 Contact
                                                            </th>
                                                            <th scope="col" className="hidden sm:table-cell px-2 py-2 sm:px-6 sm:py-3 text-left text-[10px] sm:text-xs lg:text-sm font-semibold text-gray-600 uppercase tracking-wider whitespace-nowrap">
                                                                 Date
                                                            </th>
                                                            <th scope="col" className="px-2 py-2 sm:px-6 sm:py-3 text-left text-[10px] sm:text-xs lg:text-sm font-semibold text-gray-600 uppercase tracking-wider whitespace-nowrap">
                                                                 Messages
                                                            </th>
                                                       </tr>
                                                  </thead>
                                                  <tbody className="bg-white divide-y divide-gray-200">
                                                       {paginatedContacts.map((contact: ContactDetail) => (
                                                            <tr key={contact.id} className="hover:bg-gray-50/50 transition-colors">
                                                                 <td className="px-2 py-2 sm:px-6 sm:py-3 text-[10px] sm:text-xs lg:text-sm">
                                                                      <div className="flex flex-col">
                                                                           <span className="font-medium text-gray-900 break-words line-clamp-1 sm:line-clamp-none">
                                                                                {contact.first_name} {contact.last_name}
                                                                           </span>
                                                                           <span className="text-gray-500 break-all line-clamp-1 sm:line-clamp-none">
                                                                                @{contact.username}
                                                                           </span>
                                                                      </div>
                                                                 </td>
                                                                 <td className="px-2 py-2 sm:px-6 sm:py-3 text-[10px] sm:text-xs lg:text-sm">
                                                                      <div className="flex flex-col">
                                                                           <span className="text-gray-900 break-all line-clamp-1 sm:line-clamp-none">
                                                                                {contact.email}
                                                                           </span>
                                                                           <span className="text-gray-500 line-clamp-1 sm:line-clamp-none">
                                                                                {contact.phone}
                                                                           </span>
                                                                      </div>
                                                                 </td>
                                                                 <td className="hidden sm:table-cell px-2 py-2 sm:px-6 sm:py-3 text-[10px] sm:text-xs lg:text-sm">
                                                                      <span className="text-gray-500">
                                                                           {formatDate(contact.created_at)}
                                                                      </span>
                                                                 </td>
                                                                 <td className="px-2 py-2 sm:px-6 sm:py-3 text-[10px] sm:text-xs lg:text-sm">
                                                                      <button
                                                                           onClick={() => handleOpenModal(contact)}
                                                                           className="inline-flex items-center px-2 py-1 sm:px-3 sm:py-1.5 border border-transparent text-[10px] sm:text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 w-full sm:w-auto justify-center"
                                                                      >
                                                                           View
                                                                           <span className="hidden sm:inline ml-1">Messages</span>
                                                                      </button>
                                                                 </td>
                                                            </tr>
                                                       ))}
                                                  </tbody>
                                             </table>

                                             {/* Pagination */}
                                             <div className="px-2 py-3 sm:px-6 sm:py-4 flex flex-col sm:flex-row sm:items-center sm:justify-between border-t border-gray-200">
                                                  <div className="flex-1 flex justify-between sm:hidden">
                                                       <button
                                                            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                                            disabled={currentPage === 1}
                                                            className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                                       >
                                                            Previous
                                                       </button>
                                                       <button
                                                            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                                            disabled={currentPage === totalPages}
                                                            className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                                       >
                                                            Next
                                                       </button>
                                                  </div>
                                                  <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between w-full">
                                                       <div>
                                                            <p className="text-sm text-gray-700">
                                                                 Showing <span className="font-medium">{((currentPage - 1) * itemsPerPage) + 1}</span> to{' '}
                                                                 <span className="font-medium">
                                                                      {Math.min(currentPage * itemsPerPage, filteredContacts.length)}
                                                                 </span> of{' '}
                                                                 <span className="font-medium">{filteredContacts.length}</span> results
                                                            </p>
                                                       </div>
                                                       <div>
                                                            {renderPaginationButtons()}
                                                       </div>
                                                  </div>
                                             </div>
                                        </>
                                   )}
                              </div>
                         </div>
                    </>

               {/* Modal */}
               {isModalOpen && selectedContact && (
                    <div className="fixed inset-0 bg-black/70 bg-opacity-50 flex items-center justify-center z-[999] p-2 sm:p-4">
                         <div className="bg-white rounded-lg p-3 sm:p-6 w-full max-w-[98%] sm:max-w-lg max-h-[95vh] overflow-y-auto">
                              <div className="flex justify-between items-center mb-4">
                                   <h3 className="text-sm sm:text-lg font-medium text-gray-900">Contact Details</h3>
                                   <button
                                        onClick={() => setIsModalOpen(false)}
                                        className="text-gray-400 hover:text-gray-500 p-1"
                                   >
                                        <span className="sr-only">Close</span>
                                        <svg className="h-4 w-4 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                             <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                   </button>
                              </div>
                              <div className="space-y-4">
                                   <div>
                                        <h4 className="text-xs sm:text-sm font-medium text-gray-900">Subject</h4>
                                        <p className="mt-1 text-xs sm:text-sm text-gray-500 break-words">{selectedContact.subject}</p>
                                   </div>
                                   <div>
                                        <h4 className="text-xs sm:text-sm font-medium text-gray-900">Message</h4>
                                        <p className="mt-1 text-xs sm:text-sm text-gray-500 break-words whitespace-pre-wrap">{selectedContact.message}</p>
                                   </div>
                              </div>
                              <div className="mt-6">
                                   <button
                                        onClick={() => setIsModalOpen(false)}
                                        className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-3 py-1.5 sm:px-4 sm:py-2 bg-white text-xs sm:text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                   >
                                        Close
                                   </button>
                              </div>
                         </div>
                    </div>
               )}
          </>
     );
}

export default AdminContactDetails;