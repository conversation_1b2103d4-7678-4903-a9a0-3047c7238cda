import userIcon from '../../../assets/user-icon.svg';
import emailIcon from '../../../assets/email-icon.svg';
import LockIcon from '../../../assets/lock-icon.svg';
import { useForm } from "react-hook-form";
import { useRegisterUserMutation, usePhoneNumberOtpMutation, useVerifyPhoneNumberOtpMutation } from "../../../store/features/auth/authApi";
import { toast } from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { useState, useRef } from "react";
import { useDispatch } from 'react-redux';
import { setRegistrationData, clearRegistrationData } from '../../../store/features/auth/authSlice';
import SocialLogin from '../../auth/SocialLogin';

interface SignUpModalProps {
  onClose?: () => void;
  onSignInClick?: () => void;
}

const SignUpModal: React.FC<SignUpModalProps> = ({ onClose, onSignInClick }) => {
  const dispatch = useDispatch();
  const [registerUser] = useRegisterUserMutation();
  const [sendOtp, { isLoading: isSendingOtp }] = usePhoneNumberOtpMutation();
  const [verifyOtp, { isLoading: isVerifyingOtp }] = useVerifyPhoneNumberOtpMutation();
  const { register, handleSubmit, watch, formState: { errors } } = useForm();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showOtpForm, setShowOtpForm] = useState(false);
  const [verificationId, setVerificationId] = useState<string>('');
  const [otpValues, setOtpValues] = useState<{ [key: string]: string }>({});
  const otpInputs = useRef<(HTMLInputElement | null)[]>([]);

  const navigate = useNavigate();

  const handleOtpChange = (index: number, value: string) => {
    if (!/^\d*$/.test(value)) return;

    const newOtpValues = { ...otpValues, [index]: value };
    setOtpValues(newOtpValues);

    if (value && index < 5) {
      otpInputs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !otpValues[index] && index > 0) {
      otpInputs.current[index - 1]?.focus();
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onSubmit = async (data: any) => {
    try {
      const userData = {
        username: data.name,
        email: data.email,
        phone: data.phone,
        password: data.password,
      };

      dispatch(setRegistrationData(userData));

      const otpResponse = await sendOtp({ phone: data.phone });

      if ('error' in otpResponse) {
        // @ts-expect-error error handling
        toast.error(otpResponse.error.data?.msg || "Failed to send OTP");
        return;
      }

      const verification_id = otpResponse.data?.verification_id;
      if (!verification_id) {
        toast.error("Failed to get verification ID");
        return;
      }

      setVerificationId(verification_id);
      toast.success("OTP sent successfully!");
      setShowOtpForm(true);

    } catch (error) {
      console.log("Error: ", error);
      toast.error("Failed to send OTP");
    }
  };

  const onOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const otpString = Object.values(otpValues).join('');

      const verifyResponse = await verifyOtp({
        phone: watch('phone'),
        otp: otpString,
        verification_id: verificationId
      });

      if ('error' in verifyResponse) {
        // @ts-expect-error error handling
        toast.error(verifyResponse.error.data?.msg || "Invalid OTP");
        return;
      }

      const verificationToken = verifyResponse?.data?.verification_token;
      const userData = {
        username: watch('name'),
        email: watch('email'),
        phone: watch('phone'),
        password: watch('password'),
        verification_token: verificationToken
      };

      const response = await registerUser(userData);

      if ('error' in response) {
        // @ts-expect-error error handling
        toast.error(response.error.data?.msg || "Registration failed");
        return;
      }

      toast.success(response?.data?.msg || "Account created successfully");
      dispatch(clearRegistrationData());
      if (onClose) onClose();
      navigate("/");

    } catch (error) {
      console.log("Error: ", error);
      toast.error("Registration failed");
    }
  };

  const handleResendOtp = async () => {
    try {
      const userData = {
        phone: watch('phone')
      };

      const otpResponse = await sendOtp(userData);

      if ('error' in otpResponse) {
        // @ts-expect-error error handling
        toast.error(otpResponse.error.data?.msg || "Failed to resend OTP");
        return;
      }

      const verification_id = otpResponse.data?.verification_id;
      if (!verification_id) {
        toast.error("Failed to get verification ID");
        return;
      }

      setVerificationId(verification_id);
      toast.success("OTP resent successfully!");
      // Clear existing OTP inputs
      setOtpValues({});

    } catch (error) {
      console.log("Error: ", error);
      toast.error("Failed to resend OTP");
    }
  };

  return (
    <div>
      <div className="w-full h-full fixed inset-0 z-[9999]">
        <div className="bg-[#00000080] h-full w-full absolute top-0 left-0"></div>
        <div className="px-[10px] sm:px-[20px] fixed top-1/2 left-1/2 z-[100] -translate-x-1/2 -translate-y-1/2 max-w-[745px] w-full">
          <div className="bg-white rounded-[20px]">
            <div className="flex justify-between items-center px-[15px] py-[10px] sm:px-[25px] sm:py-[15px] border-b border-[#E7ECF9]">
              <h3 className="text-[20px] sm:text-[28px] font-semibold text-[#05073C]">
                {showOtpForm ? 'Verify OTP' : 'Sign Up'}
              </h3>
              <button
                onClick={onClose}
                className="cursor-pointer w-[30px] sm:w-[35px] h-[30px] sm:h-[35px] bg-[#E7ECF9] rounded-full flex items-center justify-center"
              >
                <img src="/modalClose-icon.svg" alt="Close" />
              </button>
            </div>

            <div className="px-[15px] py-[10px] sm:px-[25px] sm:py-[20px] pb-[60px] max-h-[calc(100vh-100px)] overflow-y-auto">
              {showOtpForm ? (
                <>
                  <div className='text-center mb-[20px]'>
                    <p className="text-[#00000080] text-[16px]">Enter the 6 digits OTP sent to your phone</p>
                  </div>

                  <form onSubmit={onOtpSubmit}>
                    <div className="mb-[24px]">
                      <div className="flex justify-between gap-2">
                        {[...Array(6)].map((_, index) => (
                          <input
                            key={index}
                            type="text"
                            maxLength={1}
                            ref={el => { otpInputs.current[index] = el }}
                            className="w-12 h-12 text-center text-xl font-semibold border border-gray-300 rounded-[8px] focus:outline-none focus:border-[#0D3FC6]"
                            value={otpValues[index] || ''}
                            onChange={(e) => handleOtpChange(index, e.target.value)}
                            onKeyDown={(e) => handleKeyDown(index, e)}
                          />
                        ))}
                      </div>
                    </div>

                    <div className="text-center mb-[12px]">
                      <button
                        type="button"
                        onClick={handleResendOtp}
                        className="text-[#0D3FC6] text-sm font-medium hover:underline"
                      >
                        Resend OTP
                      </button>
                    </div>

                    <div className="text-center mb-[12px] pt-[10px]">
                      <button
                        type="submit"
                        className="bg-gradient-to-r from-[#0D3FC6] to-[#3793FF] text-white py-[16px] px-[45px] rounded-[8px] font-medium hover:bg-blue-700 transition-colors cursor-pointer !rounded-button whitespace-nowrap text-[14px] leading-[18px] uppercase"
                        disabled={isVerifyingOtp}
                      >
                        {isVerifyingOtp ? "Verifying..." : "Verify OTP"}
                      </button>
                    </div>
                  </form>
                </>
              ) : (
                <>
                  <p className="text-[14px] sm:text-[16px] font-semibod text-[#6C6C6C] mb-[20px] sm:mb-[35px]">Join now for free or log in to unlock exclusive offers and rewards!</p>
                  <form onSubmit={handleSubmit(onSubmit)}>
                    <div className="flex flex-col sm:flex-row items-center gap-[10px]">
                      <div className="mb-[20px] sm:mb-[35px] relative w-full">
                        <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">User Name</label>
                        <div className="relative">
                          <img src={userIcon} alt="user" className='w-[30px] h-[18px] absolute sm:left-[20px] left-[10px] top-1/2 transform -translate-y-1/2' />
                          <input
                            type="text"
                            {...register("name", {
                              required: "Name is required",
                              minLength: {
                                value: 2,
                                message: "Name must be at least 2 characters"
                              },
                              maxLength: {
                                value: 50,
                                message: "Name cannot exceed 50 characters"
                              },
                              pattern: {
                                value: /^[^\s]+$/,
                                message: "Spaces are not allowed in username"
                              },
                              validate: {
                                notOnlySpaces: (value) => value.trim().length > 0 || "Username cannot be empty or contain only spaces",
                                noMultipleSpaces: (value) => !/\s\s+/.test(value) || "Multiple consecutive spaces are not allowed",
                                noSpaceAtEnds: (value) => value.trim() === value || "Username cannot start or end with spaces"
                              }
                            })}
                            className="w-full py-[17px] px-[18px] sm:ps-[60px] ps-[45px] text-[14px] font-semibold text-[#000000] leading-[18px] border border-[#0D9BC6] focus:outline-none placeholder:text-[#00000080] rounded-[8px]"
                            placeholder="Enter your user name"
                          />
                        </div>
                        {/* @ts-expect-error error */}
                        {errors.name && <span className="text-red-500 text-sm">{errors.name.message}</span>}
                      </div>
                      <div className="mb-[20px] sm:mb-[35px] relative w-full">
                        <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">Email</label>
                        <div className="relative">
                          <img src={emailIcon} alt="user" className='w-[30px] h-[20px] absolute sm:left-[20px] left-[10px] top-1/2 transform -translate-y-1/2' />
                          <input
                            type="email"
                            {...register("email", {
                              required: "Email is required",
                              pattern: {
                                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                                message: "Invalid email address"
                              },
                              validate: {
                                notEmpty: (value) => value.trim() !== '' || "Email cannot be empty",
                                noSpaces: (value) => !/\s/.test(value) || "Email cannot contain spaces"
                              }
                            })}
                            className="w-full py-[17px] px-[18px] sm:ps-[60px] ps-[45px] text-[14px] font-semibold text-[#000000] leading-[18px] border border-[#0D9BC6] focus:outline-none placeholder:text-[#00000080] rounded-[8px]"
                            placeholder="Enter your email"
                          />
                        </div>
                        {/* @ts-expect-error error */}
                        {errors.email && <span className="text-red-500 text-sm">{errors.email.message}</span>}
                      </div>
                      <div className="mb-[20px] sm:mb-[35px] relative w-full">
                        <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">Phone Number</label>
                        <div className="relative">
                          <img src="/phone.svg" alt="phone" className='w-[30px] h-[20px] absolute sm:left-[20px] left-[10px] top-1/2 transform -translate-y-1/2' />
                          <input
                            type="tel"
                            {...register("phone", {
                              required: "Phone number is required",
                              pattern: {
                                value: /^(\+?88)?01[3-9]\d{8}$/,
                                message: "Enter a valid Bangladeshi phone number (e.g. +8801XXXXXXXXX or 01XXXXXXXXX)"
                              },
                              validate: {
                                notEmpty: (value) => value.trim() !== '' || "Phone number cannot be empty",
                                noSpaces: (value) => !/\s/.test(value) || "Phone number cannot contain spaces",
                                onlyNumbers: (value) => /^\d+$/.test(value) || "Phone number can only contain digits",
                                validOperator: (value) => {
                                  const operators = ['013', '014', '015', '016', '017', '018', '019'];
                                  return operators.some(op => value.startsWith(op)) || "Invalid operator code";
                                }
                              }
                            })}
                            className="w-full py-[17px] px-[18px] sm:ps-[60px] ps-[45px] text-[14px] font-semibold text-[#000000] leading-[18px] border border-[#0D9BC6] focus:outline-none placeholder:text-[#00000080] rounded-[8px]"
                            placeholder="Enter your phone number (e.g., 01712345678)"
                            onKeyPress={(e) => {
                              if (!/[0-9]/.test(e.key)) {
                                e.preventDefault();
                              }
                            }}
                          />
                        </div>
                        {/* @ts-expect-error error */}
                        {errors.phone && <span className="text-red-500 text-sm">{errors.phone.message}</span>}
                      </div>
                    </div>
                    <div className="flex flex-col sm:flex-row items-center gap-[10px]">
                      <div className="mb-[20px] sm:mb-[35px] relative w-full">
                        <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">Password</label>
                        <div className="relative">
                          <img src={LockIcon} alt="user" className='w-[30px] h-[25px] absolute sm:left-[20px] left-[10px] top-1/2 transform -translate-y-1/2' />
                          <input
                            type={showPassword ? "text" : "password"}
                            {...register("password", {
                              required: "Password is required",
                              minLength: {
                                value: 8,
                                message: "Password must be at least 8 characters"
                              },
                              maxLength: {
                                value: 50,
                                message: "Password cannot exceed 50 characters"
                              },
                              pattern: {
                                value: /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?])[A-Za-z\d!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]{8,}$/,
                                message: "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
                              },
                              validate: {
                                notEmpty: (value) => value.trim() !== '' || "Password cannot be empty",
                                noSpaces: (value) => !/\s/.test(value) || "Password cannot contain spaces"
                              }
                            })}
                            className="w-full py-[17px] px-[18px] sm:ps-[60px] ps-[45px] text-[14px] font-semibold text-[#000000] leading-[18px] border border-[#0D9BC6] focus:outline-none placeholder:text-[#00000080] rounded-[8px]"
                            placeholder="Enter your password"
                          />
                          <img
                            src={showPassword ? "/eye-open.svg" : "/eye-close.svg"}
                            alt="Toggle"
                            className="cursor-pointer absolute right-5 top-0 bottom-0 my-auto w-6 h-6"
                            onClick={() => setShowPassword(!showPassword)}
                          />
                        </div>
                        {/* @ts-expect-error error */}
                        {errors.password && <span className="text-red-500 text-sm">{errors.password.message}</span>}
                      </div>
                      <div className="mb-[20px] sm:mb-[35px] relative w-full">
                        <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">Confirm Password</label>
                        <div className="relative">
                          <img src={LockIcon} alt="user" className='w-[30px] h-[25px] absolute sm:left-[20px] left-[10px] top-1/2 transform -translate-y-1/2' />
                          <input
                            type={showConfirmPassword ? "text" : "password"}
                            {...register("confirmPassword", {
                              required: "Please confirm your password",
                              validate: {
                                matchPassword: (value) => value === watch("password") || "Passwords do not match",
                                notEmpty: (value) => value.trim() !== '' || "Confirm password cannot be empty",
                                noSpaces: (value) => !/\s/.test(value) || "Password cannot contain spaces"
                              }
                            })}
                            className="w-full py-[17px] px-[18px] sm:ps-[60px] ps-[45px] text-[14px] font-semibold text-[#000000] leading-[18px] border border-[#0D9BC6] focus:outline-none placeholder:text-[#00000080] rounded-[8px]"
                            placeholder="Confirm your password"
                          />
                          <img
                            src={showConfirmPassword ? "/eye-open.svg" : "/eye-close.svg"}
                            alt="Toggle"
                            className="cursor-pointer absolute right-5 top-0 bottom-0 my-auto w-6 h-6"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          />
                        </div>
                        {/* @ts-expect-error error */}
                        {errors.confirmPassword && <span className="text-red-500 text-sm">{errors.confirmPassword.message}</span>}
                      </div>
                    </div>
                    <div className="text-center mb-[12px] pt-[10px]">
                      <button
                        type="submit"
                        className="bg-gradient-to-r from-[#0D3FC6] to-[#3793FF] text-white py-[16px] px-[45px] rounded-[8px] font-medium hover:bg-blue-700 transition-colors cursor-pointer !rounded-button whitespace-nowrap text-[14px] leading-[18px] uppercase"
                        disabled={isSendingOtp}
                      >
                        {isSendingOtp ? "Sending OTP..." : "Sign Up"}
                      </button>
                    </div>
                  </form>

                  <SocialLogin />

                  <div className="text-center text-[#05073C] font-normal mt-[30px] text-[14px] leading-[18px]">
                    Already have an account? {" "}
                    <a href="#" className="text-[#0D3FC6] font-semibold" onClick={onSignInClick}>
                      Sign In
                    </a>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUpModal;
