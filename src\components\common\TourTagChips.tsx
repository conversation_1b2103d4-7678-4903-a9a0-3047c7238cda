import React from 'react';
import type { TourTag } from '../../utils/useTourTags';
import './TourTagChips.css';

interface TourTagChipsProps {
  tags: TourTag[];
}

const TourTagChips: React.FC<TourTagChipsProps> = ({ tags }) => {
  if (!tags?.length) return null;
  return (
    <div className="tour-tag-chips">
      {tags.map(tag => (
        <span className="tour-tag-chip" key={tag.id}>{tag.tag}</span>
      ))}
    </div>
  );
};

export default TourTagChips;
