const ChatLoader = () => {

     return (
          <div className="max-w-[740px] ">
               <div className="flex items-start gap-[8px]">
                    <div className="w-[40px] h-[40px] rounded-full bg-[#E7ECF9] flex items-center justify-center flex-shrink-0 p-1">
                         <img src="/mini-logo.svg" className="w-full h-full rounded-full" alt="AI Assistant" />
                    </div>
                    <div className="">
                         <div className="border border-[#E5E7EB] shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)] bg-[#F4F4F6] rounded-[20px] rounded-es-none px-[16px] py-[15px] text-[14px] leading-[20px] text-[#1F2937]">
                              <div className="flex items-center space-x-2">
                                   <div className="w-2 h-2 rounded-full bg-blue-600 animate-bounce"></div>
                                   <div className="w-2 h-2 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                                   <div className="w-2 h-2 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: "0.4s" }}></div>
                              </div>
                         </div>
                    </div>
               </div>
          </div>
     );
};

export default ChatLoader;
