import { useSelector, useDispatch } from "react-redux";
import { RootState } from "../../store/store";
import { addMessage } from "../../store/features/chat/chatSlice";
import { useEffect } from "react";
import PackagesPrompt from "../../components/dashboard/chat/PackagesPrompt";
import DateRangePicker from "../../components/dashboard/chat/DateRangePicker";
import Markdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import PersonalInformation from "../../components/dashboard/chat/PersonalInformation";
import ChatLoader from "../../components/common/ChatLoader";
import TravellerDetails from "../../components/dashboard/chat/TravellerDetails";
import SelectTravellers from "../../components/dashboard/chat/SelectTravellers";
import ReviewDetails from "../../components/dashboard/chat/ReviewDetails";
import BookingRequest from "../../components/dashboard/chat/BookingRequest";
import PopularDestinations from "../../components/dashboard/chat/PopularDestinations";

interface AdminChatProps {
     messagesEndRef: React.RefObject<HTMLDivElement>;
}

const AdminChat = ({ messagesEndRef }: AdminChatProps) => {
     const messages = useSelector((state: RootState) => state.chat.messages);
     const dispatch = useDispatch();
     const selectedCountry = useSelector((state: RootState) => state.tours.country);
     const isLoading = useSelector((state: RootState) => state.chat.isLoading);
     
     
     useEffect(() => {
          if (selectedCountry) {
               dispatch(addMessage({
                    content: selectedCountry,
                    sender: "user"
               }));

               setTimeout(() => {
                    dispatch(addMessage({
                         content: `Great! I'll help you find the best travel packages for ${selectedCountry}. Here are the states in ${selectedCountry}, Please select the state you want to travel to.`,
                         sender: "ai"
                    }));
               }, 500);
          }
     }, [selectedCountry, dispatch]);


     const isCalendarMessage = (message: { sender: string, responseType: string }) => {
          return message.sender === "ai" && message.responseType === "duration";
     };

     const isSelectTravelerMessage = (message: { sender: string, responseType: string }) => {
          return message.sender === "ai" && message.responseType === "number_of_travellers";
     };

     return (
          <div className="w-full px-4 md:px-6 lg:px-8">
               <div className={` sm:pt-0 flex flex-col justify-between h-[calc(100vh-170px)] pt-20 max-w-[900px] mx-auto`}>
                    <div>

                         {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                         {messages.length > 0 && messages.map((message: any, index: number) => (
                              <div key={index}>
                                   {message?.sender === "ai" ? (
                                        // AI Assistant Chat
                                        <div className="max-w-[100%] sm:max-w-[80%] mb-[20px]">
                                             <div className="flex items-start gap-[8px]">
                                                  <div className="w-[40px] h-[40px] rounded-full bg-[#E7ECF9] flex items-center justify-center flex-shrink-0">
                                                       <img src="/mini-logo.svg" alt="AI Assistant" className="w-[31px] rounded-full" />
                                                  </div>
                                                  <div className="">
                                                       <div className="border border-[#E5E7EB] shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)] bg-[#F4F4F6] rounded-[20px] rounded-es-none px-[16px] py-[10px] sm:py-[15px] text-[14px] leading-[20px] text-[#1F2937] markdown flex flex-col justify-start item">
                                                            <Markdown remarkPlugins={[remarkGfm]}>{message.content}</Markdown>
                                                            {/* Add calendar component when response type is calendar */}
                                                            {isCalendarMessage(message) && (
                                                                 <div>
                                                                      <DateRangePicker />
                                                                 </div>
                                                            )}

                                                            {isSelectTravelerMessage(message) && (
                                                                 <div className="w-full">
                                                                      <SelectTravellers />
                                                                 </div>
                                                            )}
                                                       </div>
                                                  </div>
                                             </div>
                                        </div>
                                   ) : (
                                        // User Chat
                                        <div className="max-w-[100%] sm:max-w-[80%] ms-auto my-[30px]">
                                             <div className="flex items-end flex-col justify-end">
                                                  <p className="border border-[#E5E7EB] shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)] bg-[#0D3FC6] rounded-[20px] rounded-ee-none px-[12px] sm:px-[16px] py-[12px] text-[14px] leading-[20px] text-[#FFFFFF]">
                                                       {message.content}
                                                  </p>
                                             </div>
                                        </div>
                                   )}

                                   {message.responseType === "contact_info" && (
                                        <div className="mb-4">
                                             <PersonalInformation />
                                        </div>
                                   )}

                                   {message.responseType === "traveller_details" && (
                                        <div className="mb-4">
                                             <TravellerDetails />
                                        </div>
                                   )}


                                   {message.responseType === "handle_booking" && (
                                        <div className="mb-4">
                                             <ReviewDetails content={message.extracontent} />
                                        </div>
                                   )}

                                   {message.responseType === "tour_packages" && message.tourDetails && Array.isArray(message.tourDetails) && message.tourDetails.length > 0 && (
                                        <div className="w-full">
                                             <PackagesPrompt packages={message.tourDetails} meta_details={message?.meta_details} />
                                        </div>
                                   )}

                                   {message.responseType === "popular" && (
                                        <div className="mb-4">
                                             <PopularDestinations packages={message?.popularDestinations} />
                                        </div>
                                   )}


                                   {message.responseType === "tour_packages" && message.tourPackages && (message?.tourPackages?.cheapest?.length > 0 || message?.tourPackages?.featured?.length > 0) && (
                                        <div className="w-full">
                                             <PackagesPrompt packages={message?.tourPackages} meta_details={message?.meta_details} />
                                        </div>
                                   )}

                                   {message.responseType === "booking_confirmed" && (
                                        <div className="mb-4">
                                             <BookingRequest bookingDetails={message?.extracontent} />
                                        </div>
                                   )}

                              </div>
                         ))}
                         {isLoading && (
                              <ChatLoader />
                         )}
                         <div ref={messagesEndRef} />
                    </div>
               </div>
               {/* Show loader when waiting for AI response */}
          </div>
     );
};

export default AdminChat;