import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { addMessage, setLoading, threadUid as setThreadUid } from "../../../store/features/chat/chatSlice";
import { useSendMessageMutation } from "../../../store/features/chat/chatApi";
import { RootState } from "../../../store/store";

const getDaysInMonth = (year: number, month: number) => {
     return new Date(year, month + 1, 0).getDate();
};

const getFirstDayOfWeek = (year: number, month: number) => {
     const day = new Date(year, month, 1).getDay();
     return day;
};

const isSameDay = (a: Date, b: Date) =>
     a.getFullYear() === b.getFullYear() &&
     a.getMonth() === b.getMonth() &&
     a.getDate() === b.getDate();

const isBetween = (date: Date, start: Date, end: Date) =>
     date > start && date < end;

const formatDate = (date: Date) => {
     const day = date.getDate().toString().padStart(2, '0');
     const month = (date.getMonth() + 1).toString().padStart(2, '0');
     const year = date.getFullYear();
     return `${day}/${month}/${year}`;
};

const DateRangePicker = () => {
     const today = new Date();
     const [showCalendar, setShowCalendar] = useState(false);
     const [currentMonth, setCurrentMonth] = useState(today.getMonth());
     const [currentYear, setCurrentYear] = useState(today.getFullYear());
     const [startDate, setStartDate] = useState<Date | null>(null);
     const [endDate, setEndDate] = useState<Date | null>(null);
     const [datesApplied, setDatesApplied] = useState(false);
     const messages = useSelector((state: RootState) => state.chat.messages);
     // eslint-disable-next-line @typescript-eslint/no-explicit-any
     const duration = messages?.find((message: any) => message.responseType === "duration")?.extracontent?.tour?.tour_duration;
     const dispatch = useDispatch();
     const [sendMessage] = useSendMessageMutation();
     const threadUid = useSelector((state: RootState) => state.chat.threadUid);
     
     const handleDayClick = (day: number) => {
          if (datesApplied) return;

          const selectedDate = new Date(currentYear, currentMonth, day);
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          if (selectedDate < today) {
               return;
          }

          // Only allow start date selection
          if (!duration) {
               return; // Don't allow selection if duration is not set
          }

          // Set the start date
          setStartDate(selectedDate);
          
          // Automatically calculate and set the end date based on duration
          const calculatedEndDate = new Date(selectedDate);
          calculatedEndDate.setDate(selectedDate.getDate() + (duration - 1)); // -1 because duration includes start date
          setEndDate(calculatedEndDate);
     };

     const formatDateForDisplay = (date: Date) => {
          return date.toLocaleDateString('en-US', {
               day: 'numeric',
               month: 'long'
          });
     };

     const handleApply = async () => {
          if (startDate && endDate) {
               // Set dates as applied
               setDatesApplied(true);

               // Calculate the number of days
               const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
               const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end dates
               dispatch(setLoading(true));

               const message = `I've selected a ${diffDays}-day trip from ${formatDate(startDate)} to ${formatDate(endDate)}`;

               // Create API payload with context_data
               const messagePayload = {
                    message: message,
                    thread_uid: threadUid || "",
                    context_data: {
                         intent: "booking",
                         action: "duration",
                         data: {
                              start_date: formatDate(startDate),
                              end_date: formatDate(endDate)
                         }
                    }
               };

               dispatch(addMessage({
                    content: message,
                    sender: "user"
               }));

               try {
                    // Send message to API
                    const response = await sendMessage(messagePayload).unwrap();

                    // Update thread UID if needed
                    if (response?.data?.thread_uid) {
                         dispatch(setThreadUid(response.data.thread_uid));
                    }

                    // Process AI response
                    let aiResponseMessage = "";
                    let responseType = "text";

                    if (response.data?.ai_response?.type) {
                         responseType = response.data.ai_response.type;
                    }

                    if (response.data?.ai_response?.data?.text_response?.message) {
                         aiResponseMessage = response.data.ai_response.data.text_response.message;
                    }
                    else if (response.data?.ai_response?.text_response?.message) {
                         aiResponseMessage = response.data.ai_response.text_response.message;
                    }
                    else if (response.data?.ai_response?.response?.message) {
                         aiResponseMessage = response.data.ai_response.response.message;
                    }

                    // Add AI response to chat
                    if (aiResponseMessage) {
                         dispatch(addMessage({
                              content: aiResponseMessage,
                              sender: "ai",
                              responseType: responseType,
                              tourDetails: response.data?.ai_response?.data || []
                         }));
                    } else {
                         dispatch(addMessage({
                              content: "Here are some options for your selected dates.",
                              sender: "ai"
                         }));
                    }
               } catch (error) {
                    console.error("Error sending date range:", error);

                    // Add error message
                    dispatch(addMessage({
                         content: "Sorry, I couldn't process your date selection. Please try again.",
                         sender: "ai"
                    }));
               } finally {
                    // Set loading to false
                    dispatch(setLoading(false));
               }
          }

          setShowCalendar(false);
     };

     const handleClear = () => {
          // Only allow clearing if dates haven't been applied yet
          if (!datesApplied) {
               setStartDate(null);
               setEndDate(null);
          }
     };

     const daysInMonth = getDaysInMonth(currentYear, currentMonth);
     const firstDayOfWeek = getFirstDayOfWeek(currentYear, currentMonth);

     const getDayStyle = (day: number) => {
          const date = new Date(currentYear, currentMonth, day);
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          // Style for past dates
          if (date < today) {
               return "text-gray-300 cursor-not-allowed";
          }

          if (startDate && isSameDay(date, startDate)) {
               return "bg-blue-600 text-white";
          }
          if (endDate && isSameDay(date, endDate)) {
               return "bg-blue-600 text-white";
          }
          if (startDate && endDate && isBetween(date, startDate, endDate)) {
               return "bg-blue-100";
          }
          return "";
     };

     const isDateSelectable = (date: Date) => {
          // Check if date is in the past
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          if (date < today) return false;

          // If start date is already selected, don't allow selecting other dates
          if (startDate) return false;

          return true;
     };

     // Generate calendar days based on the current month/year
     const generateCalendarDays = () => {
          const days = [];
          const today = new Date();
          today.setHours(0, 0, 0, 0);

          // Add empty cells for days before first day of month
          for (let i = 0; i < firstDayOfWeek; i++) {
               days.push(<div key={`empty-${i}`}></div>);
          }

          // Add all days of the month with click handlers
          for (let day = 1; day <= daysInMonth; day++) {
               const date = new Date(currentYear, currentMonth, day);

               days.push(
                    <button
                         key={day}
                         className={`p-1 w-[38px] h-[38px] mx-auto rounded-full ${
                              !isDateSelectable(date)
                                   ? 'text-gray-300 cursor-not-allowed'
                                   : 'hover:bg-blue-600 hover:text-white'
                              } ${getDayStyle(day)}`}
                         onClick={() => isDateSelectable(date) && handleDayClick(day)}
                         disabled={!isDateSelectable(date)}
                    >
                         {day}
                    </button>
               );
          }

          return days;
     };

     return (
          <div className="relative inline-block w-full">
               <button
                    className={`flex items-center justify-center gap-2 px-4 py-2 rounded-lg w-44 border-2 border-blue-600 text-blue-600 font-medium hover:bg-blue-50 transition-colors ${datesApplied ? 'opacity-70 cursor-not-allowed' : 'cursor-pointer'
                         }`}
                    onClick={() => !datesApplied && setShowCalendar((prev) => !prev)}
                    disabled={datesApplied}
               >
                    <svg width="20" height="20" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                         <path d="M5 1V3M11 1V3M1 7H15M3 3H13C14.1 3 15 3.9 15 5V13C15 14.1 14.1 15 13 15H3C1.9 15 1 14.1 1 13V5C1 3.9 1.9 3 3 3Z" stroke="#2563EB" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                    <span>{datesApplied
                         ? `${formatDateForDisplay(startDate!)} - ${formatDateForDisplay(endDate!)}`
                         : "Select Dates"
                    }</span>
               </button>

               {showCalendar && !datesApplied && (
                    <>
                         {/* Overlay for background dimming */}
                         <div className="fixed inset-0 bg-black/50 bg-opacity-30 z-[999]" onClick={() => setShowCalendar(false)}></div>

                         {/* Calendar popup positioned at the bottom center */}
                         <div className="fixed z-[9999] left-0 right-0 bottom-0 mx-auto bg-white rounded-t-2xl shadow-lg max-w-[550px] w-full">
                              <div className="flex justify-between items-center px-4 border-b border-gray-100">
                                   <h2 className="text-lg font-semibold">Select Dates</h2>
                                   <button
                                        className="w-8 h-8 flex items-center justify-center"
                                        onClick={() => setShowCalendar(false)}
                                   >
                                        ✕
                                   </button>
                              </div>

                              <div className="flex justify-end px-4 pt-2">
                                   <div
                                        className="text-blue-500 font-medium cursor-pointer"
                                        onClick={handleClear}
                                   >
                                        Clear dates
                                   </div>
                              </div>

                              <div className="px-6 pb-4 pt-2">
                                   <div className="flex justify-between items-center mb-4">
                                        <button
                                             className="w-8 h-8 flex items-center justify-center text-lg"
                                             onClick={() => {
                                                  if (currentMonth === 0) {
                                                       setCurrentMonth(11);
                                                       setCurrentYear(currentYear - 1);
                                                  } else {
                                                       setCurrentMonth(currentMonth - 1);
                                                  }
                                             }}
                                        >
                                             &lt;
                                        </button>
                                        <span className="font-bold text-xl">
                                             {new Date(currentYear, currentMonth).toLocaleDateString('en-US', {
                                                  month: 'long',
                                                  year: 'numeric'
                                             })}
                                        </span>
                                        <button
                                             className="w-8 h-8 flex items-center justify-center text-lg"
                                             onClick={() => {
                                                  if (currentMonth === 11) {
                                                       setCurrentMonth(0);
                                                       setCurrentYear(currentYear + 1);
                                                  } else {
                                                       setCurrentMonth(currentMonth + 1);
                                                  }
                                             }}
                                        >
                                             &gt;
                                        </button>
                                   </div>

                                   <div className="grid grid-cols-7 text-center">
                                        <div className="font-normal mb-2 text-sm text-gray-500">Sun</div>
                                        <div className="font-normal mb-2 text-sm text-gray-500">Mon</div>
                                        <div className="font-normal mb-2 text-sm text-gray-500">Tue</div>
                                        <div className="font-normal mb-2 text-sm text-gray-500">Wed</div>
                                        <div className="font-normal mb-2 text-sm text-gray-500">Thu</div>
                                        <div className="font-normal mb-2 text-sm text-gray-500">Fri</div>
                                        <div className="font-normal mb-2 text-sm text-gray-500">Sat</div>

                                        {/* Dynamic calendar days with proper click handlers */}
                                        {generateCalendarDays()}
                                   </div>
                              </div>

                              <div className="flex p-4 border-t border-gray-100">
                                   <button
                                        className="py-3 w-full rounded-lg bg-blue-100 text-blue-600 font-medium"
                                        onClick={handleClear}
                                   >
                                        Clear
                                   </button>
                                   <button
                                        className="py-3 w-full ml-2 rounded-lg bg-blue-600 text-white font-medium"
                                        onClick={handleApply}
                                        disabled={!startDate || !endDate}
                                   >
                                        Apply
                                   </button>
                              </div>
                         </div>
                    </>
               )}
          </div>
     );
};

export default DateRangePicker;