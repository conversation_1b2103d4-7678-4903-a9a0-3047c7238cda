const AdminAnalyticsSkeleton = () => {
     return (
          <div className="md:mt-0 mt-[80px] sm:mt-[60px] max-w-[1240px] sm:px-[30px] px-[10px] mx-auto py-4 sm:py-6">

               {/* Summary Cards Skeleton */}
               <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 mb-6 sm:mb-8">
                    {[...Array(4)].map((_, index) => (
                         <div key={index} className="bg-white p-3 sm:p-6 rounded-lg border border-[#E5E7EB] shadow-sm">
                              <div className="h-3 sm:h-4 bg-gray-200 rounded w-16 sm:w-20 mb-2 animate-pulse"></div>
                              <div className="h-6 sm:h-8 bg-gray-200 rounded w-12 sm:w-16 animate-pulse"></div>
                         </div>
                    ))}
               </div>

               {/* Line Chart Skeleton */}
               <div className="bg-white rounded-lg border border-[#E5E7EB] shadow-sm mb-6 sm:mb-8">
                    <div className="p-4 sm:p-6 border-b border-[#E5E7EB]">
                         <div className="h-5 sm:h-6 bg-gray-200 rounded w-48 animate-pulse"></div>
                    </div>
                    <div className="p-4 sm:p-6">
                         <div className="h-64 sm:h-80 bg-gray-50 rounded-lg animate-pulse relative overflow-hidden">
                              {/* Chart Grid Lines Skeleton */}
                              <div className="absolute inset-0 p-8">
                                   {/* Horizontal grid lines */}
                                   {[...Array(5)].map((_, i) => (
                                        <div
                                             key={`h-${i}`}
                                             className="absolute left-8 right-8 h-px bg-gray-200"
                                             style={{ top: `${20 + i * 15}%` }}
                                        />
                                   ))}
                                   {/* Vertical grid lines */}
                                   {[...Array(7)].map((_, i) => (
                                        <div
                                             key={`v-${i}`}
                                             className="absolute top-8 bottom-8 w-px bg-gray-200"
                                             style={{ left: `${15 + i * 12}%` }}
                                        />
                                   ))}
                                   {/* Chart line skeleton */}
                                   <svg className="absolute inset-8 w-full h-full" viewBox="0 0 100 100">
                                        <path
                                             d="M 10,80 Q 25,60 40,70 T 90,40"
                                             stroke="#E5E7EB"
                                             strokeWidth="2"
                                             fill="none"
                                             className="animate-pulse"
                                        />
                                        <path
                                             d="M 10,90 Q 25,70 40,80 T 90,50"
                                             stroke="#D1D5DB"
                                             strokeWidth="2"
                                             fill="none"
                                             className="animate-pulse"
                                        />
                                   </svg>
                              </div>
                              <div className="absolute inset-0 flex items-center justify-center">
                                   <div className="text-gray-400 text-sm">Loading chart...</div>
                              </div>
                         </div>
                    </div>
               </div>

               {/* Pie Chart Skeleton */}
               <div className="bg-white rounded-lg border border-[#E5E7EB] shadow-sm">
                    <div className="p-4 sm:p-6 border-b border-[#E5E7EB]">
                         <div className="h-5 sm:h-6 bg-gray-200 rounded w-36 animate-pulse"></div>
                    </div>
                    <div className="p-4">
                         <div className="h-64 sm:h-80 bg-gray-50 rounded-lg animate-pulse relative overflow-hidden">
                              {/* Pie Chart Skeleton */}
                              <div className="absolute inset-0 flex items-center justify-center">
                                   <div className="relative">
                                        {/* Pie slices skeleton */}
                                        <div className="w-32 h-32 rounded-full border-8 border-gray-200 animate-pulse"></div>
                                        <div className="absolute top-0 left-0 w-32 h-32 rounded-full border-t-8 border-l-8 border-gray-300 animate-pulse"></div>
                                        <div className="absolute top-0 left-0 w-32 h-32 rounded-full border-r-8 border-gray-250 animate-pulse"></div>
                                   </div>
                              </div>
                              <div className="absolute inset-0 flex items-center justify-center">
                                   <div className="text-gray-400 text-sm mt-20">Loading chart...</div>
                              </div>
                         </div>
                    </div>
               </div>
          </div>
     );
};

export default AdminAnalyticsSkeleton;