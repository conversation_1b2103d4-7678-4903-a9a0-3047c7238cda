interface PaginationProps {
     currentPage: number;
     totalPages: number;
     onPageChange: (page: number) => void;
}

const ClicksPagination = ({ currentPage, totalPages, onPageChange }: PaginationProps) => {
     const getPageNumbers = () => {
          const pages = [];
          
          if (totalPages <= 5) {
               // If total pages are 5 or less, show all pages
               for (let i = 1; i <= totalPages; i++) {
                    pages.push(i);
               }
          } else {
               // Always show first page
               pages.push(1);
               
               if (currentPage <= 3) {
                    // If current page is near the start
                    pages.push(2, 3, 4);
                    pages.push('...');
                    pages.push(totalPages);
               } else if (currentPage >= totalPages - 2) {
                    // If current page is near the end
                    pages.push('...');
                    pages.push(totalPages - 3, totalPages - 2, totalPages - 1, totalPages);
               } else {
                    // If current page is in the middle
                    pages.push('...');
                    pages.push(currentPage - 1, currentPage, currentPage + 1);
                    pages.push('...');
                    pages.push(totalPages);
               }
          }
          
          return pages;
     };

     return (
          <div className="flex items-center justify-between px-4 py-3 sm:px-6 border-t border-gray-200 bg-white">
               <div className="flex-1 flex justify-between sm:hidden">
                    <button
                         onClick={() => onPageChange(currentPage - 1)}
                         disabled={currentPage === 1}
                         className={`relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md
               ${currentPage === 1
                                   ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                   : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'}`}
                    >
                         Previous
                    </button>
                    <button
                         onClick={() => onPageChange(currentPage + 1)}
                         disabled={currentPage === totalPages}
                         className={`relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-md
               ${currentPage === totalPages
                                   ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                   : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'}`}
                    >
                         Next
                    </button>
               </div>
               <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                         <p className="text-sm text-gray-700">
                              Showing page <span className="font-medium">{currentPage}</span> of{' '}
                              <span className="font-medium">{totalPages}</span>
                         </p>
                    </div>
                    <div>
                         <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                              <button
                                   onClick={() => onPageChange(currentPage - 1)}
                                   disabled={currentPage === 1}
                                   className={`relative inline-flex items-center px-2 py-2 rounded-l-md border text-sm font-medium
                   ${currentPage === 1
                                             ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                             : 'bg-white text-gray-500 hover:bg-gray-50'}`}
                              >
                                   <span className="sr-only">Previous</span>
                                   <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                                   </svg>
                              </button>
                              {getPageNumbers().map((pageNumber, index) => (
                                   pageNumber === '...' ? (
                                        <span
                                             key={`ellipsis-${index}`}
                                             className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                                        >
                                             ...
                                        </span>
                                   ) : (
                                        <button
                                             key={pageNumber}
                                             onClick={() => typeof pageNumber === 'number' && onPageChange(pageNumber)}
                                             className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium
                     ${currentPage === pageNumber
                                                  ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                                  : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'}`}
                                        >
                                             {pageNumber}
                                        </button>
                                   )
                              ))}
                              <button
                                   onClick={() => onPageChange(currentPage + 1)}
                                   disabled={currentPage === totalPages}
                                   className={`relative inline-flex items-center px-2 py-2 rounded-r-md border text-sm font-medium
                   ${currentPage === totalPages
                                             ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                             : 'bg-white text-gray-500 hover:bg-gray-50'}`}
                              >
                                   <span className="sr-only">Next</span>
                                   <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                   </svg>
                              </button>
                         </nav>
                    </div>
               </div>
          </div>
     );
};

export default ClicksPagination;