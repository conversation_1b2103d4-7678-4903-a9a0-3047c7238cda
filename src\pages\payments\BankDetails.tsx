import { useParams } from "react-router-dom";
import { useGetBookinGatewaysQuery } from "../../store/features/bookings/bookingsApi";

const BankDetails = () => {
     const { orderId } = useParams();
     const { data: gateways, isLoading } = useGetBookinGatewaysQuery(orderId || "");

     const bankInstructions = gateways?.payment_gateways?.find((gateway: { id: string }) => gateway.id === "bacs")?.settings?.instructions?.value;
     const formattedInstructions = bankInstructions?.split('\n').filter(Boolean);

     if (isLoading) {
          return (
               <div className="min-h-screen flex items-center justify-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
               </div>
          );
     }

     return (
          <div className="min-h-screen bg-gray-50 py-6 sm:py-12 px-4 sm:px-6 lg:px-8">
               <div className="max-w-3xl mx-auto w-full px-4 sm:px-0">
                    <div className="bg-white shadow-lg rounded-lg overflow-hidden">
                         {/* Header Section */}
                         <div className="bg-gradient-to-r from-[#0D3FC6] to-[#3793FF] px-4 sm:px-6 py-6 sm:py-8 text-center">
                              <div className="mb-4">
                                   <svg className="mx-auto h-12 w-12 sm:h-16 sm:w-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                   </svg>
                              </div>
                              <h2 className="text-2xl sm:text-3xl font-bold text-white mb-2">Thank You for Your Order!</h2>
                              <p className="text-sm sm:text-base text-white opacity-90">Order ID: {orderId}</p>
                         </div>

                         {/* Instructions Section */}
                         <div className="px-4 sm:px-6 py-6 sm:py-8">
                              <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 sm:p-6 mb-6">
                                   <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4">Bank Transfer Instructions</h3>
                                   <div className="space-y-2 sm:space-y-3">
                                        {formattedInstructions?.map((instruction: string, index: number) => (
                                             <p key={index} className="text-sm sm:text-base text-gray-700 leading-relaxed">
                                                  {instruction}
                                             </p>
                                        ))}
                                   </div>
                              </div>

                              {/* Important Notes */}
                              <div className="space-y-3 sm:space-y-4">
                                   <div className="flex items-start">
                                        <div className="flex-shrink-0">
                                             <svg className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                             </svg>
                                        </div>
                                        <div className="ml-3">
                                             <h4 className="text-xs sm:text-sm font-medium text-gray-900">Important Note</h4>
                                             <p className="mt-1 text-xs sm:text-sm text-gray-500">Please use your Order ID ({orderId}) as the payment reference.</p>
                                        </div>
                                   </div>

                                   <div className="flex items-start">
                                        <div className="flex-shrink-0">
                                             <svg className="h-5 w-5 sm:h-6 sm:w-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                             </svg>
                                        </div>
                                        <div className="ml-3">
                                             <h4 className="text-xs sm:text-sm font-medium text-gray-900">Processing Time</h4>
                                             <p className="mt-1 text-xs sm:text-sm text-gray-500">Your order will be processed once the payment has been confirmed in our account.</p>
                                        </div>
                                   </div>
                              </div>
                         </div>

                         {/* Footer Section */}
                         <div className="bg-gray-50 px-4 sm:px-6 py-3 sm:py-4 border-t border-gray-100">
                              <p className="text-xs sm:text-sm text-gray-600 text-center">
                                   If you have any questions, please contact our support team.
                              </p>
                         </div>
                    </div>
               </div>
          </div>
     );
};

export default BankDetails;