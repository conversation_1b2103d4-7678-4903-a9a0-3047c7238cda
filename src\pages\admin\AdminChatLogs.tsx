import AdminLayout from "../../components/admin/AdminLayout";
import AdminChatHistory from "./AdminChatHistory";
import { useLocation, useParams } from "react-router-dom";

const AdminChatLogs = () => {
  const { userId } = useParams();
  const isErrorLogs = useLocation().pathname.includes("error-logs");
  const errorLogBackLink = "/admin/error-logs";
  const userLogBackLink = `/admin/users/${userId}/threads`;
  
  return (
    <AdminLayout title="Chat Logs" backLink={isErrorLogs ? errorLogBackLink : userLogBackLink}>
      <div className="flex">
        <div className="h-screen overflow-y-auto flex-1">
          <div className="opacity-80 w-full mt-10">
            <AdminChatHistory />
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminChatLogs;