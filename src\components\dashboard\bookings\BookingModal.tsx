
interface SelectedBookings {
     id: number;
     destination: string;
     hotel: string;
     dates: string;
     guests: string;
     booking_reference: string;
     booking_date: string;
     duration: {
          start_date: string;
          end_date: string;
     };
     traveller_numbers: {
          total: number;
     };
     booking_amount: string;
     booking_currency: string;
     booking_status: string;
     booking_type: string;
     status: string;
     tour: {
          location: {
               address: string;
          }
     },
     order: {
          order_id: string;
          payment_status: string;
     }
}

const BookingModal = ({ setShowDetailModal, selectedBooking }: {
     setShowDetailModal: (show: boolean) => void;
     selectedBooking: SelectedBookings;
}) => {
     return (
          <div className="fixed inset-0 bg-[#0000004D] flex items-center justify-center z-[9999] px-[10px]">
               <div className="bg-white rounded-[24px] shadow-xl w-full max-w-[915px]">
                    <div className="flex justify-between items-center border-b border-gray-200 px-[15px] sm:px-6 py-[10px] sm:py-4">
                         <h3 className="text-[20px] sm:text-[24px] font-bold text-gray-900">
                              Booking Details
                         </h3>
                         <button
                              className="cursor-pointer w-[30px] sm:w-[35px] h-[30px] sm:h-[35px] bg-[#E7ECF9] rounded-full flex items-center justify-center"
                              onClick={() => setShowDetailModal(false)}
                         >
                              <img src="./modalClose-icon.svg" alt="Close" />
                         </button>
                    </div>

                    <div className="p-[15px] sm:p-[25px] max-h-[calc(100vh-100px)] overflow-y-auto">
                         <div>
                              <h3 className="text-xl sm:text-2xl text-[#05073C] font-semibold mb-[10px] sm:mb-[15px]">
                                   Booking Information
                              </h3>
                              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                                   <div>
                                        <p className="text-xs text-[#6B7280]">Booking Number</p>
                                        <p className="text-sm font-medium">{selectedBooking?.booking_reference || "N/A"}</p>
                                   </div>
                                   <div>
                                        <p className="text-xs text-[#6B7280]">Booking Date</p>
                                        <p className="text-sm font-medium">{selectedBooking?.booking_date?.split('T')[0] || "N/A"}</p>
                                   </div>
                                   <div>
                                        <p className="text-xs text-[#6B7280]">Stay Dates</p>
                                        <p className="text-sm font-medium">
                                             {selectedBooking?.duration?.start_date || "N/A"} - {selectedBooking?.duration?.end_date || "N/A"}
                                        </p>
                                   </div>
                                   <div>
                                        <p className="text-xs text-[#6B7280]">Total Price</p>
                                        <p className="text-sm font-medium">৳ {selectedBooking?.booking_amount || "N/A"}</p>
                                   </div>
                              </div>
                         </div>
                         <div className="mt-[20px] sm:mt-[50px]">
                              <h3 className="text-xl sm:text-2xl text-[#05073C] font-semibold mb-[10px] sm:mb-[15px]">
                                   Traveller Information
                              </h3>
                              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                                   <div>
                                        <p className="text-xs text-[#6B7280]">Destination</p>
                                        <p className="text-sm font-medium">{selectedBooking?.tour?.location?.address || "N/A"}</p>
                                   </div>
                                   <div>
                                        <p className="text-xs text-[#6B7280]">Hotel</p>
                                        <p className="text-sm font-medium">{selectedBooking?.hotel || "N/A"}</p>
                                   </div>
                                   <div>
                                        <p className="text-xs text-[#6B7280]">Guests</p>
                                        <p className="text-sm font-medium">{selectedBooking?.traveller_numbers?.total || "N/A"} Guests</p>
                                   </div>
                                   <div>
                                        <p className="text-xs text-[#6B7280]">Status</p>
                                        <p className="text-sm font-medium">{selectedBooking?.booking_status || "N/A"}</p>
                                   </div>
                              </div>
                         </div>
                         <div className="mt-[20px] sm:mt-[50px] mb-[20px]">
                              <h3 className="text-xl sm:text-2xl text-[#05073C] font-semibold mb-[10px] sm:mb-[15px]">
                                   Payment Information
                              </h3>
                              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                                   <div>
                                        <p className="text-xs text-[#6B7280]">Payment Status</p>
                                        <p className="text-sm font-medium bg-[#DCFCE7] text-[#166534] rounded-[5px] px-[18px] py-[4px] inline-block mt-[4px]">
                                             {selectedBooking?.booking_status}
                                        </p>
                                   </div>
                                   <div>
                                        <p className="text-xs text-[#6B7280]">Total Amount</p>
                                        <p className="text-sm font-medium">৳ {selectedBooking?.booking_amount || "N/A"}</p>
                                   </div>
                                   <div>
                                        <p className="text-xs text-[#6B7280]">Booking Type</p>
                                        <p className="text-sm font-medium">{selectedBooking?.booking_type || "N/A"}</p>
                                   </div>
                              </div>
                         </div>
                    </div>
               </div>
          </div>
     )
}

export default BookingModal;
