import SelectIcon from "../../assets/arrow-bottom.svg";
import SearchIcon from "../../assets/search-icon.svg";
import { tableHeading } from "../../utils/dummyData";
import { useGetPaymentsQuery } from "../../store/features/bookings/bookingsApi";
import PaymentSkeleton from "../../components/skeletons/PaymentSkeleton";
import { useState, useMemo } from 'react';

interface PaymentsData {
    booking_date: string;
    booking_amount: number;
    status: string;
    paymentMethod: string;
    action: string;
    tour: {
        tour_title: string;
    };
    booking_status: string;
}

const Payments = () => {
    const { data: paymentsData, isLoading } = useGetPaymentsQuery({});
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setStatusFilter(e.target.value);
    };

    const filteredPayments = useMemo(() => {
        if (!paymentsData?.payments) return [];

        return paymentsData.payments.filter((payment: PaymentsData) => {
            // Search filter
            const matchesSearch = payment.tour.tour_title.toLowerCase().includes(searchTerm.toLowerCase());

            // Status filter - make it case-insensitive and handle all statuses
            const matchesStatus = statusFilter === 'all'
                ? true
                : payment.booking_status.toLowerCase() === statusFilter.toLowerCase();

            return matchesSearch && matchesStatus;
        });
    }, [paymentsData?.payments, searchTerm, statusFilter]);

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(e.target.value);
    };

    const paginatedPayments = useMemo(() => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return filteredPayments.slice(startIndex, endIndex);
    }, [filteredPayments, currentPage]);
    const totalPages = Math.ceil(filteredPayments.length / itemsPerPage);

    const handlePageChange = (pageNumber: number) => {
        setCurrentPage(pageNumber);
    };

    const getPageNumbers = () => {
        const pages = [];

        if (totalPages <= 3) {
            return Array.from({ length: totalPages }, (_, i) => i + 1);
        }

        if (currentPage <= 2) {
            pages.push(1, 2, 3);
            if (totalPages > 4) pages.push('...');
            pages.push(totalPages);
        } else if (currentPage >= totalPages - 1) {
            pages.push(1);
            if (totalPages > 4) pages.push('...');
            pages.push(totalPages - 2, totalPages - 1, totalPages);
        } else {
            pages.push(1);
            if (currentPage > 3) pages.push('...');
            pages.push(currentPage - 1, currentPage, currentPage + 1);
            if (currentPage < totalPages - 2) pages.push('...');
            pages.push(totalPages);
        }

        return pages;
    };

    return (
        <div className="flex">
            <div className="md:ms-[275px] h-screen overflow-y-auto flex-1">
                <div className="fixed md:static w-full bg-white py-3 md:py-3.5 mx-auto px-4 sm:px-6 md:px-8 border-b border-[#E5E7EB] shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)]">
                    <h1 className="text-center md:text-start text-xl sm:text-2xl leading-8 font-bold text-[#05073C] max-w-[1240px] mx-auto">
                        Payments & Billing
                    </h1>
                </div>
                {isLoading ? <PaymentSkeleton /> : (
                    <div className="mt-16 md:mt-0 max-w-[1240px] px-4 sm:px-6 md:px-6 mx-auto py-6 md:py-8">
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-6 md:mb-8">
                            {/* Total Spent Card */}
                            <div className="bg-[#DCFAED] rounded-lg p-4 md:p-5">
                                <p className='text-sm text-[#05073CE5]'>Total Spent</p>
                                <h6 className="text-[#0EAD69] text-lg md:text-xl font-semibold">৳{paymentsData?.total_spend || 0}</h6>
                                <span className='text-sm text-[#49454FCC]'>This month {paymentsData?.total_spend || 0}</span>
                            </div>

                            {/* Pending Payments Card */}
                            <div className="bg-[#F3F4F6] rounded-lg p-4 md:p-5">
                                <p className='text-sm text-[#05073CE5]'>Pending Payments</p>
                                <h6 className="text-[#0D3FC6] text-lg md:text-xl font-semibold">৳ {paymentsData?.pending_payment || 0}</h6>
                                <span className='text-sm text-[#49454FCC]'>{paymentsData?.number_of_pending_payments} pending</span>
                            </div>
                        </div>

                        {/* Search and Filter Section - Made more responsive */}
                        <div className='flex flex-col sm:flex-row justify-end items-stretch sm:items-center gap-3 py-4 md:py-6'>
                            <div className="relative w-full sm:max-w-[145px]">
                                <select
                                    className='w-full py-2 px-4 text-sm leading-5 font-normal text-[#989EAE] rounded-lg border border-[#E5E7EB] appearance-none'
                                    value={statusFilter}
                                    onChange={handleStatusChange}
                                >
                                    <option value="all">All Bookings</option>
                                    <option value="pending">Pending</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                                <img src={SelectIcon} alt="Select" className="absolute right-3 top-1/2 transform -translate-y-1/2" />
                            </div>
                            <div className="relative w-full sm:max-w-[250px]">
                                <span className="absolute inset-y-0 left-3 flex items-center text-[#9CA3AF]">
                                    <img
                                        src={SearchIcon}
                                        alt="Search"
                                        className="w-4 h-4"
                                    />
                                </span>
                                <input
                                    type="text"
                                    placeholder="Search by tour title"
                                    value={searchTerm}
                                    onChange={handleSearchChange}
                                    className="w-full pl-10 pr-3 py-2 rounded-lg text-sm font-semibold leading-5 placeholder:text-[#6B7280] border border-[#E5E7EB] focus:outline-none"
                                />
                            </div>
                        </div>

                        {/* Table Section - Improved responsiveness */}
                        <div className='border border-[#EBEBEE] rounded-lg overflow-hidden bg-white'>
                            {/* Make table horizontally scrollable on mobile */}
                            <div className='w-full overflow-x-auto'>
                                <table className="w-full min-w-[200px] sm:min-w-[600px] whitespace-nowrap">
                                    <thead className="hidden sm:table-header-group">
                                        <tr className='bg-[#F9FAFB] border-b border-[#E5E7EB]'>
                                            {tableHeading.map((heading, index) => (
                                                <th
                                                    key={index}
                                                    className='text-[#6B7280] text-xs font-semibold px-2 sm:px-4 md:px-6 py-2 sm:py-3 text-start'
                                                >
                                                    {heading}
                                                </th>
                                            ))}
                                        </tr>
                                    </thead>
                                    <tbody className="block sm:table-row-group">
                                        {paginatedPayments.length === 0 ? (
                                            <tr>
                                                <td colSpan={tableHeading.length} className="text-center py-6 text-gray-500">
                                                    No payments found matching your search criteria
                                                </td>
                                            </tr>
                                        ) : (
                                            paginatedPayments.map((data: PaymentsData, index: number) => (
                                                <tr
                                                    key={index}
                                                    className="border-b border-[#E5E7EB] sm:table-row block sm:border-0 mb-4 sm:mb-0 rounded-lg sm:rounded-none shadow-sm sm:shadow-none"
                                                >
                                                    <td className="text-[#05073C] text-xs sm:text-sm px-2 sm:px-4 md:px-6 py-2 sm:py-4 block sm:table-cell">
                                                        <span className="sm:hidden font-semibold text-[#6B7280]">Date: </span>
                                                        {data?.booking_date?.split("T")[0]}
                                                    </td>
                                                    <td className="px-2 sm:px-4 md:px-6 py-2 sm:py-4 block sm:table-cell">
                                                        <span className="sm:hidden font-semibold text-[#6B7280]">Tour: </span>
                                                        <div>
                                                            <h6 className="text-[#05073C] text-xs sm:text-sm font-semibold">{data?.tour?.tour_title}</h6>
                                                            <p className="text-[#6B7280] text-[10px] sm:text-xs">{data?.status}</p>
                                                        </div>
                                                    </td>
                                                    <td className="text-[#05073C] text-xs sm:text-sm font-semibold px-2 sm:px-4 md:px-6 py-2 sm:py-4 block sm:table-cell">
                                                        <span className="sm:hidden font-semibold text-[#6B7280]">Amount: </span>
                                                        ৳{data?.booking_amount}
                                                    </td>
                                                    <td className="px-2 sm:px-4 md:px-6 py-2 sm:py-4 block sm:table-cell">
                                                        <span className="sm:hidden font-semibold text-[#6B7280]">Status: </span>
                                                        <div
                                                            className={`${
                                                                data?.status === "Success"
                                                                    ? "bg-[#DCFAED] text-[#0EAD69]"
                                                                    : data?.booking_status === "cancelled"
                                                                    ? "bg-[#FEE2E2] text-[#991B1B]"
                                                                    : "bg-[#FEF9C3] text-[#854D0E]"
                                                            } text-[10px] sm:text-xs font-semibold px-2 sm:px-3 py-1 rounded-full inline-block text-center`}
                                                        >
                                                            {data?.booking_status}
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))
                                        )}
                                    </tbody>
                                </table>
                            </div>
                            {/* Pagination Section - Improved responsiveness */}
                            <div className='flex flex-col sm:flex-row justify-between items-center p-2 sm:p-4 md:p-6 gap-2 sm:gap-4 border-t border-[#E5E7EB]'>
                                <div className='text-xs sm:text-sm text-[#05073C] font-semibold order-2 sm:order-1'>
                                    Page {currentPage} of {totalPages}
                                </div>
                                <div className='flex items-center gap-1 sm:gap-2 order-1 sm:order-2'>
                                    <button
                                        className='text-[#05073C] text-xs sm:text-sm font-semibold disabled:opacity-50 px-2 py-1 cursor-pointer disabled:cursor-not-allowed'
                                        disabled={currentPage === 1}
                                        onClick={() => handlePageChange(currentPage - 1)}
                                    >
                                        Prev
                                    </button>
                                    <div className="hidden sm:flex items-center gap-1 sm:gap-2">
                                        {getPageNumbers().map((pageNum, index) => (
                                            pageNum === '...' ? (
                                                <span
                                                    key={`ellipsis-${index}`}
                                                    className="text-xs sm:text-sm font-semibold text-[#05073C] w-6 sm:w-8 h-6 sm:h-8 flex justify-center items-center"
                                                >
                                                    ...
                                                </span>
                                            ) : (
                                                <button
                                                    key={`page-${pageNum}`}
                                                    onClick={() => handlePageChange(pageNum as number)}
                                                    className={`text-xs sm:text-sm font-semibold ${currentPage === pageNum
                                                            ? 'bg-[#2F80ED] text-white'
                                                            : 'text-[#05073C] border border-[#F1F1F1]'
                                                        } rounded-lg w-6 sm:w-8 h-6 sm:h-8 flex justify-center items-center cursor-pointer`}
                                                >
                                                    {pageNum}
                                                </button>
                                            )
                                        ))}
                                    </div>
                                    <button
                                        className='text-[#05073C] text-xs sm:text-sm font-semibold px-2 py-1 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed'
                                        disabled={currentPage === totalPages}
                                        onClick={() => handlePageChange(currentPage + 1)}
                                    >
                                        Next
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default Payments;
