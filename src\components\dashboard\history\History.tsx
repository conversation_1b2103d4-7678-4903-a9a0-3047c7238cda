import { useState, useEffect, useRef } from "react";
import { useGetThreadMessagesQuery, useGetChatHistoryQuery } from "../../../store/features/chat/chatApi";
import { useDispatch, useSelector } from "react-redux";
import { threadUid as setThreadUid, setMessages, setTourDetails, setHistoryLoading } from "../../../store/features/chat/chatSlice";
import { useParams, useNavigate } from "react-router-dom";
import { addMessage } from "../../../store/features/chat/chatSlice";
import { useDeleteThreadMutation, useRenameThreadMutation } from "../../../store/features/chat/chatApi";
import { RootState } from "../../../store/store";
import LoaderDark from "../../common/LoaderDark";
import Cookies from "js-cookie";

const History = ({ setIsOpen }: { setIsOpen: (isOpen: boolean) => void }) => {
  const navigate = useNavigate();
  const [deletingThread, setDeletingThread] = useState<string | null>(null);
  const [threadToDelete, setThreadToDelete] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const updatedThreadUid = useSelector((state: RootState) => state.chat.threadUid);
  const [openDropdownThread, setOpenDropdownThread] = useState<string | null>(null);
  const [editingThreadId, setEditingThreadId] = useState<string | null>(null);
  const [newTitle, setNewTitle] = useState("");
  const [dropdownPosition, setDropdownPosition] = useState<'bottom' | 'top'>('bottom');

  const isAuthenticated = !!Cookies.get('accessToken');
  const { data: threads } = useGetThreadMessagesQuery(undefined, {
    skip: !isAuthenticated
  });
  const [selectedThread, setSelectedThread] = useState<string | null>(null);
  const { data: chatHistory, isLoading: isChatHistoryLoading } = useGetChatHistoryQuery(selectedThread, {
    skip: !selectedThread
  });
  const dispatch = useDispatch();
  const { threadUid: urlThreadUid } = useParams();
  const [deleteThread, { isLoading: isDeleting }] = useDeleteThreadMutation();
  const [renameThread, { isLoading: isRenaming }] = useRenameThreadMutation();

  const editContainerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleDeleteConfirm = async () => {
    if (!threadToDelete) return;

    setDeletingThread(threadToDelete);
    try {
      await deleteThread(threadToDelete).unwrap();
      if (threadToDelete === updatedThreadUid) {
        handleNewChat();
      }
    } catch (error) {
      console.error("Failed to delete thread:", error);
    } finally {
      setDeletingThread(null);
      setShowDeleteModal(false);
      setThreadToDelete(null);
    }
  }

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
    setThreadToDelete(null);
  }

  useEffect(() => {
    dispatch(setHistoryLoading(isChatHistoryLoading));
  }, [isChatHistoryLoading, dispatch]);

  const handleHistoryClick = (thread: string | null) => {
    setSelectedThread(thread);
    dispatch(setThreadUid(thread));
    navigate(`/chat/${thread}`);
    setIsOpen(false);
  }

  const handleNewChat = () => {
    dispatch(setThreadUid(null));
    dispatch(setMessages([]));
    dispatch(addMessage({
      content: "Hi there! I'm Zoe, your travel assistant. I'm here to help you find the best travel packages. To get started, where would you like to go? And how long are you planning to stay?",
      sender: "ai"
    }));
    navigate('/');
  }

  const handleEditStart = (thread: { uid: string, thread_name: string }) => {
    setEditingThreadId(thread.uid);
    setNewTitle(thread.thread_name || thread.uid);
    setOpenDropdownThread(null);
  };

  const handleRenameSubmit = async (threadUid: string) => {
    if (!newTitle.trim() || isRenaming) return;

    try {
      await renameThread({
        thread_uid: threadUid,
        new_title: newTitle.trim()
      }).unwrap();
    } catch (error) {
      console.error("Failed to rename thread:", error);
    } finally {
      setEditingThreadId(null);
      setNewTitle("");
    }
  };

  const handleRenameCancel = () => {
    setEditingThreadId(null);
    setNewTitle("");
  };

  useEffect(() => {
    // If there's a thread ID in the URL, select it
    if (urlThreadUid !== selectedThread) {
      setSelectedThread(urlThreadUid || null);
      dispatch(setThreadUid(urlThreadUid || null));
    }
  }, [urlThreadUid, selectedThread, dispatch]);

  useEffect(() => {
    const reverseMessages = chatHistory?.data?.messages
      ? [...chatHistory.data.messages].reverse()
      : undefined;

    if (chatHistory?.data?.messages) {
      let foundTourDetails = null;
      // Parse messages
      const parsedMessages = reverseMessages?.map((msg: { context: string, message: string }) => {
        if (msg.context !== "AI") {
          return { content: msg.message, sender: "user" };
        } else {
          let content = "";
          let tourDetails = null;
          let responseType = "text";
          let parsed = null;

          try {
            // Try to parse as JSON, if it fails, treat as plain text
            parsed = typeof msg.message === 'string' ? JSON.parse(msg.message) : msg.message;

            // Extract response type
            responseType = parsed?.type || "text";

            // Extract content based on different possible structures
            if (parsed?.response?.message) {
              content = parsed.response.message;
            } else if (parsed?.text_response?.message) {
              content = parsed.text_response.message;
            } else if (parsed?.data?.text_response?.message) {
              content = parsed.data.text_response.message;
            } else if (typeof parsed?.response === "string") {
              content = parsed.response;
            }

            // Handle tour details for various response types
            if (responseType === "tour_packages" && parsed?.data) {
              tourDetails = parsed.data;
            } else if (responseType === "popular" && parsed?.data) {
              tourDetails = parsed.data;
            } else if (responseType === "json" && Array.isArray(parsed?.data)) {
              tourDetails = parsed.data;
            } else {
              // Try to find tour details in various locations
              tourDetails = parsed?.data?.tour_details ||
                parsed?.tour_details ||
                (parsed?.data && Array.isArray(parsed.data) ? parsed.data : null);
            }
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
          } catch (e) {
            // If parsing fails, use the raw message as content
            content = msg.message;
            responseType = "text";
          }

          // Final fallback - if content is still empty, use the original message
          if (!content || content.trim() === "") {
            content = msg.message;
          }

          if (tourDetails && Array.isArray(tourDetails) && tourDetails.length > 0) {
            foundTourDetails = tourDetails;
          }

          if (responseType === "popular" && parsed?.data) {
            return {
              content,
              sender: "ai",
              responseType: responseType,
              tourDetails,
              popularDestinations: parsed.data
            };
          } else if (responseType === "tour_packages" && parsed?.data) {
            return {
              content,
              sender: "ai",
              responseType: responseType,
              tourDetails,
              tourPackages: parsed.data,
              meta_details: parsed?.meta_details || {}
            };
          } else {
            return {
              content,
              sender: "ai",
              responseType: responseType,
              extracontent: parsed?.response?.extra_context,
              tourDetails
            };
          }
        }
      });

      dispatch(setMessages(parsedMessages || []));

      // If we found tour details, set them globally
      if (foundTourDetails) {
        dispatch(setTourDetails(foundTourDetails));
      }
    }
  }, [chatHistory, dispatch]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Handle edit mode click outside
      if (editingThreadId && editContainerRef.current && !editContainerRef.current.contains(event.target as Node)) {
        handleRenameCancel();
      }

      // Handle dropdown click outside
      if (openDropdownThread && dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpenDropdownThread(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [editingThreadId, openDropdownThread]);

  useEffect(() => {
    if (openDropdownThread) {
      const threadElement = document.getElementById(`thread-${openDropdownThread}`);
      if (threadElement) {
        const rect = threadElement.getBoundingClientRect();
        const spaceBelow = window.innerHeight - rect.bottom;
        setDropdownPosition(spaceBelow < 100 ? 'top' : 'bottom');
      }
    }
  }, [openDropdownThread]);

  return (
    <div className="max-w-full">
      <div className="overflow-y-auto pr-1" style={{
        height: 'calc(var(--vh, 1vh) * 100 - 530px)'
      }}>
        {threads?.data?.length === 0 ? (
          <div className="text-center text-gray-500 py-4 bg-[#F9FAFB] rounded-lg">
            No chat history yet
          </div>
        ) : (
          <ul className="space-y-2">
            {threads?.data?.map((thread: { id: number, uid: string, thread_name: string }) => (
              <li
                key={`threads-${thread.uid}`}
                id={`thread-${thread.uid}`}
                className="relative"
              >
                <div
                  className={`flex items-center py-3 px-3 rounded-md transition-all duration-200 group relative
                    ${updatedThreadUid === thread.uid
                      ? 'bg-[#2563EB] text-white'
                      : 'bg-[#F3F4F6] hover:bg-[#E7ECF9] text-[#05073C]'}`}
                >
                  {editingThreadId === thread.uid ? (
                    // Edit mode
                    <div ref={editContainerRef} className="flex gap-1.5">
                      <input
                        type="text"
                        value={newTitle}
                        onChange={(e) => setNewTitle(e.target.value)}
                        className="w-full px-3 border-none outline-none rounded border text-gray-900 text-sm focus:outline-none focus:ring-blue-500 bg-white"
                        autoFocus
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleRenameSubmit(thread.uid);
                          } else if (e.key === 'Escape') {
                            handleRenameCancel();
                          }
                        }}
                      />
                      <div className="flex justify-end">
                        <button
                          onClick={() => handleRenameSubmit(thread.uid)}
                          disabled={isRenaming}
                          className={`rounded-md transition-colors duration-200 bg-white p-1.5
                            ${isRenaming
                              ? 'opacity-50 cursor-not-allowed'
                              : 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'}`}
                        >
                          {isRenaming ? (
                            <LoaderDark />
                          ) : (
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>
                  ) : (
                    // View mode
                    <>
                      <div
                        className="flex-1 truncate pr-8 text-[14px] cursor-pointer"
                        onClick={() => handleHistoryClick(thread?.uid)}
                      >
                        {thread?.thread_name || thread?.uid}
                      </div>
                      <button
                        type="button"
                        disabled={isDeleting && deletingThread === thread?.uid}
                        onClick={(e) => {
                          e.stopPropagation();
                          setOpenDropdownThread(openDropdownThread === thread.uid ? null : thread.uid);
                        }}
                        className={`absolute right-2 p-1.5 rounded-md transition-all duration-200 cursor-pointer
                          ${updatedThreadUid === thread.uid
                            ? 'hover:bg-blue-700 text-white'
                            : 'hover:bg-gray-200 text-gray-600'}
                          ${isDeleting && deletingThread === thread?.uid ? 'opacity-70' :
                            openDropdownThread === thread.uid ? 'opacity-100' :
                              'md:opacity-0 md:group-hover:opacity-100 opacity-100'}`}
                      >
                        {isDeleting && deletingThread === thread?.uid ? (
                          <LoaderDark />
                        ) : (
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                          </svg>
                        )}
                      </button>
                    </>
                  )}
                </div>

                {/* Dropdown Menu */}
                {openDropdownThread === thread.uid && (
                  <div
                    ref={dropdownRef}
                    className={`absolute ${dropdownPosition === 'bottom'
                      ? 'top-full mt-1'
                      : 'bottom-full mb-1'
                      } right-0 z-[999] min-w-[120px]`}
                  >
                    <div className="relative bg-white border border-gray-200 rounded-lg shadow-lg py-1 flex flex-col">
                      <div
                        className={`absolute ${dropdownPosition === 'bottom'
                          ? '-top-2 border-t border-l'
                          : '-bottom-2 border-b border-r'
                          } right-[17px] w-3 h-3 bg-white border-gray-200 ${dropdownPosition === 'bottom' ? 'rotate-45' : '-rotate-45'
                          } z-10`}
                      />
                      <button
                        className="flex items-center gap-2 px-4 py-2 text-left text-sm font-medium text-red-500 hover:bg-red-50 hover:text-red-700 transition-colors whitespace-nowrap"
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowDeleteModal(true);
                          setThreadToDelete(thread.uid);
                          setOpenDropdownThread(null);
                        }}
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Delete
                      </button>
                      <div className="border-t border-gray-100 my-1"></div>
                      <button
                        className="flex items-center gap-2 px-4 py-2 text-left text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors whitespace-nowrap"
                        onClick={() => handleEditStart(thread)}
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Edit
                      </button>
                    </div>
                  </div>
                )}
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 w-screen bg-black/60 bg-opacity-50 flex items-center justify-center z-[9999]" onClick={handleDeleteCancel}>
          <div className="bg-white rounded-lg p-6 max-w-sm w-full shadow-lg" onClick={e => e.stopPropagation()}>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Delete Conversation</h3>
            <p className="text-sm text-gray-500 mb-5">
              Are you sure you want to delete this conversation? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={handleDeleteCancel}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirm}
                className="px-4 py-2 rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700"
                disabled={isDeleting}
              >
                {isDeleting ? 'Deleting...' : 'Delete'}
              </button>

            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default History;