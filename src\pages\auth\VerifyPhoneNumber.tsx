import { useState } from 'react';
import VerifyPhoneNumberModal from './VerifyPhoneNumberModal';
import VerifyPhoneOtpModal from './VerifyPhoneOtpModal';
import { useNavigate } from 'react-router-dom';

interface VerifyPhoneNumberProps {
     isModal?: boolean;
     onModalClose?: () => void;
     onModalSuccess?: () => void;
}

const VerifyPhoneNumber = ({ isModal, onModalClose, onModalSuccess }: VerifyPhoneNumberProps) => {
     const [isPhoneModalOpen, setIsPhoneModalOpen] = useState(true);
     const [isOtpModalOpen, setIsOtpModalOpen] = useState(false);
     const navigate = useNavigate();

     const handlePhoneVerificationSuccess = () => {
          setIsPhoneModalOpen(false);
          setIsOtpModalOpen(true);
     };

     const handleOtpVerificationSuccess = () => {
          setIsOtpModalOpen(false);
          if (isModal && onModalSuccess) {
               onModalSuccess();
          } else {
               navigate('/confirm-password');
          }
     };

     const handlePhoneModalClose = () => {
          setIsPhoneModalOpen(false);
          if (isModal && onModalClose) {
               onModalClose();
          } else {
               navigate('/forgot-password');
          }
     };

     const handleOtpModalClose = () => {
          setIsOtpModalOpen(false);
          if (isModal) {
               setIsPhoneModalOpen(true);
          } else {
               navigate('/forgot-password');
          }
     };

     return (
          <>
               <VerifyPhoneNumberModal
                    isOpen={isPhoneModalOpen}
                    onClose={handlePhoneModalClose}
                    onSuccess={handlePhoneVerificationSuccess}
               />
               
               <VerifyPhoneOtpModal
                    isOpen={isOtpModalOpen}
                    onClose={handleOtpModalClose}
                    onSuccess={handleOtpVerificationSuccess}
               />
          </>
     );
};

export default VerifyPhoneNumber;