import { useState } from "react";
import { FiChevronDown, FiChevronUp, FiPlus, FiMinus } from "react-icons/fi";
import { useDispatch, useSelector } from "react-redux";
import { addMessage, threadUid as setThreadUid, setLoading } from "../../../store/features/chat/chatSlice";
import { useSendMessageMutation } from "../../../store/features/chat/chatApi";
import { RootState } from "../../../store/store";

const SelectTravellers = () => {
     const [isOpen, setIsOpen] = useState(false);
     const [counts, setCounts] = useState({
          adult: 0,
          child: 0,
          infant: 0
     });
     // const tourDetails = useSelector((state: RootState) => state.tours.tourDetails);
     const messages = useSelector((state: RootState) => state.chat.messages);
     // Log only the latest 'tour' value from 'extracontent'
     const travellersAllowed = messages?.filter((message) => message.responseType === "number_of_travellers")


     const dispatch = useDispatch();
     const [sendMessage] = useSendMessageMutation();
     const threadUid = useSelector((state: RootState) => state.chat.threadUid);

     const totalTravellers = counts.adult + counts.child + counts.infant;

     const handleIncrement = (type: "adult" | "child" | "infant") => {
          setCounts({ ...counts, [type]: counts[type] + 1 });
     };

     const handleDecrement = (type: "adult" | "child" | "infant") => {
          if (counts[type] > 0) {
               setCounts({ ...counts, [type]: counts[type] - 1 });
          }
     };

     const handleSave = async () => {
          setIsOpen(false);

          const totalTravelers = totalTravellers;

          if (totalTravelers === 0) {
               dispatch(addMessage({
                    content: "Please select at least one traveler",
                    sender: "ai"
               }));
               return;
          }

          const messageText = `I want to book for ${totalTravelers} traveller${totalTravelers !== 1 ? 's' : ''}`;

          // Add user message to the chat
          dispatch(addMessage({
               content: messageText,
               sender: "user"
          }));

          dispatch(setLoading(true));

          const messagePayload = {
               message: messageText,
               thread_uid: threadUid || "",
               context_data: {
                    intent: "booking",
                    action: "number_of_travellers",
                    data: {
                         adult: counts.adult,
                         child: counts.child,
                         infant: counts.infant
                    }
               }
          };

          try {
               // Send the message to the API
               const response = await sendMessage(messagePayload).unwrap();

               // Get thread UID from the response
               const responseThreadUid = response?.data?.thread_uid || threadUid;

               // Update the thread UID in the Redux store
               if (responseThreadUid) {
                    dispatch(setThreadUid(responseThreadUid));
               }

               // Process AI response
               let aiResponseMessage = "";
               let responseType = "text";

               if (response.data?.ai_response?.type) {
                    responseType = response.data.ai_response.type;
               }

               if (response.data?.ai_response?.data?.text_response?.message) {
                    aiResponseMessage = response.data.ai_response.data.text_response.message;
               }
               else if (response.data?.ai_response?.text_response?.message) {
                    aiResponseMessage = response.data.ai_response.text_response.message;
               }
               else if (response.data?.ai_response?.response?.message) {
                    aiResponseMessage = response.data.ai_response.response.message;
               }

               // Add AI response to chat
               if (aiResponseMessage) {
                    dispatch(addMessage({
                         content: aiResponseMessage,
                         sender: "ai",
                         responseType: responseType
                    }));
               } else {
                    dispatch(addMessage({
                         content: "Thank you for providing your traveller information. How else can I assist with your booking?",
                         sender: "ai"
                    }));
               }
          } catch (error) {
               console.error("Error sending traveller count:", error);

               // Add error message to chat
               dispatch(addMessage({
                    content: "Sorry, I couldn't process your traveller information at the moment. Please try again later.",
                    sender: "ai"
               }));
          } finally {
               // Set loading state back to false
               dispatch(setLoading(false));
          }
     };

     const latestAllowed = travellersAllowed[travellersAllowed.length - 1]?.extracontent?.tour;

     return (
          <div className="mt-3 w-full max-w-[300px] sm:max-w-[350px] md:max-w-[400px]">
               {/* Dropdown header */}
               <div
                    className="bg-white border border-gray-200 rounded-full p-2 sm:p-3 flex justify-between items-center cursor-pointer w-[300px]"
                    onClick={() => setIsOpen(!isOpen)}
               >
                    <span className="text-gray-700 text-sm sm:text-base">
                         {totalTravellers === 0 ? "0 Travelers" : `${totalTravellers} Traveler${totalTravellers !== 1 ? 's' : ''}`}
                    </span>
                    {isOpen ? <FiChevronUp /> : <FiChevronDown />}
               </div>

               {/* Dropdown content */}
               {isOpen && (
                    <div className="mt-2  bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
                         {/* Travelers selection */}
                         <div className="p-3 sm:p-4">
                              {/* Adult selection */}
                              {latestAllowed && latestAllowed?.is_adult_allowed && <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-4">
                                   <div className="mt-1 sm:mt-2.5">
                                        <p className="text-[#05073C] font-medium text-sm sm:text-base">Adult (18+ years)</p>
                                   </div>
                                   <div className="flex items-center gap-3 sm:gap-4">
                                        <button
                                             className="w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center border border-gray-300"
                                             onClick={() => handleDecrement("adult")}
                                        >
                                             <FiMinus size={14} className="sm:text-base" />
                                        </button>
                                        <span className="w-4 text-center">{counts.adult}</span>
                                        <button
                                             className="w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center border border-gray-300"
                                             onClick={() => handleIncrement("adult")}
                                        >
                                             <FiPlus size={14} className="sm:text-base" />
                                        </button>
                                   </div>
                              </div>}

                              {/* Child selection */}
                              {latestAllowed && latestAllowed?.is_child_allowed && <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-4 py-2 sm:py-3 mt-2 sm:mt-0">
                                   <div className="mt-1 sm:mt-2.5">
                                        <p className="text-[#05073C] font-medium text-sm sm:text-base">Child (13-17 years)</p>
                                   </div>
                                   <div className="flex items-center gap-3 sm:gap-4">
                                        <button
                                             className="w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center border border-gray-300"
                                             onClick={() => handleDecrement("child")}
                                        >
                                             <FiMinus size={14} className="sm:text-base" />
                                        </button>
                                        <span className="w-4 text-center">{counts.child}</span>
                                        <button
                                             className="w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center border border-gray-300"
                                             onClick={() => handleIncrement("child")}
                                        >
                                             <FiPlus size={14} className="sm:text-base" />
                                        </button>
                                   </div>
                              </div>}

                              {/* Infant selection */}
                              {latestAllowed && latestAllowed?.is_infant_allowed && <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-4 mt-2 sm:mt-0">
                                   <div className="mt-1 sm:mt-2.5">
                                        <p className="text-[#05073C] font-medium text-sm sm:text-base">Infant (0-12 years)</p>
                                   </div>
                                   <div className="flex items-center gap-3 sm:gap-4">
                                        <button
                                             className="w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center border border-gray-300"
                                             onClick={() => handleDecrement("infant")}
                                        >
                                             <FiMinus size={14} className="sm:text-base" />
                                        </button>
                                        <span className="w-4 text-center">{counts.infant}</span>
                                        <button
                                             className="w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center border border-gray-300"
                                             onClick={() => handleIncrement("infant")}
                                        >
                                             <FiPlus size={14} className="sm:text-base" />
                                        </button>
                                   </div>
                              </div>}
                         </div>

                         {/* Save button */}
                         <div className="p-3 border-t border-gray-100 flex justify-end">
                              <button
                                   className="bg-[#0D3FC6] text-white font-medium px-4 sm:px-6 py-1.5 sm:py-2 rounded-md text-sm sm:text-base"
                                   onClick={handleSave}
                              >
                                   Save
                              </button>
                         </div>
                    </div>
               )}
          </div>
     );
};

export default SelectTravellers;
