const TermContact = ({ terms }: { terms: string }) => {
     return (
          <div className="max-w-4xl mx-auto py-8 px-4">
               <h2 className="text-[18px] sm:text-[24px] md:text-[30px] md:leading-[40px] leading-[24px] sm:leading-[30px] font-bold mb-6 text-gray-800">
                    Terms & Conditions
               </h2>
               <div
                    className="mt-6 space-y-4 [&_h3]:text-xl [&_h3]:font-semibold [&_h3]:text-gray-800 [&_h3]:mb-2
                         [&_p]:text-gray-600 [&_p]:leading-relaxed [&_p]:mb-4
                         [&_table]:w-full [&_table]:border-collapse [&_table]:mb-4
                         [&_td]:border [&_td]:p-3 [&_td]:text-sm [&_td]:text-gray-600
                         [&_th]:bg-gray-50 [&_th]:border [&_th]:p-3 [&_th]:text-left [&_th]:font-medium
                         [&_ul]:list-disc [&_ul]:pl-5 [&_ul]:space-y-2
                         [&_li]:text-gray-600"
                    dangerouslySetInnerHTML={{ __html: terms }}
               />
          </div>
     )
}

export default TermContact;