import { useForm } from 'react-hook-form';
import userIcon from '../../assets/user-icon.svg';
import airplane from '../../assets/plane-icon.svg';
import LockIcon from '../../assets/lock-icon.svg';
import video from '../../assets/videos/login-video.mp4';
import { useNavigate } from 'react-router-dom';
import AuthButton from '../../components/auth/AuthButton';
import InputField from '../../components/common/InputField';
import { useLoginUserMutation } from '../../store/features/auth/authApi';
import { toast } from 'react-hot-toast';
import Cookies from 'js-cookie';
import { useEffect, useState } from 'react';
import LoaderDark from '../../components/common/LoaderDark';
import LeftSide from '../../components/auth/LeftSide';

interface LoginFormInputs {
     email: string;
     password: string;
}

const Login = () => {
     const isAuthenticated = Cookies.get("accessToken");
     const [loading, setLoading] = useState(true)
     const [loginUser, { isLoading }] = useLoginUserMutation();
     const navigate = useNavigate();
     const [showPassword, setShowPassword] = useState(false);

     const {
          register,
          handleSubmit,
          formState: { errors },
     } = useForm<LoginFormInputs>();

     const onSubmit = async (data: LoginFormInputs) => {
          try {
               const userData = {
                    username_or_email: data.email,
                    password: data.password,
                    request_source: "admin"
               };

               const response = await loginUser(userData);
               if (response.error) {
                    // @ts-expect-error error
                    toast.error(response.error.data?.msg || "An unexpected error occurred");
                    return;
               } else {
                    if (response.data && response.data.access) {
                         Cookies.set('accessToken', response.data.access, { expires: 7 });
                    }
                    window.location.href = "/admin/dashboard";
               }
          } catch (error) {
               console.log("Error: ", error);
               toast.error("Invalid email or password");
          }
     };

     useEffect(() => {
          if (isAuthenticated) {
               window.location.href = "/admin/dashboard";
          } else {
               setLoading(false)
          }
     }, [isAuthenticated, navigate]);


     return (
          <>
               {loading ? <div className="flex flex-col md:flex-row md:h-screen w-full">
                    <div className="w-full h-full flex ss items-center justify-center">
                         <LoaderDark />
                    </div>
               </div> : <div className="flex flex-col md:flex-row md:h-screen w-full">
                    <LeftSide video={video} title="Your next adventure is calling!" description="Log in and uncover the best travel deals in seconds." />

                    <div className="md:h-[100vh] overflow-y-auto w-full md:w-1/2 bg-white flex flex-col justify-center items-center px-[20px] pt-[80px] py-[30px] relative">
                         <div className="w-full max-w-[460px] py-[30px]">
                              <div className='absolute right-0 sm:w-[224px] w-[150px] mt-[-50px] ms-auto'>
                                   <img src={airplane} alt="Airplane" className='w-full h-full' />
                              </div>
                              <div className='text-center mb-[35px]'>
                                   <h1 className="lg:text-[55px] sm:text-[40px] text-[30px] font-bold sm:leading-[48px] md:leading-[40px] leading-[30px] text-[#0D3FC6] mb-[12px]">Welcome</h1>
                                   <p className="text-[#********] text-[16px]">Login into your account</p>
                              </div>

                              <form onSubmit={handleSubmit(onSubmit)}>
                                   <InputField<LoginFormInputs>
                                        label="Email"
                                        name="email"
                                        type="email"
                                        placeholder="Enter email"
                                        icon={userIcon}
                                        register={register}
                                        error={errors.email}
                                        validation={{
                                             required: "Email is required",
                                             pattern: {
                                                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                                                  message: "Invalid email address"
                                             }
                                        }}
                                   />

                                   <div className='relative'>
                                        <InputField<LoginFormInputs>
                                             label="Password"
                                             name="password"
                                             type={showPassword ? "text" : "password"}
                                             placeholder="Enter your password"
                                             icon={LockIcon}
                                             register={register}
                                             error={errors.password}
                                             validation={{
                                                  required: "Password is required",
                                                  minLength: {
                                                       value: 6,
                                                       message: "Password must be at least 6 characters"
                                                  }
                                             }}
                                        />

                                        <img src={showPassword ? "/eye-open.svg" : "/eye-close.svg"} alt="Eye Close" className=' cursor-pointer absolute right-5 top-0 bottom-0 my-auto w-6 h-6' onClick={() => setShowPassword(!showPassword)} />
                                   </div>

                                   <AuthButton isLoading={isLoading}>
                                        LOGIN
                                   </AuthButton>
                              </form>
                         </div>
                    </div>
               </div>}
          </>
     );
};

export default Login;