import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import Cookies from 'js-cookie';

export const tourClickApi = createApi({
     reducerPath: "tourClickApi",
     baseQuery: fetchBaseQuery({
          baseUrl: `${import.meta.env.VITE_API_URL}`,
          credentials: "include",
          prepareHeaders: (headers, { endpoint }) => {
               const token = Cookies.get('accessToken');

               if (token && endpoint !== 'loginUser' && endpoint !== 'registerUser') {
                    try {
                         const parts = token.split('.');
                         if (parts.length === 3) {
                              JSON.parse(atob(parts[1]));
                              headers.set('Authorization', `Bearer ${token}`);
                         } else {
                              Cookies.remove('accessToken');
                              window.location.reload();
                         }
                         // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    } catch (e: any) {
                         console.log(e, "::error");
                         Cookies.remove('accessToken');
                    }
               }

               return headers;
          },
     }),
     tagTypes: ['TourClick'],

     endpoints: (builder) => ({
          externalTourClick: builder.mutation({
               // eslint-disable-next-line @typescript-eslint/no-explicit-any
               query: (data: any) => ({
                    url: `/database/external-tour-click/`,
                    method: "POST",
                    body: data
               }),
               invalidatesTags: ['TourClick']
          })
     }),
});

export const { useExternalTourClickMutation } = tourClickApi;
