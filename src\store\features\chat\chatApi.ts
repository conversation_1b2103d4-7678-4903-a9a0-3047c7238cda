import Cookies from 'js-cookie';
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const chatApi = createApi({
     reducerPath: "chatApi",
     baseQuery: fetchBaseQuery({
          baseUrl: `${import.meta.env.VITE_API_URL}/chat`,
          credentials: "include",
          prepareHeaders: (headers, { endpoint }) => {
               const token = Cookies.get('accessToken');

               if (token && endpoint !== 'loginUser' && endpoint !== 'registerUser') {
                    try {
                         const parts = token.split('.');
                         if (parts.length === 3) {
                              JSON.parse(atob(parts[1]));
                              headers.set('Authorization', `Bearer ${token}`);
                         } else {
                              Cookies.remove('accessToken');
                              window.location.reload();
                         }
                         // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    } catch (e: any) {
                         console.log(e, "::error");
                         Cookies.remove('accessToken');
                    }
               }

               return headers;
          },
     }),
     tagTypes: ['Threads', 'ChatHistory'],
     endpoints: (builder) => ({
          sendMessage: builder.mutation({
               query: (message) => ({
                    url: "/threads/send-message/",
                    method: "POST",
                    body: message,
               }),
               invalidatesTags: (result) => {
                    const threadUid = result?.data?.thread_uid;
                    return [
                         { type: 'Threads', id: 'LIST' },
                         threadUid ? { type: 'ChatHistory', id: threadUid } : { type: 'ChatHistory', id: 'LIST' }
                    ];
               }
          }),

          getThreadMessages: builder.query({
               query: () => ({
                    url: `/threads/all/`,
               }),
               providesTags: [{ type: 'Threads', id: 'LIST' }],
          }),

          getChatHistory: builder.query({
               query: (id) => ({
                    url: `/threads/${id}`,
               }),
               providesTags: (_result, _error, id) => [{ type: 'ChatHistory', id }],
          }),

          deleteThread: builder.mutation({
               query: (id) => ({
                    url: `/threads/${id}/`,
                    method: "DELETE",
               }),
               invalidatesTags: () => [{ type: 'Threads', id: 'LIST' }],
          }),

          renameThread: builder.mutation({
               query: (data) => ({
                    url: "/rename-thread/",
                    method: "PUT",
                    body: data,
               }),
               invalidatesTags: () => [{ type: 'Threads', id: 'LIST' }],
          }),

         
     }),
})

export const { useSendMessageMutation, useGetThreadMessagesQuery, useGetChatHistoryQuery, useDeleteThreadMutation, useRenameThreadMutation } = chatApi;
