import Cookies from 'js-cookie';
import { Middleware } from '@reduxjs/toolkit';
import { logout } from '../store/features/auth/authSlice';
import { threadUid as setThreadUid, setMessages, addMessage } from '../store/features/chat/chatSlice';

// Function to handle token expired/invalid 
export const handleAuthError = () => {
     Cookies.remove("accessToken");

     window.location.href = '/';
};

interface AuthErrorAction {
     type: string;
     payload: {
          status: number;
     };
}

// Add this function to validate token format
export const isValidToken = (token: string | undefined): boolean => {
     if (!token) return false;

     try {
          // Check if token has valid JWT format (header.payload.signature)
          const parts = token.split('.');
          if (parts.length !== 3) return false;

          // Try to decode the payload
          JSON.parse(atob(parts[1]));
          return true;
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
     } catch (e: any) {
          console.log(e, "::error");
          // If parsing fails, token is invalid
          Cookies.remove('accessToken');
          return false;
     }
}

// Modified middleware
export const authErrorMiddleware: Middleware = ({ dispatch }) => next => (action: unknown) => {
     const token = Cookies.get('accessToken');
     const isAdmin = import.meta.env.VITE_APP_TYPE;
     if (token && !isValidToken(token)) {
          Cookies.remove('accessToken');

          // Only redirect if not already on login page
          if (!window.location.pathname.includes('/login')) {
               dispatch(logout());
               window.location.href = isAdmin === 'admin' ? '/admin/login' : '/login';
               return next(action);
          }

     }

     // Continue with normal error handling
     if (typeof action === 'object' && action !== null && 'type' in action && 'payload' in action) {
          const { type, payload } = action as AuthErrorAction;

          if (type.endsWith('/rejected') && payload) {
               const { status } = payload;

               if ((status === 401 || status === 403) && !window.location.pathname.includes('/login')) {
                    // Only clear token once to prevent loops
                    if (Cookies.get('accessToken')) {
                         Cookies.remove("accessToken");

                         dispatch(setThreadUid(null));
                         dispatch(setMessages([]));
                         dispatch(addMessage({
                              content: "Hi there! I'm Zoe, your travel assistant. I'm here to help you find the best travel packages. To get started, where would you like to go? And how long are you planning to stay?",
                              sender: "ai"
                         }));

                         dispatch(logout());

                         window.location.href = isAdmin === 'admin' ? '/admin/login' : '/login';
                    }
               }
          }
     }

     return next(action);
};
