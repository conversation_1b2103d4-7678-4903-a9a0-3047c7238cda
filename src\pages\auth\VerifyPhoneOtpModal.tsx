import { toast } from "react-hot-toast";
import { usePhoneNumberOtpMutation, useVerifyPhoneNumberOtpMutation } from "../../store/features/auth/authApi";
import { useForm, Controller } from 'react-hook-form';
import { useEffect, useRef, useState } from 'react';
import Cookies from 'js-cookie';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';

interface OtpInputs {
     otp: string[];
}

interface VerifyPhoneOtpModalProps {
     isOpen: boolean;
     onClose: () => void;
     onSuccess: () => void;
}

const VerifyPhoneOtpModal = ({ isOpen, onClose, onSuccess }: VerifyPhoneOtpModalProps) => {
     const [verifyPhoneNumberOtp] = useVerifyPhoneNumberOtpMutation();
     const [isVerifying, setIsVerifying] = useState(false);
     const forgetEmail = useSelector((state: RootState) => state.auth.forgotPasswordEmail);
     const [phoneNumberOtp] = usePhoneNumberOtpMutation();
     const firstInputRef = useRef<HTMLInputElement>(null);
     const userDetails = useSelector((state: RootState) => state.auth.userDetails);
     const userId = userDetails?.id || null;

     const { handleSubmit, control } = useForm<OtpInputs>({
          defaultValues: {
               otp: ['', '', '', '', '', ''],
          },
     });

     useEffect(() => {
          if (firstInputRef.current) {
               firstInputRef.current.focus();
          }
     }, [isOpen]);

     const onSubmit = async (data: OtpInputs) => {
          const otpValue = data.otp.join('');
          setIsVerifying(true);
          const verification_id = Cookies.get("verification_id");
          try {
               const response = await verifyPhoneNumberOtp({ otp: otpValue, phone: forgetEmail || "", verification_id: verification_id || "", user_id: userId || "" });
               if (response.error) {
                    // @ts-expect-error error
                    toast.error(response?.error?.data?.msg || "An unexpected error occurred");
               } else {
                    toast.success(response?.data?.msg || "OTP verified successfully");
                    Cookies.set("secure_token", response.data.secure_token);
                    onSuccess();
               }
          } catch (error) {
               console.log("Error: ", error);
          } finally {
               setIsVerifying(false);
          }
     };

     const handleResend = async () => {
          const phoneNumber = Cookies.get("phone_number");
          if (phoneNumber) {
               const response = await phoneNumberOtp({ phone: phoneNumber });
               if (response.error) {
                    // @ts-expect-error error
                    toast.error(response?.error?.data?.msg || "An unexpected error occurred");
               }
          }
     };

     if (!isOpen) return null;

     return (
          <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-[999]">
               <div className="bg-white rounded-lg p-8 max-w-[490px] w-full relative">
                    <button
                         onClick={onClose}
                         className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
                    >
                         ✕
                    </button>

                    <div className="text-center mb-[35px]">
                         <h1 className="text-[30px] font-bold leading-[30px] text-[#0D3FC6] mb-[12px]">
                              Verification
                         </h1>
                         <p className="text-[#00000080] text-[16px]">
                              Enter the 6 digits OTP sent to your Phone Number
                         </p>
                    </div>

                    <form onSubmit={handleSubmit(onSubmit)}>
                         <div className="flex sm:gap-[10px] gap-[2px] justify-between items-center mb-[24px]">
                              {Array.from({ length: 6 }).map((_, index) => (
                                   <Controller
                                        key={index}
                                        name={`otp.${index}`}
                                        control={control}
                                        render={({ field }) => (
                                             <input
                                                  {...field}
                                                  ref={index === 0 ? firstInputRef : null}
                                                  type="text"
                                                  maxLength={1}
                                                  className="sm:w-[54px] w-[40px] sm:h-[54px] h-[40px] text-[14px] font-semibold text-[#000000] leading-[18px] border border-[#DDDFE4] focus:outline-none rounded-[8px] text-center"
                                                  onChange={(e) => {
                                                       const value = e.target.value;
                                                       if (/^\d*$/.test(value)) {
                                                            field.onChange(value);
                                                            if (value && index < 5) {
                                                                 const nextSibling = document.querySelector<HTMLInputElement>(`input[name="otp.${index + 1}"]`);
                                                                 if (nextSibling) {
                                                                      nextSibling.focus();
                                                                 }
                                                            }
                                                       }
                                                  }}
                                                  onKeyDown={(e) => {
                                                       if (e.key === 'Backspace' && !field.value && index > 0) {
                                                            const prevSibling = document.querySelector<HTMLInputElement>(`input[name="otp.${index - 1}"]`);
                                                            if (prevSibling) {
                                                                 prevSibling.focus();
                                                            }
                                                       }
                                                  }}
                                             />
                                        )}
                                   />
                              ))}
                         </div>

                         <button
                              className="w-full bg-gradient-to-r from-[#0D3FC6] to-[#3793FF] text-white py-[16px] rounded-[8px] font-medium hover:bg-blue-700 transition-colors cursor-pointer !rounded-button whitespace-nowrap text-[14px] leading-[18px] uppercase flex items-center justify-center"
                              type="submit"
                              disabled={isVerifying}
                         >
                              {isVerifying ? (
                                   <>
                                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                             <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                             <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Verifying...
                                   </>
                              ) : (
                                   'Verify'
                              )}
                         </button>
                    </form>

                    <div className="flex items-center my-[25px] w-full max-w-[290px] mx-auto">
                         <div className="flex-1 border-t border-[#1C1C1C33]"></div>
                         <span className="px-4 text-[#05073C] text-[14px] leading-[18px]">
                              OR
                         </span>
                         <div className="flex-1 border-t border-[#1C1C1C33]"></div>
                    </div>

                    <div className="text-center text-[#05073C] font-normal mt-[30px] text-[14px] leading-[18px]">
                         Didn't receive the code? {" "}
                         <button onClick={handleResend} type="button" className="text-[#0D3FC6] font-semibold">
                              Resend
                         </button>
                    </div>
               </div>
          </div>
     );
};

export default VerifyPhoneOtpModal;
