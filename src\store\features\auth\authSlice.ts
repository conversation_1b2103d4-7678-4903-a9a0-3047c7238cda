import { createSlice } from "@reduxjs/toolkit";

interface User {
     id: string;
     name: string;
     email: string;
     role: string;
}

interface UserDetails {
     id: string;
     first_name: string;
     last_name: string;
     email: string;
     username: string;
     role: string;
     otp_verify: boolean;
     registration_source: string;
}

interface RegistrationData {
     username: string;
     email: string;
     phone: string;
     password: string;
}

interface AuthState {
     isAuthenticated: boolean;
     user: User | null;
     userDetails: UserDetails | null;
     forgotPasswordEmail: string | null;
     registrationData: RegistrationData | null;
}

const initialState: AuthState = {
     isAuthenticated: false,
     user: null,
     userDetails: null,
     forgotPasswordEmail: null,
     registrationData: null,
};

const authSlice = createSlice({
     name: "auth",
     initialState,
     reducers: {
          login: (state, action) => {
               state.isAuthenticated = true;
               state.user = action.payload;
          },
          setUserDetails: (state, action) => {
               state.userDetails = action.payload;
          },
          logout: (state) => {
               state.isAuthenticated = false;
               state.user = null;
          },
          setForgotPasswordEmail: (state, action) => {
               state.forgotPasswordEmail = action.payload;
          },
          setRegistrationData: (state, action) => {
               state.registrationData = action.payload;
          },
          clearRegistrationData: (state) => {
               state.registrationData = null;
          },
     },
});

export const {
     login,
     logout,
     setUserDetails,
     setForgotPasswordEmail,
     setRegistrationData,
     clearRegistrationData
} = authSlice.actions;
export default authSlice.reducer;
