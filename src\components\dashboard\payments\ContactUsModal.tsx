import { useForm } from 'react-hook-form';
import { useContactUsMutation } from '../../../store/features/contact-us/contactUsApi';
import { toast } from 'react-hot-toast';
import PhoneIcon from "../../../assets/phone-icon.svg";
import WhatsappIcon from "../../../assets/whatsapp-icon.svg";
import userIcon from '../../../assets/user-icon.svg';
import emailIcon from '../../../assets/email-icon.svg';
import numberIcon from '../../../assets/number-icon.svg';
import Loader from '../../common/Loader';

interface ContactUsFormInputs {
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    subject: string;
    message: string;
}

const ContactUsModal = ({ onClose }: { onClose: () => void }) => {
    const [contactUs, { isLoading }] = useContactUsMutation();
    const {
        register,
        handleSubmit,
        formState: { errors },
        reset
    } = useForm<ContactUsFormInputs>({
        mode: 'onBlur'
    });

    const onSubmit = async (data: ContactUsFormInputs) => {
        try {
            const response = await contactUs(data);
            if (response.error) {
                // @ts-expect-error error
                toast.error(response.error.data?.msg || "An unexpected error occurred");
                return;
            }

            toast.success(response.data?.msg || "Message sent successfully");
            reset();
            onClose();
        } catch (error) {
            console.log(error);
            toast.error("Failed to send message. Please try again.");
        }
    };

    return (
        <div>
            <div className="w-full h-full absolute bottom-0 left-0 z-[999]">
                <div className="bg-[#00000080] h-full w-full absolute top-0 left-0" onClick={onClose}></div>
                <div className="px-[10px] sm:px-[20px] absolute top-[50%] left-[50%] z-30 translate-x-[-50%] translate-y-[-50%] max-w-[745px] w-full">
                    <div className="bg-white rounded-[20px]">
                        <div className="flex justify-between items-center p-[25px] py-[15px] border-b border-[#E7ECF9]">
                            <h3 className="text-[20px] sm:text-[28px] font-semibold text-[#05073C]">Contact Us</h3>
                            <button className="cursor-pointer w-[30px] sm:w-[35px] h-[30px] sm:h-[35px] bg-[#E7ECF9] rounded-full flex items-center justify-center" onClick={onClose}>
                                <img src="/cross.svg" className="w-[15px] h-[15px]" alt="Close" />
                            </button>
                        </div>
                        <div className="px-[15px] py-[10px] sm:px-[25px] sm:py-[20px] max-h-[calc(100vh-100px)] overflow-y-auto">
                            <div className="flex gap-[10px] flex-col sm:flex-row mb-[20px] sm:mb-[30px]">
                                <div className="w-full flex gap-[13px] items-center bg-[#F4F4F6] rounded-[15px] py-[10px] sm:py-[17px] px-[15px] sm:px-[24px]">
                                    <div className="w-[47px] h-[47px] bg-[#0D3FC6] rounded-full flex items-center justify-center">
                                        <img src={PhoneIcon} alt="Phone" />
                                    </div>
                                    <div>
                                        <h6 className="text-[16px] sm:text-[20px] font-semibold text-[#05073C]">Call Us Directly</h6>
                                        <p className="text-[14px] sm:text-[16px] font-normal text-[#6B7280] mb-0">+8801992222450                                        </p>
                                    </div>
                                </div>
                                <div className="w-full flex gap-[13px] items-center bg-[#F4F4F6] rounded-[15px] py-[10px] sm:py-[17px] px-[15px] sm:px-[24px]">
                                    <div className="w-[47px] h-[47px] bg-[#25D366] rounded-full flex items-center justify-center">
                                        <img src={WhatsappIcon} alt="Whatsapp" />
                                    </div>
                                    <div>
                                        <h6 className="text-[16px] sm:text-[20px] font-semibold text-[#05073C]">Whatsapp</h6>
                                        <p className="text-[14px] sm:text-[16px] font-normal text-[#6B7280] mb-0">+8801992222450</p>
                                    </div>
                                </div>
                            </div>

                            <form onSubmit={handleSubmit(onSubmit)}>
                                <div className="flex flex-col sm:flex-row items-center gap-[10px]">
                                    <div className="mb-[20px] sm:mb-[35px] relative w-full">
                                        <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">First Name</label>
                                        <div className="relative">
                                            <img src={userIcon} alt="user" className='w-[30px] h-[18px] absolute sm:left-[20px] left-[10px] top-1/2 transform -translate-y-1/2' />
                                            <input
                                                type="text"
                                                {...register('first_name', {
                                                    required: 'First name is required',
                                                    minLength: {
                                                        value: 3,
                                                        message: 'First name must be at least 3 characters'
                                                    },
                                                    maxLength: {
                                                        value: 50,
                                                        message: 'First name must not exceed 50 characters'
                                                    },
                                                    pattern: {
                                                        value: /^[A-Za-z]+(?: [A-Za-z]+)*$/,
                                                        message: 'First name can only contain letters and single spaces (no leading/trailing/multiple spaces)'
                                                    },
                                                    validate: value => value.trim() === value || "No leading or trailing spaces"
                                                })}
                                                className={`w-full py-[17px] px-[18px] sm:ps-[60px] ps-[45px] text-[14px] font-semibold text-[#000000] leading-[18px] border ${errors.first_name ? 'border-red-500' : 'border-[#0D9BC6]'} focus:outline-none placeholder:text-[#00000080] rounded-[8px]`}
                                                placeholder="Enter your first name"
                                            />
                                        </div>
                                        <span className="text-red-500 text-[11px] mt-1 block min-h-[16px]">
                                            {errors.first_name?.message || "\u00A0"}
                                        </span>
                                    </div>

                                    <div className="mb-[20px] sm:mb-[35px] relative w-full">
                                        <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">Last Name</label>
                                        <div className="relative">
                                            <img src={userIcon} alt="user" className='w-[30px] h-[18px] absolute sm:left-[20px] left-[10px] top-1/2 transform -translate-y-1/2' />
                                            <input
                                                type="text"
                                                {...register('last_name', {
                                                    required: 'Last name is required',
                                                    minLength: {
                                                        value: 3,
                                                        message: 'Last name must be at least 3 characters'
                                                    },
                                                    maxLength: {
                                                        value: 50,
                                                        message: 'Last name must not exceed 50 characters'
                                                    },
                                                    pattern: {
                                                        value: /^[A-Za-z]+(?: [A-Za-z]+)*$/,
                                                        message: 'Last name can only contain letters and single spaces (no leading/trailing/multiple spaces)'
                                                    },
                                                    validate: value => value.trim() === value || "No leading or trailing spaces"
                                                })}
                                                className={`w-full py-[17px] px-[18px] sm:ps-[60px] ps-[45px] text-[14px] font-semibold text-[#000000] leading-[18px] border ${errors.last_name ? 'border-red-500' : 'border-[#0D9BC6]'} focus:outline-none placeholder:text-[#00000080] rounded-[8px]`}
                                                placeholder="Enter your last name"
                                            />
                                        </div>
                                        <span className="text-red-500 text-[11px] mt-1 block min-h-[16px]">
                                            {errors.last_name?.message || "\u00A0"}
                                        </span>
                                    </div>
                                </div>

                                <div className="flex flex-col sm:flex-row items-center gap-[10px]">
                                    <div className="mb-[20px] sm:mb-[35px] relative w-full">
                                        <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">Email</label>
                                        <div className="relative">
                                            <img src={emailIcon} alt="user" className='w-[30px] h-[20px] absolute sm:left-[20px] left-[10px] top-1/2 transform -translate-y-1/2' />
                                            <input
                                                type="email"
                                                {...register('email', {
                                                    required: 'Email is required',
                                                    pattern: {
                                                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                                                        message: 'Please enter a valid email address'
                                                    }
                                                })}
                                                className={`w-full py-[17px] px-[18px] sm:ps-[60px] ps-[45px] text-[14px] font-semibold text-[#000000] leading-[18px] border ${errors.email ? 'border-red-500' : 'border-[#0D9BC6]'} focus:outline-none placeholder:text-[#00000080] rounded-[8px]`}
                                                placeholder="Enter your email"
                                            />
                                        </div>
                                        <span className="text-red-500 text-[11px] mt-1 block min-h-[16px]">
                                            {errors.email?.message || "\u00A0"}
                                        </span>
                                    </div>

                                    <div className="mb-[20px] sm:mb-[35px] relative w-full">
                                        <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">Phone Number</label>
                                        <div className="relative">
                                            <img src={numberIcon} alt="user" className='w-[30px] h-[18px] absolute sm:left-[20px] left-[10px] top-1/2 transform -translate-y-1/2' />
                                            <input
                                                type="tel"
                                                {...register('phone', {
                                                    required: 'Phone number is required',
                                                    pattern: {
                                                        value: /^(\+?88)?01[3-9]\d{8}$/,
                                                        message: "Enter a valid Bangladeshi phone number (e.g. +8801XXXXXXXXX or 01XXXXXXXXX)"
                                                    }
                                                })}
                                                className={`w-full py-[17px] px-[18px] sm:ps-[60px] ps-[45px] text-[14px] font-semibold text-[#000000] leading-[18px] border ${errors.phone ? 'border-red-500' : 'border-[#0D9BC6]'} focus:outline-none placeholder:text-[#00000080] rounded-[8px]`}
                                                placeholder="Enter your phone number"
                                            />
                                        </div>
                                        <span className="text-red-500 text-[11px] mt-1 block min-h-[16px]">
                                            {errors.phone?.message || "\u00A0"}
                                        </span>
                                    </div>
                                </div>

                                <div className="mb-[20px] sm:mb-[35px] relative w-full">
                                    <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">Subject</label>
                                    <div className="relative">
                                        <img src="/subject.svg" alt="user" className='w-[30px] h-[18px] absolute sm:left-[20px] left-[10px] top-1/2 transform -translate-y-1/2' />
                                        <input
                                            type="text"
                                            {...register('subject', {
                                                required: 'Subject is required',
                                                minLength: {
                                                    value: 5,
                                                    message: 'Subject must be at least 5 characters'
                                                },
                                                pattern: {
                                                    value: /^(?!.* {2})(?! )[A-Za-z0-9\s.,!?'"-]+(?<! )$/,
                                                    message: 'No leading/trailing/multiple spaces'
                                                },
                                                validate: value => value.trim() === value || "No leading or trailing spaces"
                                            })}
                                            className={`w-full py-[17px] px-[18px] sm:ps-[60px] ps-[45px] text-[14px] font-semibold text-[#000000] leading-[18px] border ${errors.subject ? 'border-red-500' : 'border-[#0D9BC6]'} focus:outline-none placeholder:text-[#00000080] rounded-[8px]`}
                                            placeholder="Type your subject"
                                        />
                                    </div>
                                    <span className="text-red-500 text-[11px] mt-1 block min-h-[16px]">
                                        {errors.subject?.message || "\u00A0"}
                                    </span>
                                </div>

                                <div className="mb-[20px] sm:mb-[35px] relative w-full">
                                    <label className="block text-[10px] font-bold text-[#0D9BC6] mb-1 absolute top-[-7px] left-[20px] bg-white px-[7px] pe-[15px] z-10">Message</label>
                                    <div className="relative">
                                        <textarea
                                            {...register('message', {
                                                required: 'Message is required',
                                                minLength: {
                                                    value: 10,
                                                    message: 'Message must be at least 10 characters'
                                                },
                                                pattern: {
                                                    value: /^(?!.* {2})(?! )[A-Za-z0-9\s.,!?'"-]+(?<! )$/,
                                                    message: 'No leading/trailing/multiple spaces'
                                                },
                                                validate: value => value.trim() === value || "No leading or trailing spaces"
                                            })}
                                            className={`w-full h-[160px] resize-none py-[17px] px-[18px] sm:ps-[10px] ps-[20px] text-[14px] font-semibold text-[#000000] leading-[18px] border ${errors.message ? 'border-red-500' : 'border-[#0D9BC6]'} focus:outline-none placeholder:text-[#00000080] rounded-[8px]`}
                                            placeholder="Enter your message"
                                        />
                                    </div>
                                    <span className="text-red-500 text-[11px] mt-1 block min-h-[16px]">
                                        {errors.message?.message || "\u00A0"}
                                    </span>
                                </div>

                                <div className="text-center mb-[12px] pt-[10px]">
                                    <button
                                        type="submit"
                                        disabled={isLoading}
                                        className="bg-gradient-to-r from-[#0D3FC6] to-[#3793FF] text-white py-[16px] px-[45px] rounded-[8px] font-medium transition-colors cursor-pointer !rounded-button whitespace-nowrap text-[14px] leading-[18px] uppercase disabled:opacity-50"
                                    >
                                        {isLoading ? <Loader /> : "SEND MESSAGE"}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ContactUsModal;
