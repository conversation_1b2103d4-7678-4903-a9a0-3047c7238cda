interface Payments {
     id: number;
     user: {
          email: string;
          username: string;
     };
     booking_amount: number;
     booking_date: string;
     booking_status: string;
}
const PaymentsTable = ({ payments }: { payments: Payments[] }) => {
     return (
          <>
               <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                         <tr>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                   User
                              </th>

                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                   Amount
                              </th>

                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                   Date
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                                   Status
                              </th>
                         </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                         {payments?.map((pkg: Payments) => (
                              <tr key={pkg.id} className="hover:bg-gray-50 transition-colors">
                                   <td className="px-4 sm:px-6 py-4">
                                        <div className="flex flex-col">
                                             <span className="text-sm font-medium text-gray-900 break-words">{pkg?.user?.email}</span>
                                             <span className="text-xs sm:text-sm text-gray-500 mt-1">{pkg?.user?.username}</span>
                                             {/* Mobile-only location */}
                                             <span className="text-xs text-gray-500 mt-1 sm:hidden">{pkg?.user?.email}</span>
                                        </div>
                                   </td>
                                   <td className="px-4 sm:px-6 py-4 text-sm text-gray-500 hidden sm:table-cell">
                                        ৳ {pkg?.booking_amount}
                                   </td>
                                   <td className="px-4 sm:px-6 py-4">
                                        <span className="px-4 sm:px-6 py-4 text-sm text-gray-500 hidden sm:table-cell">
                                             {pkg?.booking_date?.split('T')[0]}
                                        </span>
                                   </td>
                                   <td className="px-4 sm:px-6 py-4">
                                        <span className="inline-flex items-center px-2 sm:px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                             {pkg?.booking_status}
                                        </span>
                                   </td>
                              </tr>
                         ))}
                    </tbody>
               </table>
          </>
     )
}

export default PaymentsTable;