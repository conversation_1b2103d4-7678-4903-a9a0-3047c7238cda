import { useNavigate, useParams } from 'react-router-dom';
import PaymentSuccessIcon from "../../assets/green-check.svg";

const PaymentSuccess = () => {
     const { orderId } = useParams();
     const navigate = useNavigate();

     const handleReturnHome = () => {
          navigate('/');
     };

     const handleViewBookings = () => {
          navigate('/bookings');
     };

     return (
          <div className="flex">
               <div className="md:ms-[275px] h-screen overflow-y-auto flex-1">
                    <div className="py-[15px] sm:py-[14px] mx-auto sm:px-[30px] px-[10px] border-b border-[#E5E7EB] shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)]">
                         <h1 className="text-center lg:text-start text-[20px] sm:text-[24px] leading-[32px] font-bold text-[#05073C] max-w-[1240px] mx-auto">
                              Payment Successful
                         </h1>
                    </div>

                    <div className="max-w-[740px] lg:max-w-[1240px] mx-auto lg:px-[30px] sm:px-[15px] px-[8px] py-[30px]">
                         <div className="bg-white rounded-[10px] shadow-md p-[20px] sm:p-[30px] max-w-[600px] mx-auto">
                              <div className="text-center mb-[30px]">
                                   <div className="w-[60px] h-[60px] sm:w-[90px] sm:h-[90px] rounded-full bg-[#DCFCE7] mx-auto mb-[20px] flex items-center justify-center flex-shrink-0">
                                        <img src={PaymentSuccessIcon} alt="Payment Success" className="mx-auto sm:w-auto w-[30px]" />
                                   </div>
                                   <h3 className="mb-[10px] text-[24px] sm:text-[28px] leading-[28px] sm:leading-[34px] font-semibold text-[#05073C]">Thank You!</h3>
                                   <p className="text-[16px] text-[#636A7E]">Your payment has been successfully processed</p>
                              </div>

                              <div className="bg-[#E7ECF9] rounded-[10px] p-[15px] sm:p-[20px] mb-[30px]">
                                   <h4 className="text-[16px] sm:text-[20px] font-semibold text-[#05073C] mb-[15px]">Payment Details</h4>
                                   <div className="flex justify-between items-center gap-[10px] mb-[14px]">
                                        <p className="text-[14px] text-[#05073C]">Order ID:</p>
                                        <p className="text-[14px] text-[#05073C] text-right">{orderId || 'N/A'}</p>
                                   </div>
                                   <div className="flex justify-between items-center gap-[10px] mb-[14px]">
                                        <p className="text-[14px] text-[#05073C]">Date:</p>
                                        <p className="text-[14px] text-[#05073C] text-right">{new Date().toLocaleDateString()}</p>
                                   </div>
                                   <div className="flex justify-between items-center gap-[10px] mb-[14px]">
                                        <p className="text-[14px] text-[#05073C]">Payment Status:</p>
                                        <p className="text-[14px] text-[#22C55E] font-semibold text-right">Completed</p>
                                   </div>
                              </div>

                              <div className="mb-[30px]">
                                   <h5 className="text-[16px] font-semibold text-[#05073C] mb-[15px]">What Happens Next?</h5>
                                   <ul className="list-disc text-[14px] text-[#05073C] ps-[18px]">
                                        <li className="text-[14px] leading-[20px] mb-[10px]">
                                             <span className="font-semibold">Email Confirmation:</span> You'll receive a detailed receipt in your email shortly.
                                        </li>
                                        <li className="text-[14px] leading-[20px] mb-[10px]">
                                             <span className="font-semibold">Booking Confirmation:</span> Your travel booking details have been confirmed.
                                        </li>
                                        <li className="text-[14px] leading-[20px] mb-[10px]">
                                             <span className="font-semibold">Customer Support:</span> Our team is available if you have any questions about your booking.
                                        </li>
                                   </ul>
                              </div>

                              <div className="text-center mt-[30px] flex flex-col sm:flex-row gap-[15px]">
                                   <button
                                        onClick={handleViewBookings}
                                        className="w-full bg-[#E7ECF9] text-[#0D3FC6] py-[14px] rounded-[8px] hover:bg-[#DCE4F9] transition-colors cursor-pointer text-[14px] leading-[18px] font-semibold"
                                   >
                                        View My Bookings
                                   </button>
                                   <button
                                        onClick={handleReturnHome}
                                        className="w-full bg-[#0D3FC6] text-white py-[14px] rounded-[8px] hover:bg-[#1A339B] transition-colors cursor-pointer text-[14px] leading-[18px] font-semibold"
                                   >
                                        Return to Home
                                   </button>
                              </div>
                         </div>
                    </div>
               </div>
          </div>
     );
};

export default PaymentSuccess;