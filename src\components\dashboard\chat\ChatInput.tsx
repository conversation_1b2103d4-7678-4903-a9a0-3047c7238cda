import SendIcon from "../../../assets/inputSend-icon.svg";
import { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { addMessage, clearTourDetails, threadUid as setThreadUid, setLoading, setTourDetails } from "../../../store/features/chat/chatSlice";
import { RootState } from "../../../store/store";
import SignInModal from "./SignInModal";
import SignUpModal from "./SignUpModal";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const ChatInput = ({ sendMessage, isSendingMessage }: { sendMessage: any, isSendingMessage: boolean }) => {
  const [inputValue, setInputValue] = useState("");
  const dispatch = useDispatch();
  const threadUid = useSelector((state: RootState) => state.chat.threadUid);
  const [showSignInModal, setShowSignInModal] = useState(false);
  const [showSignUpModal, setShowSignUpModal] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (threadUid) {
      dispatch(setThreadUid(threadUid));
    }
  }, [threadUid, dispatch]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleSendMessage = async () => {
    if (isSendingMessage) {
      return;
    }

    if (inputValue.trim()) {
      const messagePayload: {
        message: string;
        thread_uid?: string;
      } = {
        message: inputValue
      };

      if (threadUid) {
        messagePayload.thread_uid = threadUid;
      }

      dispatch(addMessage({
        content: inputValue,
        sender: "user"
      }));

      setInputValue("");
      dispatch(clearTourDetails());

      dispatch(setLoading(true));
      try {
        const response = await sendMessage(messagePayload).unwrap();
        const responseThreadUid = response?.data?.thread_uid;
        dispatch(setThreadUid(responseThreadUid));

        window.history.pushState(
          { threadUid: responseThreadUid },
          '',
          `/chat/${responseThreadUid}`
        );

        if (response.data?.ai_response) {
          const aiResponse = response.data.ai_response;
          let content = "";
          let tourDetails = null;
          let responseType = "text";

          responseType = aiResponse?.type || "text";
          if (aiResponse?.response?.message) {
            content = aiResponse.response.message;
          } else if (aiResponse?.text_response?.message) {
            content = aiResponse.text_response.message;
          } else if (aiResponse?.data?.text_response?.message) {
            content = aiResponse.data.text_response.message;
          } else if (typeof aiResponse?.response === "string") {
            content = aiResponse.response;
          } else if (aiResponse?.type === "general") {
            content = aiResponse.response.message;
          }

          if (responseType === "tour_packages" && aiResponse?.data) {
            tourDetails = aiResponse.data;
          } else if (responseType === "general") {
            tourDetails = aiResponse.data;
          } else if (responseType === "popular" && aiResponse?.data) {
            tourDetails = aiResponse.data;
          } else if (responseType === "json" && Array.isArray(aiResponse?.data)) {
            tourDetails = aiResponse.data;
          } else if (responseType === "booking_confirmed") {
            tourDetails = aiResponse.data;
          } else {
            tourDetails = aiResponse?.data?.tour_details ||
              aiResponse?.tour_details ||
              (aiResponse?.data && Array.isArray(aiResponse.data) ? aiResponse.data : null);
          }

          if (!content || content.trim() === "") {
            content = "I'm here to help with your travel plans. How can I assist you today?";
          }

          if (tourDetails && Array.isArray(tourDetails) && tourDetails.length > 0) {
            dispatch(setTourDetails(tourDetails));
          }

          if (responseType === "popular" && aiResponse?.data) {
            dispatch(addMessage({
              content,
              sender: "ai",
              responseType: responseType,
              tourDetails,
              popularDestinations: aiResponse.data
            }));
          } else if (responseType === "tour_packages") {
            dispatch(addMessage({
              content,
              sender: "ai",
              responseType: responseType,
              tourDetails,
              tourPackages: aiResponse.data,
              meta_details: aiResponse.meta_details
            }));
          } else if (responseType === "general") {
            dispatch(addMessage({
              content,
              sender: "ai",
              responseType: responseType,
              tourDetails,
              meta_details: aiResponse.meta_details,
              tourPackages: aiResponse.data
            }));
          } else if (responseType === "booking_confirmed") {
            dispatch(addMessage({
              content,
              sender: "ai",
              responseType: responseType,
              extracontent: aiResponse?.response?.extra_context,
              tourDetails
            }));
          }
        } else {
          dispatch(addMessage({
            content: "I'm here to help with your travel plans. How can I assist you today?",
            sender: "ai"
          }));
        }
      } catch (error) {
        console.error("Error sending message:", error);

        dispatch(addMessage({
          content: "Sorry, I couldn't process your request at the moment. Please try again later.",
          sender: "ai"
        }));
      } finally {
        dispatch(setLoading(false));
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  const handleSignInClick = () => {
    if (threadUid) {
      localStorage.setItem('threadUid', threadUid);
    }
    setShowSignUpModal(false);
    setShowSignInModal(true);
  };

  const handleSignUpClick = () => {
    setShowSignInModal(false);
    setShowSignUpModal(true);
  };

  return (
    <>
      <div className="max-w-4xl mx-auto px-[10px] sm:px-8 sm:mb-[10px]">
        <div className="flex bg-white p-[12px] py-[10px] border border-[#E5E7EB] shadow-[0px_4px_16px_0px_rgba(0,0,0,0.2)]
         rounded-[14px]">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Find me travel packages"
            className="w-full pr-[10px] py-0 pl-[8px] border-none text-[#05073C] placeholder:text-[#989EAE] focus:outline-none text-[16px] leading-[24px] font-semibold"
          />
          <button
            onClick={handleSendMessage}
            disabled={isSendingMessage}
            className="h-[42px] w-[42px] text-white rounded-[10px] flex items-center justify-center cursor-pointer bg-[#E7ECF9]"
          >
            <img src={SendIcon} alt="Send" className="" />
          </button>
        </div>
      </div>
      {showSignInModal && <SignInModal onClose={() => setShowSignInModal(false)} onSignUpClick={handleSignUpClick} />}
      {showSignUpModal && <SignUpModal onClose={() => setShowSignUpModal(false)} onSignInClick={handleSignInClick} />}
    </>
  );
};

export default ChatInput;