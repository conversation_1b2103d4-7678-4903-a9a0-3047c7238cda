import { useState, useMemo, useEffect } from "react";
import Pagination from "../../components/admin/ClicksPagination";
import AdminPackagesSkeleton from "../../components/skeletons/AdminPackagesSkeleton";
import BookingsTable from "../../components/admin/BookingsTable";
import PaymentsTable from "../../components/admin/PaymentsTable";
import { useGetAdminPaymentsQuery } from "../../store/features/bookings/bookingsApi";
import { useUpdateBookingStatusMutation, useGetAdminBookingsQuery } from "../../store/features/admin/adminApi";
import { toast } from "react-hot-toast";
const ITEMS_PER_PAGE = 10;

const AdminBookings = () => {
     const { data: bookingsData, isLoading: bookingsLoading } = useGetAdminBookingsQuery({});
     const { data: paymentsData, isLoading: paymentsLoading } = useGetAdminPaymentsQuery({});
     const [searchQuery, setSearchQuery] = useState("");
     const [updateBookingStatus] = useUpdateBookingStatusMutation();
     const [statusFilter, setStatusFilter] = useState('all');

     const payments = paymentsData?.payments;
     const bookings = bookingsData?.bookings;
     const [activeTab, setActiveTab] = useState<'bookings' | 'payments'>('bookings');
     const [currentPage, setCurrentPage] = useState(1);

     // Filter data based on search query
     const filteredData = useMemo(() => {
          const data = activeTab === 'bookings' ? bookings : payments;
          if (!data) return [];

          let filtered = data;

          // Apply status filter first
          if (activeTab === 'bookings' && statusFilter !== 'all') {
               filtered = filtered.filter((item: {
                    booking_status: string;
               }) =>
                    item.booking_status.toLowerCase() === statusFilter.toLowerCase()
               );
          }

          // Then apply search filter
          if (searchQuery.trim()) {
               return filtered.filter((item: {
                    tour: {
                         tour_title: string;
                         tour_location: string;
                    };
                    order: {
                         order_id: string;
                    };
                    booking_status: string;
                    user: {
                         email: string;
                         username: string;
                    };
               }) => {
                    if (activeTab === 'bookings') {
                         return (
                              item?.tour?.tour_title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                              item?.tour?.tour_location?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                              item?.order?.order_id?.toString().includes(searchQuery) ||
                              item?.booking_status?.toLowerCase().includes(searchQuery.toLowerCase())
                         );
                    } else {
                         return (
                              item?.user?.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                              item?.user?.username?.toLowerCase().includes(searchQuery.toLowerCase())
                         );
                    }
               });
          }

          return filtered;
     }, [activeTab, bookings, payments, searchQuery, statusFilter]);

     const totalPages = Math.ceil((filteredData?.length || 0) / ITEMS_PER_PAGE);
     const paginatedData = filteredData?.slice(
          (currentPage - 1) * ITEMS_PER_PAGE,
          currentPage * ITEMS_PER_PAGE
     );

     // Reset page when search changes
     useEffect(() => {
          setCurrentPage(1);
          setStatusFilter('all');
     }, [activeTab]);

     const handlePageChange = (page: number) => {
          setCurrentPage(page);
          window.scrollTo({ top: 0, behavior: 'smooth' });
     };

     const handleStatusChange = async (bookingId: string, newStatus: string) => {
          try {
               await updateBookingStatus({
                    order_id: bookingId,
                    status: newStatus
               }).unwrap();

               toast.success('Booking status updated successfully');
          } catch (error) {
               console.error('Failed to update status:', error);
               toast.error('Failed to update booking status');
          }
     };

     return (
          < >
               <div className="mb-6">
                    {/* Mobile Dropdown */}
                    <div className="sm:hidden">
                         <select
                              className="block w-full rounded-lg border-gray-300 py-2 pl-3 pr-10 text-base focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                              value={activeTab}
                              onChange={(e) => {
                                   setActiveTab(e.target.value as 'bookings' | 'payments');
                                   setCurrentPage(1);
                              }}
                         >
                              <option value="bookings">Bookings</option>
                              <option value="payments">Payments</option>
                         </select>
                    </div>

                    {/* Desktop Tabs */}
                    <div className="hidden sm:block">
                         <div className="border-b border-gray-200">
                              <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                                   {['bookings', 'payments'].map((tab) => (
                                        <button
                                             key={tab}
                                             onClick={() => {
                                                  setActiveTab(tab as 'bookings' | 'payments');
                                                  setCurrentPage(1);
                                             }}
                                             className={`${activeTab === tab
                                                  ? 'border-blue-500 text-blue-600'
                                                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                                  } whitespace-nowrap pb-3 px-1 border-b-2 font-medium text-sm cursor-pointer`}
                                        >
                                             {tab === 'bookings' ? 'Bookings' : 'Payments'}
                                             <span className={`ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium ${activeTab === tab ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                                                  }`}>
                                                  {tab === 'bookings' ? bookings?.length : payments?.length}
                                             </span>
                                        </button>
                                   ))}
                              </nav>
                         </div>
                    </div>
               </div>

               <div className="mb-6 flex flex-col sm:flex-row gap-4 sm:items-center">
                    {/* Search Input */}
                    <div className="relative w-full sm:w-64">
                         <input
                              type="text"
                              placeholder={`Search ${activeTab === 'bookings' ? 'bookings' : 'payments'}...`}
                              value={searchQuery}
                              onChange={(e) => setSearchQuery(e.target.value)}
                              className="w-full px-4 py-2 pl-10 pr-4 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                         />
                         <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                              </svg>
                         </div>
                         {searchQuery && (
                              <button
                                   onClick={() => setSearchQuery("")}
                                   className="absolute inset-y-0 right-0 pr-3 flex items-center"
                              >
                                   <svg className="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                   </svg>
                              </button>
                         )}
                    </div>

                    {/* Status Filter - Only show for bookings tab */}
                    {activeTab === 'bookings' && (
                         <div className="w-full sm:w-48">
                              <select
                                   value={statusFilter}
                                   onChange={(e) => {
                                        setStatusFilter(e.target.value);
                                        setCurrentPage(1);
                                   }}
                                   className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              >
                                   <option value="all">All Statuses</option>
                                   <option value="Interested">Interested</option>
                                   <option value="initiated">Initiated</option>
                                   <option value="processing">Processing</option>
                                   <option value="on-hold">On hold</option>
                                   <option value="completed">Completed</option>
                                   <option value="cancelled">Cancelled</option>
                                   <option value="failed">Failed</option>
                                   <option value="payment_pending">Payment pending</option>
                              </select>
                         </div>
                    )}

                    {/* Filter Stats */}
                    <div className="text-sm text-gray-500">
                         {filteredData?.length} {filteredData?.length === 1 ? 'result' : 'results'} found
                    </div>
               </div>

               <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div className="overflow-x-auto">
                         {bookingsLoading || paymentsLoading ? (
                              <AdminPackagesSkeleton />
                         ) : (
                              <>
                                   {filteredData?.length === 0 ? (
                                        <div className="text-center py-12">
                                             <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                             </svg>
                                             <h3 className="mt-2 text-sm font-medium text-gray-900">No results found</h3>
                                             <p className="mt-1 text-sm text-gray-500">
                                                  No {activeTab} match your search criteria.
                                             </p>
                                        </div>
                                   ) : (
                                        activeTab === 'bookings' ? (
                                             <BookingsTable bookings={paginatedData} onStatusChange={handleStatusChange} />
                                        ) : (
                                             <PaymentsTable payments={paginatedData} />
                                        )
                                   )}
                              </>
                         )}
                    </div>

                    {/* Pagination */}
                    {totalPages > 1 && (
                         <Pagination
                              currentPage={currentPage}
                              totalPages={totalPages}
                              onPageChange={handlePageChange}
                         />
                    )}
               </div>
          </>
     );
};

export default AdminBookings;